import * as fs from 'fs/promises'
import * as path from 'path'

export async function ensureTestProject(projectName: string, projectPath: string) {
  try {
    await fs.mkdir(projectPath, { recursive: true })
    console.log(`Created test project directory: ${projectPath}`)
  } catch (error) {
    console.log(`Test project directory already exists: ${projectPath}`)
  }
}

export async function deleteTestProject(projectPath: string) {
  try {
    await fs.rm(projectPath, { recursive: true, force: true })
    console.log(`Deleted test project directory: ${projectPath}`)
  } catch (error) {
    console.log(`Could not delete test project directory: ${projectPath}`)
  }
}

export async function createSampleImages(projectPath: string, count: number = 5) {
  // Copy sample images from the existing files directory
  const sourceDir = 'C:\\claude\\dl-organizer\\files'
  
  try {
    // Get existing image files
    const files = await fs.readdir(sourceDir)
    const imageFiles = files.filter(f => /\.(jpg|jpeg|png|gif)$/i.test(f)).slice(0, count)
    
    // Copy them to test directory
    for (let i = 0; i < Math.min(imageFiles.length, count); i++) {
      const sourceFile = path.join(sourceDir, imageFiles[i])
      const destFile = path.join(projectPath, `test-image-${i + 1}.jpg`)
      
      await fs.copyFile(sourceFile, destFile)
      console.log(`Copied sample image: ${destFile}`)
    }
    
    // If we need more images, create simple placeholder files
    if (imageFiles.length < count) {
      console.log(`Only found ${imageFiles.length} sample images, needed ${count}`)
    }
  } catch (error) {
    console.error('Error creating sample images:', error)
    throw error
  }
}

export async function waitForElement(page: any, selector: string, timeout: number = 10000) {
  await page.waitForSelector(selector, { timeout })
}