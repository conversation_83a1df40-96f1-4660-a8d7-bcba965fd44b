# Changelog

All notable changes to the DL Organizer project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.3.0] - 2025-01-24

### Added
- **Smart Filter Analyzer**: AI-powered folder content analysis system
  - Automatic license side detection (front/back/selfie/unknown) using OCR technology
  - Pattern recognition for filename clusters, size distributions, and resolution buckets
  - One-click filter chip generation based on discovered patterns
  - Real-time analysis progress with Server-Sent Events (SSE)
  - Smart caching system for instant subsequent folder scans
  - Seamless integration with existing power-filter workbench
- **New Filter Types**: 
  - `side` filter for license side classification
  - `size-range` filter for size bucket filtering
  - `resolution-range` filter for resolution bucket filtering
- **Backend Infrastructure**:
  - Smart analyzer service with job queue management
  - License side detection utility using OpenRouter OCR
  - Analysis manifest builder with statistical aggregation
  - New API endpoints: `/api/smart-analyzer/analyze`, `/api/smart-analyzer/status/:jobId`, `/api/smart-analyzer/cancel/:jobId`
- **Frontend Components**:
  - `useSmartAnalyzer` hook for analysis state management
  - `SmartChips` component with interactive filter chips
  - Progress tracking and error handling UI
  - Integration with enhanced image grid

### Changed
- Extended filter type system to support new smart filter types
- Updated image filtering utility to handle range-based filters
- Enhanced type definitions for comprehensive filter support

### Technical Details
- Added real-time SSE streaming for analysis progress
- Implemented in-memory job queue for analysis processing
- Enhanced OCR service integration for side detection
- Type-safe filter system with discriminated unions
- Optimized caching strategy for analysis results

## [1.2.0] - 2025-01-23

### Added
- **Power-Filter Workbench**: Professional filtering system with stackable filters
- **Smart Selection Controls**: Select All Visible/Folder with additive accumulation
- **Advanced Pagination**: Configurable page sizes (50-500) with Show All toggle
- **Real-time Filter Management**: Visual filter chips with statistics display
- **Performance Optimization**: Sub-100ms filtering with intelligent caching

## [1.1.0] - 2025-01-22

### Added
- **AI Model Validation System**: Real-time model availability checking
- **Enhanced OCR Testing Playground**: Multi-model comparison and testing
- **Smart Model Management**: Automatic model replacement suggestions
- **Performance Metrics**: Cost analysis and response time monitoring

## [1.0.0] - 2025-01-21

### Added
- Initial release of DL Organizer
- Multi-provider OCR support (OpenAI, OpenRouter)
- Windows filesystem integration
- Dual region support (US/Australian)
- ReadySearch integration for Australian licenses
- Modern React/Next.js interface
- Production-ready architecture with health monitoring