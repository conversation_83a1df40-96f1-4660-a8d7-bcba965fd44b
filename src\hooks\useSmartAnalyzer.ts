import { useState, useEffect, useRef, useCallback } from 'react';
import { safeStringify, safeConsoleLog } from '@/utils/safe-json';

export interface SmartSelectedImage {
  imageId: string;
  filename: string;
  side: 'front' | 'back' | 'selfie' | 'unknown';
  baseId: string;
  qualityScore: number;
  isExpanded: boolean;
  duplicatesInGroup: number;
}

export interface SmartSelection {
  totalGroups: number;
  selectedImages: SmartSelectedImage[];
  duplicatesFound: number;
  potentialSavings: number; // Percentage of images that can be skipped
}

export interface AnalyzerManifest {
  sideCounts: Record<'front' | 'back' | 'selfie' | 'unknown', number>;
  filenameClusters: Array<{ token: string; count: number }>;
  sizeBuckets: Array<{ label: string; count: number }>;
  resBuckets: Array<{ label: string; count: number }>;
  cachedPct: number;
  totalImages: number;
  imageIds: string[];
  imageSides: Record<string, 'front' | 'back' | 'selfie' | 'unknown'>;
  smartSelection: SmartSelection;
  generatedAt: string;
  version: string;
}

export interface AnalysisProgress {
  jobId: string;
  progress: number;
  processedImages: number;
  totalImages: number;
  currentImage?: {
    id: string;
    filename: string;
  };
}

export interface AnalysisResult {
  jobId: string;
  status: 'started' | 'processing' | 'completed' | 'failed' | 'cancelled';
  done?: boolean;
  manifest?: AnalyzerManifest;
  error?: string;
  processingTime?: number;
  progress?: number;
  processedImages?: number;
  totalImages?: number;
  currentImage?: {
    id: string;
    filename: string;
  };
}

export const useSmartAnalyzer = (folderPath?: string) => {
  const [manifest, setManifest] = useState<AnalyzerManifest | null>(null);
  const [progress, setProgress] = useState<number>(0);
  const [running, setRunning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentJobId, setCurrentJobId] = useState<string | null>(null);
  const [processingTime, setProcessingTime] = useState<number | null>(null);

  const eventSourceRef = useRef<EventSource | null>(null);
  const startTimeRef = useRef<number | null>(null);

  const cleanup = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
  }, []);

  const start = useCallback(async (forceRefresh = false) => {
    if (!folderPath || running) {
      console.warn('Cannot start analysis: missing folderPath or already running');
      return;
    }

    setRunning(true);
    setError(null);
    setManifest(null);
    setProgress(0);
    setCurrentJobId(null);
    setProcessingTime(null);
    startTimeRef.current = Date.now();

    // Clean up any existing connection
    cleanup();

    try {
      console.log('🔍 Smart Analyzer: Starting analysis for folder:', folderPath);

      // Start the analysis job via POST - Use safe serialization
      const response = await fetch('/api/smart-analyzer/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: safeStringify({
          folderPath,
          forceRefresh
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      // Get job ID from response
      const { jobId, totalImages } = await response.json();
      setCurrentJobId(jobId);
      
      console.log(`🚀 Smart Analyzer: Job ${jobId} started with ${totalImages} images`);

      // Set up SSE connection for progress updates
      const eventSource = new EventSource(`/api/smart-analyzer/stream/${jobId}`);
      eventSourceRef.current = eventSource;

      eventSource.onopen = () => {
        console.log('✅ Smart Analyzer: SSE connection opened for job', jobId);
      };

      eventSource.onmessage = (event) => {
        try {
          const data: AnalysisResult = JSON.parse(event.data);
          safeConsoleLog('📊 Smart Analyzer: Received event:', data);

          if (typeof data.progress === 'number') {
            setProgress(data.progress);
          }

          if (data.done) {
            const endTime = Date.now();
            const duration = startTimeRef.current ? endTime - startTimeRef.current : null;
            setProcessingTime(duration);

            if (data.status === 'completed' && data.manifest) {
              console.log('✅ Smart Analyzer: Analysis completed successfully');
              safeConsoleLog('📊 Smart Analyzer: Manifest:', data.manifest);
              setManifest(data.manifest);
              setProgress(100);
            } else if (data.status === 'failed') {
              console.error('❌ Smart Analyzer: Analysis failed:', data.error);
              setError(data.error || 'Analysis failed');
            } else if (data.status === 'cancelled') {
              console.log('⏹️ Smart Analyzer: Analysis cancelled');
              setError('Analysis was cancelled');
            }

            // Clean up and close connection
            cleanup();
            setRunning(false);
          }
        } catch (parseError) {
          console.error('❌ Smart Analyzer: Failed to parse SSE data:', parseError);
          setError('Failed to parse server response');
          cleanup();
          setRunning(false);
        }
      };

      eventSource.onerror = (err) => {
        console.error('❌ Smart Analyzer: SSE error:', err);
        setError('Connection error occurred');
        cleanup();
        setRunning(false);
      };

    } catch (fetchError) {
      console.error('❌ Smart Analyzer: Failed to start analysis:', fetchError);
      setError(fetchError instanceof Error ? fetchError.message : 'Failed to start analysis');
      setRunning(false);
    }
  }, [folderPath, running, cleanup]);

  const cancel = useCallback(async () => {
    if (!currentJobId) {
      console.warn('No job to cancel');
      return;
    }

    try {
      console.log(`⏹️ Smart Analyzer: Cancelling job ${currentJobId}`);
      
      const response = await fetch(`/api/smart-analyzer/cancel/${currentJobId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error(`Failed to cancel job: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('✅ Smart Analyzer: Job cancelled successfully:', result);

    } catch (cancelError) {
      console.error('❌ Smart Analyzer: Failed to cancel job:', cancelError);
      setError(cancelError instanceof Error ? cancelError.message : 'Failed to cancel job');
    }

    // Always clean up local state
    cleanup();
    setRunning(false);
  }, [currentJobId, cleanup]);

  const reset = useCallback(() => {
    cleanup();
    setManifest(null);
    setProgress(0);
    setRunning(false);
    setError(null);
    setCurrentJobId(null);
    setProcessingTime(null);
    startTimeRef.current = null;
  }, [cleanup]);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    // State
    manifest,
    progress,
    running,
    error,
    currentJobId,
    processingTime,
    
    // Actions
    start,
    cancel,
    reset,
    
    // Computed values
    isComplete: !!manifest && !running,
    canStart: !!folderPath && !running,
    hasResults: !!manifest,
    progressPercentage: Math.min(100, Math.max(0, progress))
  };
};