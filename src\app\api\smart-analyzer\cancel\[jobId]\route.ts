import { NextRequest, NextResponse } from 'next/server';

export async function DELETE(
  request: NextRequest,
  { params }: { params: { jobId: string } }
) {
  const { jobId } = params;
  const backendPort = process.env.BACKEND_PORT || '3003';
  
  console.log(`🔄 Smart Analyzer API: Proxying cancel request for job ${jobId}`);
  
  try {
    const response = await fetch(`http://localhost:${backendPort}/api/smart-analyzer/cancel/${jobId}`, {
      method: 'DELETE'
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('❌ Smart Analyzer API: Backend cancel error:', errorData);
      return NextResponse.json(errorData, { status: response.status });
    }

    const data = await response.json();
    console.log(`✅ Smart Analyzer API: Job ${jobId} cancelled successfully`);
    
    return NextResponse.json(data);
    
  } catch (error) {
    console.error(`❌ Smart Analyzer API: Cancel proxy error for job ${jobId}:`, error);
    return NextResponse.json(
      { error: 'Failed to cancel job via backend', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}