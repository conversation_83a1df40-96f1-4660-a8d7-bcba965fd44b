"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useRef, useMemo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import Fuse from "fuse.js";
import {
  RotateCw,
  RotateCcw,
  Eye,
  Download,
  Trash2,
  RefreshCw,
  Loader2,
  CheckCircle2,
  AlertCircle,
  Clock,
  FlipVertical2,
  Save,
  Database,
  X,
} from "lucide-react";
import Image from "next/image";
import { ImageFile } from "@/types";
import { Filter, FilterStack } from "@/types/filters";
import FilterBuilder from "./filter-builder";
import SelectionControls from "./selection-controls";
import PaginationControls from "./pagination-controls";
import SmartChips from "./smart-chips";
import { useS<PERSON><PERSON><PERSON>yzer } from "@/hooks/useSmartAnalyzer";
import { cn } from "@/lib/utils";

// Helper functions for smart chip selection
function matchesSizeRange(sizeKB: number, range: string): boolean {
  switch (range) {
    case "< 100KB":
      return sizeKB < 100;
    case "100KB - 300KB":
      return sizeKB >= 100 && sizeKB < 300;
    case "300KB - 500KB":
      return sizeKB >= 300 && sizeKB < 500;
    case "500KB - 1MB":
      return sizeKB >= 500 && sizeKB < 1024;
    case "1MB - 2MB":
      return sizeKB >= 1024 && sizeKB < 2048;
    case "2MB - 5MB":
      return sizeKB >= 2048 && sizeKB < 5120;
    case "> 5MB":
      return sizeKB >= 5120;
    default:
      return false;
  }
}

function matchesResolutionRange(
  width: number,
  height: number,
  range: string
): boolean {
  const megapixels = (width * height) / (1024 * 1024);
  switch (range) {
    case "< 1MP":
      return megapixels < 1;
    case "1-2MP":
      return megapixels >= 1 && megapixels < 2;
    case "2-5MP":
      return megapixels >= 2 && megapixels < 5;
    case "5-8MP":
      return megapixels >= 5 && megapixels < 8;
    case "8-12MP":
      return megapixels >= 8 && megapixels < 12;
    case "> 12MP":
      return megapixels >= 12;
    default:
      return false;
  }
}

interface EnhancedImageGridProps {
  images: ImageFile[]; // Currently displayed images (paginated)
  allImages: ImageFile[]; // All images in folder
  filteredImages: ImageFile[]; // All filtered images (before pagination)
  folderPath?: string; // Path to current folder for Smart Analyzer
  onImageSelect: (image: ImageFile) => void;
  onImageRotate: (imageId: string, degrees: number) => void;
  selectedImageId?: string;
  gridSize: number;
  className?: string;
  onClearCache?: (imageId?: string) => void; // Optional cache clearing callback
  // Multi-select props
  multiSelect?: boolean;
  selectedIds?: Set<string>;
  onSelectionChange?: (ids: Set<string>) => void;
  // Filter system props
  filters: FilterStack;
  onAddFilter: (filter: Filter) => void;
  onRemoveFilter: (index: number) => void;
  onClearAllFilters: () => void;
  // Pagination props
  showAllImages: boolean;
  onShowAllToggle: () => void;
  currentPage: number;
  totalPages: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  // Cache map update callback
  onHasCacheMapUpdate?: (map: Map<string, boolean>) => void;
}

interface ImageOperationStatus {
  id: string;
  operation: "rotating" | "processing" | "completed" | "error";
  progress?: number;
  message?: string;
}

export default function EnhancedImageGrid({
  images,
  allImages,
  filteredImages,
  folderPath,
  onImageSelect,
  onImageRotate,
  selectedImageId,
  gridSize,
  className,
  onClearCache,
  multiSelect = false,
  selectedIds = new Set(),
  onSelectionChange,
  // Filter system props
  filters,
  onAddFilter,
  onRemoveFilter,
  onClearAllFilters,
  // Pagination props
  showAllImages,
  onShowAllToggle,
  currentPage,
  totalPages,
  pageSize,
  onPageChange,
  onPageSizeChange,
  onHasCacheMapUpdate,
}: EnhancedImageGridProps) {
  const [imageStates, setImageStates] = useState<
    Map<string, ImageOperationStatus>
  >(new Map());
  const [refreshingImages, setRefreshingImages] = useState<Set<string>>(
    new Set()
  );
  const [cacheStatus, setCacheStatus] = useState<Map<string, boolean>>(
    new Map()
  );
  const [hoveredImage, setHoveredImage] = useState<string | null>(null);
  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null);
  const [previewPosition, setPreviewPosition] = useState({ x: 0, y: 0 });
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Smart Filter Analyzer hook
  const smartAnalyzer = useSmartAnalyzer(folderPath);

  // Optimized multi-select handler - minimal re-renders
  const selectedIdsRef = useRef<Set<string>>(selectedIds);

  // Update ref when props change
  useEffect(() => {
    selectedIdsRef.current = selectedIds;
  }, [selectedIds]);

  const toggleImageSelection = useCallback(
    (imageId: string) => {
      if (!multiSelect || !onSelectionChange) return;

      // Work with the ref for immediate updates
      const currentSelection = selectedIdsRef.current;
      const newSelectedIds = new Set(currentSelection);

      if (newSelectedIds.has(imageId)) {
        newSelectedIds.delete(imageId);
      } else {
        newSelectedIds.add(imageId);
      }

      // Update the ref immediately
      selectedIdsRef.current = newSelectedIds;

      // Notify parent with minimal delay to batch updates
      onSelectionChange(newSelectedIds);
    },
    [multiSelect, onSelectionChange]
  );

  // Combined click handler for both single and multi-select modes
  const handleImageClick = useCallback(
    (image: ImageFile) => {
      if (multiSelect) {
        toggleImageSelection(image.id);
      } else {
        onImageSelect(image);
      }
    },
    [multiSelect, toggleImageSelection, onImageSelect]
  );

  // Track images that are being refreshed by the parent component
  useEffect(() => {
    // When images prop changes, clear any refreshing states for updated images
    setRefreshingImages((prev) => {
      const newSet = new Set(prev);
      images.forEach((image) => {
        // If an image was refreshing and now has a new timestamp, clear the refreshing state
        if (newSet.has(image.id)) {
          newSet.delete(image.id);
        }
      });
      return newSet;
    });
  }, [images]);

  // Enhanced rotate function with progress tracking
  const handleRotate = async (imageId: string, degrees: number) => {
    // Set rotating state with initial progress
    setImageStates((prev) =>
      new Map(prev).set(imageId, {
        id: imageId,
        operation: "rotating",
        progress: 0,
        message: `Rotating ${
          degrees === 180
            ? "180°"
            : degrees > 0
            ? "clockwise"
            : "counter-clockwise"
        }...`,
      })
    );

    // Simulate progress updates for user feedback
    const progressInterval = setInterval(() => {
      setImageStates((prev) => {
        const current = prev.get(imageId);
        if (
          current &&
          current.progress !== undefined &&
          current.progress < 90
        ) {
          return new Map(prev).set(imageId, {
            ...current,
            progress: current.progress + 15,
          });
        }
        return prev;
      });
    }, 100);

    try {
      // Call the parent callback to handle rotation
      if (onImageRotate) {
        await onImageRotate(imageId, degrees);
      }

      // Clear progress interval
      clearInterval(progressInterval);

      // Set completed state
      setImageStates((prev) =>
        new Map(prev).set(imageId, {
          id: imageId,
          operation: "completed",
          progress: 100,
          message: "Rotation completed!",
        })
      );

      // The parent component will update the images state with new timestamps
      // which will trigger our useEffect to clear the refreshing state

      // Clear status after delay to show completion feedback
      setTimeout(() => {
        setImageStates((prev) => {
          const newMap = new Map(prev);
          newMap.delete(imageId);
          return newMap;
        });
      }, 1500);
    } catch (error) {
      clearInterval(progressInterval);

      setImageStates((prev) =>
        new Map(prev).set(imageId, {
          id: imageId,
          operation: "error",
          message: error instanceof Error ? error.message : "Rotation failed",
        })
      );

      // Clear error status after delay
      setTimeout(() => {
        setImageStates((prev) => {
          const newMap = new Map(prev);
          newMap.delete(imageId);
          return newMap;
        });
      }, 3000);
    }
  };

  const getImageUrl = useCallback((image: ImageFile) => {
    // Always use the provided thumbnailUrl - it may have been cache-busted by parent
    const baseUrl = image.thumbnailUrl.split("?")[0];

    // If parent already added cache busting, use it
    if (image.thumbnailUrl.includes("?t=")) {
      return image.thumbnailUrl;
    }

    // Otherwise, add our own cache busting based on lastModified
    const timestamp = new Date(image.lastModified).getTime();
    return `${baseUrl}?t=${timestamp}&cb=${Math.random()
      .toString(36)
      .substr(2, 9)}`;
  }, []);

  const getImageOperationState = useCallback(
    (imageId: string) => {
      return imageStates.get(imageId);
    },
    [imageStates]
  );

  // Hover preview functionality
  const handleMouseEnter = useCallback(
    (image: ImageFile, event: React.MouseEvent<HTMLDivElement>) => {
      // Check if this specific image has an operation in progress
      const operationStatus = imageStates.get(image.id);
      if (
        operationStatus?.operation === "rotating" ||
        operationStatus?.operation === "processing"
      ) {
        return;
      }

      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }

      const rect = event.currentTarget.getBoundingClientRect();
      const scrollTop =
        window.pageYOffset || document.documentElement.scrollTop;
      const scrollLeft =
        window.pageXOffset || document.documentElement.scrollLeft;

      // Calculate optimal position for preview
      const previewWidth = 650;
      const previewHeight = 500;

      let x = rect.right + scrollLeft + 15;
      let y = rect.top + scrollTop - (previewHeight - rect.height) / 2;

      // Adjust if preview would go off screen
      if (x + previewWidth > window.innerWidth + scrollLeft) {
        x = rect.left + scrollLeft - previewWidth - 15;
      }
      if (y < scrollTop + 10) {
        y = scrollTop + 10;
      }
      if (y + previewHeight > scrollTop + window.innerHeight - 10) {
        y = scrollTop + window.innerHeight - previewHeight - 10;
      }

      setPreviewPosition({ x, y });

      hoverTimeoutRef.current = setTimeout(() => {
        // Double-check operation status before showing preview
        const currentStatus = imageStates.get(image.id);
        if (
          !currentStatus ||
          (currentStatus.operation !== "rotating" &&
            currentStatus.operation !== "processing")
        ) {
          setHoveredImage(image.id);
        }
      }, 2000); // 2 second delay (doubled from 1 second)
    },
    [imageStates]
  );

  const handleMouseLeave = useCallback(() => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }
    setHoveredImage(null);
  }, []);

  // Cache clearing functionality
  const handleClearCache = async (imageId: string) => {
    try {
      const image = images.find((img) => img.id === imageId);
      if (!image) return;

      const encodedPath = btoa(image.path);
      const response = await fetch("/api/ocr/cache/delete", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ imagePath: encodedPath }),
      });

      if (response.ok) {
        // Update cache status locally
        setCacheStatus((prev) => {
          const newStatus = new Map(prev);
          newStatus.set(imageId, false);
          return newStatus;
        });

        // Notify parent component if callback provided
        if (onClearCache) {
          onClearCache(imageId);
        }

        console.log("Cache cleared for image:", image.filename);
      } else {
        console.error("Failed to clear cache for image:", image.filename);
      }
    } catch (error) {
      console.error("Error clearing cache:", error);
    }
  };

  // Check cache status for all images with debouncing and error handling
  useEffect(() => {
    const checkCacheStatus = async () => {
      if (allImages.length === 0) return;

      const newCacheStatus = new Map<string, boolean>();

      // Check cache for each image in smaller batches to improve performance
      const batchSize = 5;

      try {
        for (let i = 0; i < allImages.length; i += batchSize) {
          const batch = allImages.slice(i, i + batchSize);

          const promises = batch.map(async (image) => {
            try {
              const controller = new AbortController();
              const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

              const encodedPath = btoa(image.path);
              const response = await fetch("/api/ocr/cache/check", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({ imagePath: encodedPath }),
                signal: controller.signal,
              });

              clearTimeout(timeoutId);

              if (response.ok) {
                const cacheInfo = await response.json();
                return {
                  imageId: image.id,
                  hasCache:
                    cacheInfo.exists &&
                    cacheInfo.availableVariations &&
                    cacheInfo.availableVariations.length > 0,
                };
              }
            } catch (error) {
              if (error instanceof Error && error.name === "AbortError") {
                console.warn(`Cache check timeout for: ${image.filename}`);
              } else {
                console.warn(
                  `Cache check failed for: ${image.filename}`,
                  error
                );
              }
            }
            return { imageId: image.id, hasCache: false };
          });

          const results = await Promise.all(promises);
          results.forEach(({ imageId, hasCache }) => {
            newCacheStatus.set(imageId, hasCache);
          });

          // Smaller delay between batches
          if (i + batchSize < allImages.length) {
            await new Promise((resolve) => setTimeout(resolve, 50));
          }
        }
      } catch (error) {
        console.warn("Cache status check failed:", error);
      }

      setCacheStatus(newCacheStatus);

      // Update parent cache map for filtering
      if (onHasCacheMapUpdate) {
        onHasCacheMapUpdate(newCacheStatus);
      }
    };

    // Debounce the cache check to prevent excessive API calls
    const timeoutId = setTimeout(() => {
      checkCacheStatus();
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [allImages, onHasCacheMapUpdate]);

  if (allImages.length === 0) {
    return (
      <Card className={cn("h-full", className)}>
        <CardContent className="flex items-center justify-center h-full text-muted-foreground">
          <div className="text-center">
            <Eye className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p className="mb-2">No images in this folder</p>
            <p className="text-sm">
              Select a folder containing images to get started
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Power Filter System */}
      <FilterBuilder
        filters={filters}
        onAddFilter={onAddFilter}
        onRemoveFilter={onRemoveFilter}
        onClearAll={onClearAllFilters}
        smartAnalyzer={smartAnalyzer}
      />

      {/* Selection Controls */}
      {multiSelect && (
        <SelectionControls
          filteredImages={filteredImages}
          totalImages={allImages}
          selectedIds={selectedIds}
          onSelectionChange={onSelectionChange || (() => {})}
        />
      )}

      {/* Pagination Controls */}
      <PaginationControls
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        totalItems={filteredImages.length}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
        showAll={showAllImages}
        onShowAllToggle={onShowAllToggle}
      />

      {/* Grid Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="font-medium">Images</h3>
          <Badge variant="outline">{images.length} showing</Badge>
          {filteredImages.length !== allImages.length && (
            <Badge variant="secondary">
              of {filteredImages.length} filtered
            </Badge>
          )}
          {allImages.length !== filteredImages.length && (
            <Badge variant="outline">({allImages.length} total)</Badge>
          )}
          {multiSelect && selectedIds.size > 0 && (
            <Badge variant="secondary" className="bg-primary/10">
              {selectedIds.size} selected
            </Badge>
          )}
        </div>

        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>Grid size: {gridSize}px</span>
          {multiSelect && <span>• Multi-select mode</span>}
          {filters.length > 0 && <span>• {filters.length} filters active</span>}
        </div>
      </div>

      {/* Image Grid */}
      <div
        className="grid gap-6 p-2 overflow-y-auto"
        style={{
          gridTemplateColumns: `repeat(auto-fill, minmax(${gridSize}px, 1fr))`,
        }}
      >
        {images.map((image) => {
          const operationStatus = imageStates.get(image.id);
          const isRefreshing = refreshingImages.has(image.id);
          const isSelected = multiSelect
            ? selectedIds.has(image.id)
            : selectedImageId === image.id;
          const hasCache = cacheStatus.get(image.id) || false;

          return (
            <Card
              key={image.id}
              className={cn(
                "relative overflow-hidden transition-all duration-300 hover:shadow-lg cursor-pointer group focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
                isSelected &&
                  "ring-2 ring-purple-500 shadow-xl shadow-purple-500/60 dark:shadow-purple-400/50 gray:shadow-purple-400/50 ring-offset-1 ring-offset-background",
                operationStatus?.operation === "error" && "ring-2 ring-red-500"
              )}
              onClick={() => !operationStatus && handleImageClick(image)}
              onMouseEnter={(e) => handleMouseEnter(image, e)}
              onMouseLeave={handleMouseLeave}
              onKeyDown={(e) => {
                if ((e.key === "Enter" || e.key === " ") && !operationStatus) {
                  e.preventDefault();
                  handleImageClick(image);
                }
              }}
              tabIndex={0}
              role="button"
              aria-label={`Select image ${image.filename}, ${Math.round(
                image.fileSize / 1024
              )}KB, ${image.width} × ${image.height} pixels`}
              aria-pressed={isSelected}
            >
              <CardContent className="p-0">
                {/* Image container */}
                <div
                  className="relative overflow-hidden bg-muted"
                  style={{
                    height: gridSize,
                    width: "100%",
                    position: "relative",
                  }}
                >
                  <Image
                    src={getImageUrl(image)}
                    alt={`${image.filename} - ${Math.round(
                      image.fileSize / 1024
                    )}KB image`}
                    fill
                    className={cn(
                      "object-cover transition-all duration-500 ease-in-out",
                      (isRefreshing ||
                        operationStatus?.operation === "rotating") &&
                        "opacity-60 scale-95"
                    )}
                    sizes={`${gridSize}px`}
                    loading="lazy"
                    placeholder="blur"
                    blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQEhIQFhAVEhIVEhIVEhIVEhIVEhIVEhIVEhISFhUVGhgYGhgYGhf/2wBDAQcHBwoIChMKChMYFhIWGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkgEgUXB/xAAVAQEBAAAAAAAAAAAAAAAAAAAQAf/aAAwDAQACEQMRAD8A0NbvFovF6z2hcqrVCMYVoSK4pnG4KjN4CyqVdF9lLVu5IhGGQGTFFNJFmO6VkpNmD7z5lGnJr14sNZAQAAAAAA//2Q=="
                    key={`thumb-${image.id}-${image.lastModified.getTime()}`}
                    onLoad={() => {
                      // Image loaded successfully - could remove any loading state
                    }}
                    onError={(e) => {
                      console.warn(
                        `Failed to load thumbnail: ${image.filename}`
                      );
                      // The Next.js Image component handles fallbacks automatically
                    }}
                  />

                  {/* Loading overlay */}
                  {(isRefreshing || operationStatus) && (
                    <div className="absolute inset-0 bg-black/50 flex items-center justify-center transition-opacity duration-300">
                      <div className="text-center text-white">
                        {operationStatus?.operation === "rotating" && (
                          <>
                            <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
                            <div className="text-xs mb-1">
                              {operationStatus.message}
                            </div>
                            {operationStatus.progress !== undefined && (
                              <Progress
                                value={operationStatus.progress}
                                className="w-20 h-1 bg-primary/20"
                              />
                            )}
                          </>
                        )}

                        {operationStatus?.operation === "completed" && (
                          <>
                            <CheckCircle2 className="h-6 w-6 mx-auto mb-1 text-green-400" />
                            <div className="text-xs">
                              {operationStatus.message}
                            </div>
                          </>
                        )}

                        {operationStatus?.operation === "error" && (
                          <>
                            <AlertCircle className="h-6 w-6 mx-auto mb-1 text-red-400" />
                            <div className="text-xs">
                              {operationStatus.message}
                            </div>
                          </>
                        )}

                        {isRefreshing && !operationStatus && (
                          <>
                            <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-1" />
                            <div className="text-xs">Refreshing...</div>
                          </>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Multi-select checkbox */}
                  {multiSelect && (
                    <div className="absolute top-2 left-2 z-20">
                      <Checkbox
                        checked={selectedIds.has(image.id)}
                        onCheckedChange={() => toggleImageSelection(image.id)}
                        onClick={(e) => e.stopPropagation()}
                        className="bg-white/90 shadow-md h-4 w-4"
                        aria-label={`Select ${image.filename}`}
                      />
                    </div>
                  )}

                  {/* Enhanced Cache indicator badges */}
                  {hasCache && !operationStatus && (
                    <div
                      className={cn(
                        "absolute z-10 flex flex-col gap-1",
                        multiSelect ? "top-2 right-2" : "top-2 left-2"
                      )}
                    >
                      {/* OCR Cache Indicator */}
                      <div className="bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs px-2 py-1 rounded-full shadow-lg backdrop-blur-sm border border-green-400/50 flex items-center gap-1 animate-fade-in">
                        <div className="w-2 h-2 bg-green-300 rounded-full animate-pulse"></div>
                        <span className="font-medium">OCR</span>
                      </div>
                      {/* ReadySearch Cache Indicator */}
                      {/* TODO: Implement ReadySearch cache detection logic */}
                      <div className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white text-xs px-2 py-1 rounded-full shadow-lg backdrop-blur-sm border border-blue-400/50 flex items-center gap-1 animate-fade-in">
                        <div className="w-2 h-2 bg-blue-300 rounded-full animate-pulse"></div>
                        <span className="font-medium">RS</span>
                      </div>
                    </div>
                  )}

                  {/* Control overlay */}
                  <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="info"
                        className="h-8 w-8 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRotate(image.id, -90);
                        }}
                        disabled={!!operationStatus}
                        title="Rotate counter-clockwise"
                        aria-label={`Rotate ${image.filename} counter-clockwise`}
                      >
                        <RotateCcw className="h-5 w-5" aria-hidden="true" />
                      </Button>

                      <Button
                        size="sm"
                        variant="premium"
                        className="h-8 w-8 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRotate(image.id, 180);
                        }}
                        disabled={!!operationStatus}
                        title="Rotate 180 degrees"
                        aria-label={`Rotate ${image.filename} 180 degrees`}
                      >
                        <FlipVertical2 className="h-5 w-5" aria-hidden="true" />
                      </Button>

                      <Button
                        size="sm"
                        variant="success"
                        className="h-8 w-8 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRotate(image.id, 90);
                        }}
                        disabled={!!operationStatus}
                        title="Rotate clockwise"
                        aria-label={`Rotate ${image.filename} clockwise`}
                      >
                        <RotateCw className="h-5 w-5" aria-hidden="true" />
                      </Button>

                      {/* Cache clear button - only show if image has cache */}
                      {hasCache && (
                        <Button
                          size="sm"
                          variant="destructive"
                          className="h-8 w-8 p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleClearCache(image.id);
                          }}
                          disabled={!!operationStatus}
                          title="Clear cached OCR results"
                          aria-label={`Clear cached OCR results for ${image.filename}`}
                        >
                          <X className="h-5 w-5" aria-hidden="true" />
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Status indicator */}
                  {isSelected && (
                    <div className="absolute top-2 right-2 z-20">
                      <Badge className="bg-primary text-primary-foreground shadow-md">
                        Selected
                      </Badge>
                    </div>
                  )}
                </div>

                {/* Image info */}
                <div className="p-3">
                  <div className="space-y-1">
                    <p
                      className="text-sm font-medium truncate"
                      title={image.filename}
                    >
                      {image.filename}
                    </p>
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>{Math.round(image.fileSize / 1024)}KB</span>
                      <span>
                        {image.width} × {image.height}
                      </span>
                    </div>
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      <span>
                        {new Date(image.lastModified).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}

        {/* No results message */}
        {images.length === 0 &&
          filteredImages.length === 0 &&
          allImages.length > 0 && (
            <div className="col-span-full flex items-center justify-center p-8 text-muted-foreground">
              <div className="text-center">
                <Eye className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p className="mb-2">No images match the current filters</p>
                <p className="text-sm">
                  Try adjusting your filters or clearing them
                </p>
              </div>
            </div>
          )}
      </div>

      {/* Hover Preview Overlay */}
      {hoveredImage && (
        <div
          className="fixed z-50 bg-background border-2 border-border rounded-lg shadow-2xl p-4 animate-fade-in pointer-events-none"
          style={{
            left: `${previewPosition.x}px`,
            top: `${previewPosition.y}px`,
            width: "650px",
            maxHeight: "500px",
            opacity: 1,
            transform: "translateY(0)",
            transition: "opacity 0.2s ease-out, transform 0.2s ease-out",
          }}
        >
          {(() => {
            const previewImage = images.find((img) => img.id === hoveredImage);
            if (!previewImage) return null;

            return (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-sm truncate flex-1 mr-2">
                    {previewImage.filename}
                  </h3>
                  <div className="text-xs text-muted-foreground">
                    {previewImage.width} × {previewImage.height}
                  </div>
                </div>

                <div className="relative bg-muted rounded overflow-hidden">
                  <Image
                    src={
                      previewImage.previewUrl.includes("?t=")
                        ? previewImage.previewUrl
                        : `${previewImage.previewUrl}?t=${new Date(
                            previewImage.lastModified
                          ).getTime()}&cb=${Math.random()
                            .toString(36)
                            .substr(2, 9)}`
                    }
                    alt={previewImage.filename}
                    width={600}
                    height={400}
                    className="w-full h-auto max-h-96 object-contain transition-opacity duration-300"
                    loading="eager"
                    key={`preview-${
                      previewImage.id
                    }-${previewImage.lastModified.getTime()}`}
                    onLoad={() => {
                      console.log(
                        `Preview loaded successfully: ${previewImage.filename}`
                      );
                    }}
                    onError={(e) => {
                      console.warn(
                        `Failed to load preview for ${previewImage.filename}`
                      );
                      const target = e.target as HTMLImageElement;
                      target.style.display = "none";
                      const parent = target.parentElement;
                      if (parent && !parent.querySelector(".preview-error")) {
                        const error = document.createElement("div");
                        error.className =
                          "preview-error w-full h-32 bg-muted rounded flex items-center justify-center text-muted-foreground text-sm";
                        error.innerHTML = "🖼️ Preview unavailable";
                        parent.appendChild(error);
                      }
                    }}
                  />
                </div>

                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>{Math.round(previewImage.fileSize / 1024)}KB</span>
                  <span>
                    {new Date(previewImage.lastModified).toLocaleDateString()}
                  </span>
                </div>
              </div>
            );
          })()}
        </div>
      )}
    </div>
  );
}
