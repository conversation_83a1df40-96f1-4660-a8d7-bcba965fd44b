const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs').promises;

class DatabaseManager {
  constructor(dbPath = null) {
    this.dbPath = dbPath || path.join(__dirname, '../../data/dl-organizer.db');
    this.db = null;
    this.isInitialized = false;
  }
  
  async initialize() {
    if (this.isInitialized) return;
    
    try {
      // Ensure data directory exists
      const dataDir = path.dirname(this.dbPath);
      await fs.mkdir(dataDir, { recursive: true });
      
      // Open database connection
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error('Error opening database:', err);
          throw err;
        }
        console.log(`Connected to SQLite database: ${this.dbPath}`);
      });
      
      // Enable foreign keys
      await this.run('PRAGMA foreign_keys = ON');
      
      // Create tables
      await this.createTables();
      
      // Create indexes for performance
      await this.createIndexes();
      
      this.isInitialized = true;
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }
  
  async createTables() {
    const tables = [
      // Projects table
      `CREATE TABLE IF NOT EXISTS projects (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        root_path TEXT NOT NULL,
        settings TEXT DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // Folders table
      `CREATE TABLE IF NOT EXISTS folders (
        id TEXT PRIMARY KEY,
        project_id TEXT NOT NULL,
        name TEXT NOT NULL,
        path TEXT NOT NULL UNIQUE,
        parent_id TEXT,
        relative_path TEXT,
        image_count INTEGER DEFAULT 0,
        has_text_file BOOLEAN DEFAULT FALSE,
        tags TEXT DEFAULT '[]',
        metadata TEXT DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
        FOREIGN KEY (parent_id) REFERENCES folders(id) ON DELETE SET NULL
      )`,
      
      // Images table
      `CREATE TABLE IF NOT EXISTS images (
        id TEXT PRIMARY KEY,
        folder_id TEXT NOT NULL,
        filename TEXT NOT NULL,
        path TEXT NOT NULL UNIQUE,
        relative_path TEXT,
        file_size INTEGER,
        width INTEGER,
        height INTEGER,
        format TEXT,
        rotation INTEGER DEFAULT 0,
        thumbnail_path TEXT,
        preview_path TEXT,
        metadata TEXT DEFAULT '{}',
        last_modified DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (folder_id) REFERENCES folders(id) ON DELETE CASCADE
      )`,
      
      // OCR results table
      `CREATE TABLE IF NOT EXISTS ocr_results (
        id TEXT PRIMARY KEY,
        image_id TEXT NOT NULL,
        folder_id TEXT NOT NULL,
        first_name TEXT,
        last_name TEXT,
        date_of_birth TEXT,
        address TEXT,
        license_number TEXT,
        expiration_date TEXT,
        issue_date TEXT,
        state TEXT,
        confidence REAL DEFAULT 0,
        raw_text TEXT,
        model_used TEXT,
        processing_time INTEGER,
        cost REAL DEFAULT 0,
        metadata TEXT DEFAULT '{}',
        processed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE,
        FOREIGN KEY (folder_id) REFERENCES folders(id) ON DELETE CASCADE
      )`,
      
      // Processing jobs table (for batch operations)
      `CREATE TABLE IF NOT EXISTS processing_jobs (
        id TEXT PRIMARY KEY,
        project_id TEXT NOT NULL,
        type TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        progress INTEGER DEFAULT 0,
        total_items INTEGER DEFAULT 0,
        processed_items INTEGER DEFAULT 0,
        failed_items INTEGER DEFAULT 0,
        settings TEXT DEFAULT '{}',
        results TEXT DEFAULT '[]',
        errors TEXT DEFAULT '[]',
        started_at DATETIME,
        completed_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
      )`,
      
      // Settings table for application configuration
      `CREATE TABLE IF NOT EXISTS app_settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        type TEXT DEFAULT 'string',
        description TEXT,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // Cost tracking table
      `CREATE TABLE IF NOT EXISTS cost_tracking (
        id TEXT PRIMARY KEY,
        model_id TEXT NOT NULL,
        provider_id TEXT NOT NULL,
        operation_type TEXT NOT NULL,
        tokens_used INTEGER DEFAULT 0,
        cost REAL NOT NULL,
        image_id TEXT,
        ocr_result_id TEXT,
        processing_time INTEGER,
        success BOOLEAN DEFAULT TRUE,
        error_message TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE SET NULL,
        FOREIGN KEY (ocr_result_id) REFERENCES ocr_results(id) ON DELETE SET NULL
      )`
    ];
    
    for (const tableSQL of tables) {
      await this.run(tableSQL);
    }
  }
  
  async createIndexes() {
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_folders_project_id ON folders(project_id)',
      'CREATE INDEX IF NOT EXISTS idx_folders_parent_id ON folders(parent_id)',
      'CREATE INDEX IF NOT EXISTS idx_folders_path ON folders(path)',
      'CREATE INDEX IF NOT EXISTS idx_images_folder_id ON images(folder_id)',
      'CREATE INDEX IF NOT EXISTS idx_images_path ON images(path)',
      'CREATE INDEX IF NOT EXISTS idx_ocr_results_image_id ON ocr_results(image_id)',
      'CREATE INDEX IF NOT EXISTS idx_ocr_results_folder_id ON ocr_results(folder_id)',
      'CREATE INDEX IF NOT EXISTS idx_ocr_results_processed_at ON ocr_results(processed_at)',
      'CREATE INDEX IF NOT EXISTS idx_processing_jobs_project_id ON processing_jobs(project_id)',
      'CREATE INDEX IF NOT EXISTS idx_processing_jobs_status ON processing_jobs(status)',
      'CREATE INDEX IF NOT EXISTS idx_cost_tracking_created_at ON cost_tracking(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_cost_tracking_model_id ON cost_tracking(model_id)'
    ];
    
    for (const indexSQL of indexes) {
      await this.run(indexSQL);
    }
  }
  
  // Promisified database operations
  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) {
          console.error('Database run error:', err);
          reject(err);
        } else {
          resolve({ id: this.lastID, changes: this.changes });
        }
      });
    });
  }
  
  get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) {
          console.error('Database get error:', err);
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }
  
  all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          console.error('Database all error:', err);
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }
  
  // Transaction support
  async transaction(operations) {
    await this.run('BEGIN TRANSACTION');
    
    try {
      const results = [];
      for (const operation of operations) {
        const result = await operation(this);
        results.push(result);
      }
      
      await this.run('COMMIT');
      return results;
    } catch (error) {
      await this.run('ROLLBACK');
      throw error;
    }
  }
  
  // Backup database
  async backup(backupPath) {
    return new Promise((resolve, reject) => {
      const backup = this.db.backup(backupPath);
      
      backup.step(-1, (err) => {
        if (err) {
          reject(err);
        } else {
          backup.finish((err) => {
            if (err) {
              reject(err);
            } else {
              resolve(backupPath);
            }
          });
        }
      });
    });
  }
  
  // Database maintenance
  async vacuum() {
    await this.run('VACUUM');
    console.log('Database vacuumed successfully');
  }
  
  async analyze() {
    await this.run('ANALYZE');
    console.log('Database statistics updated');
  }
  
  // Get database statistics
  async getStats() {
    const tables = ['projects', 'folders', 'images', 'ocr_results', 'processing_jobs', 'cost_tracking'];
    const stats = {};
    
    for (const table of tables) {
      try {
        const result = await this.get(`SELECT COUNT(*) as count FROM ${table}`);
        stats[table] = result.count;
      } catch (error) {
        stats[table] = 0;
      }
    }
    
    // Get database file size
    try {
      const fs = require('fs').promises;
      const dbStats = await fs.stat(this.dbPath);
      stats.databaseSize = dbStats.size;
      stats.databaseSizeMB = Math.round(dbStats.size / (1024 * 1024) * 100) / 100;
    } catch (error) {
      stats.databaseSize = 0;
      stats.databaseSizeMB = 0;
    }
    
    return stats;
  }
  
  // Close database connection
  async close() {
    if (this.db) {
      return new Promise((resolve, reject) => {
        this.db.close((err) => {
          if (err) {
            console.error('Error closing database:', err);
            reject(err);
          } else {
            console.log('Database connection closed');
            this.isInitialized = false;
            resolve();
          }
        });
      });
    }
  }
  
  // Health check
  async healthCheck() {
    try {
      await this.get('SELECT 1');
      return { status: 'healthy', timestamp: new Date().toISOString() };
    } catch (error) {
      return { status: 'unhealthy', error: error.message, timestamp: new Date().toISOString() };
    }
  }
}

module.exports = DatabaseManager;