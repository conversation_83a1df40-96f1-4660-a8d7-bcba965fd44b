import { NextRequest, NextResponse } from "next/server";
import { getAppConfig } from "@/config/app-config";

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    const config = getAppConfig();
    const previewPath = params.path.join("/");

    console.log(`🖼️  Preview request: ${previewPath}`);

    // The previewPath should be in format: {base64EncodedFilePath}.jpg
    // We need to forward this directly to the backend
    let finalPath = previewPath;

    // Validate that this looks like a proper preview request
    if (
      previewPath.includes(".jpg") &&
      previewPath.match(/^[A-Za-z0-9+/=]+\.jpg$/)
    ) {
      // Extract the base64 part for validation
      const base64Part = previewPath.replace(".jpg", "");
      try {
        // Decode to verify it's a valid base64 encoded file path
        const decoded = Buffer.from(base64Part, "base64").toString("utf8");
        console.log(`🔓 Validated preview request for file: ${decoded}`);

        // Use the original path as-is (it's already in the correct format)
        finalPath = previewPath;
        console.log(`🎯 Using preview path: ${finalPath}`);
      } catch (decodeError) {
        console.warn(
          `⚠️  Invalid base64 in preview path: ${previewPath}`,
          decodeError
        );
        // If decoding fails, still try the original path
        finalPath = previewPath;
      }
    } else {
      console.log(`📝 Non-standard preview path format: ${previewPath}`);
    }

    // Forward the request to the backend
    const backendUrl = `${config.backendUrl}/previews/${finalPath}`;
    console.log(`📡 Forwarding to backend: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: "GET",
      headers: {
        "User-Agent": request.headers.get("User-Agent") || "",
      },
    });

    if (!response.ok) {
      console.warn(
        `❌ Backend preview not found: ${backendUrl} (${response.status})`
      );
      return new NextResponse("Preview not found", { status: 404 });
    }

    // Get the image data
    const imageBuffer = await response.arrayBuffer();
    console.log(
      `✅ Preview served successfully: ${previewPath} (${imageBuffer.byteLength} bytes)`
    );

    // Return the image with proper headers
    // Use shorter cache for previews to allow for rotation updates
    const cacheControl =
      request.url.includes("?t=") || request.url.includes("&cb=")
        ? "public, max-age=3600" // 1 hour for cache-busted requests
        : "public, max-age=300"; // 5 minutes for regular requests

    return new NextResponse(imageBuffer, {
      status: 200,
      headers: {
        "Content-Type": response.headers.get("Content-Type") || "image/jpeg",
        "Cache-Control": cacheControl,
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET",
        "Cross-Origin-Resource-Policy": "cross-origin",
        ETag: `"${Date.now()}"`, // Add ETag for better cache management
      },
    });
  } catch (error) {
    console.error("❌ Preview proxy error:", error);
    return new NextResponse("Internal server error", { status: 500 });
  }
}
