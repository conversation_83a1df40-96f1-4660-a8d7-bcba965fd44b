import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { jobId: string } }
) {
  const { jobId } = params;
  const backendPort = process.env.BACKEND_PORT || '3003';
  
  console.log(`🔄 Smart Analyzer API: Proxying status request for job ${jobId}`);
  
  try {
    const response = await fetch(`http://localhost:${backendPort}/api/smart-analyzer/status/${jobId}`, {
      method: 'GET'
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('❌ Smart Analyzer API: Backend status error:', errorData);
      return NextResponse.json(errorData, { status: response.status });
    }

    const data = await response.json();
    
    return NextResponse.json(data);
    
  } catch (error) {
    console.error(`❌ Smart Analyzer API: Status proxy error for job ${jobId}:`, error);
    return NextResponse.json(
      { error: 'Failed to get job status from backend', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}