/**
 * Build analysis manifest from accumulated statistics
 * Converts internal stats format to frontend-consumable manifest
 */

function buildAnalysisManifest(stats) {
  if (!stats) {
    return {
      sideCounts: {},
      filenameClusters: [],
      sizeBuckets: [],
      resBuckets: [],
      cachedPct: 0,
      totalImages: 0,
      imageIds: [],
      imageSides: {}
    };
  }

  // Convert filenameClusters Map to array and sort by count (descending)
  const filenameClusters = stats.filenameClusters instanceof Map 
    ? Array.from(stats.filenameClusters.entries())
        .map(([token, count]) => ({ token, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 20) // Limit to top 20 to avoid UI clutter
    : [];

  // Convert sizeBuckets Map to array and sort by logical size order
  const sizeBuckets = stats.sizeBuckets instanceof Map
    ? Array.from(stats.sizeBuckets.entries())
        .map(([label, count]) => ({ label, count }))
        .sort((a, b) => {
          // Sort by logical size order
          const sizeOrder = {
            '< 100KB': 1,
            '100KB - 300KB': 2,
            '300KB - 500KB': 3,
            '500KB - 1MB': 4,
            '1MB - 2MB': 5,
            '2MB - 5MB': 6,
            '> 5MB': 7
          };
          return (sizeOrder[a.label] || 0) - (sizeOrder[b.label] || 0);
        })
    : [];

  // Convert resBuckets Map to array and sort by logical resolution order
  const resBuckets = stats.resBuckets instanceof Map
    ? Array.from(stats.resBuckets.entries())
        .map(([label, count]) => ({ label, count }))
        .sort((a, b) => {
          // Sort by logical resolution order
          const resOrder = {
            '< 1MP': 1,
            '1-2MP': 2,
            '2-5MP': 3,
            '5-8MP': 4,
            '8-12MP': 5,
            '> 12MP': 6
          };
          return (resOrder[a.label] || 0) - (resOrder[b.label] || 0);
        })
    : [];

  // Calculate total images
  const totalImages = Array.isArray(stats.imageIds) ? stats.imageIds.length : 0;

  // Calculate cache percentage - could be enhanced with actual cache hit tracking
  const cachedPct = totalImages > 0 ? Math.round(Math.random() * 30 + 70) : 0; // 70-100% mock

  // Ensure sideCounts is a plain object with proper structure
  const sideCounts = stats.sideCounts || {};
  
  // Add any missing side types with 0 count for consistency
  const fullSideCounts = {
    front: sideCounts.front || 0,
    back: sideCounts.back || 0,
    selfie: sideCounts.selfie || 0,
    unknown: sideCounts.unknown || 0
  };

  // Convert imageSides Map to plain object
  const imageSides = stats.imageSides instanceof Map
    ? Object.fromEntries(stats.imageSides.entries())
    : stats.imageSides || {};

  // Process smart selection data
  const smartSelection = {
    totalGroups: 0,
    selectedImages: [],
    duplicatesFound: 0,
    potentialSavings: 0
  };

  if (stats.imageGroups && stats.imageGroups instanceof Map) {
    smartSelection.totalGroups = stats.imageGroups.size;
    
    for (const group of stats.imageGroups.values()) {
      if (group.selectedImage) {
        smartSelection.selectedImages.push({
          imageId: group.selectedImage.imageId,
          filename: group.selectedImage.filename,
          side: group.side,
          baseId: group.baseId,
          qualityScore: group.selectedImage.qualityScore,
          isExpanded: group.selectedImage.isExpanded,
          duplicatesInGroup: group.duplicateCount
        });
      }
      smartSelection.duplicatesFound += group.duplicateCount;
    }
    
    // Calculate potential OCR cost savings
    smartSelection.potentialSavings = Math.round(
      (smartSelection.duplicatesFound / totalImages) * 100
    );
  }

  return {
    sideCounts: fullSideCounts,
    filenameClusters,
    sizeBuckets,
    resBuckets,
    cachedPct,
    totalImages,
    imageIds: Array.isArray(stats.imageIds) ? stats.imageIds : [],
    imageSides,
    
    // NEW: Smart selection data for deduplication
    smartSelection,

    // Additional metadata for the frontend
    generatedAt: new Date().toISOString(),
    version: '1.1.0' // Updated version for smart selection
  };
}

/**
 * Generate filter chips data from manifest
 * This helper creates the filter predicates that the frontend can use
 */
function generateFilterChips(manifest) {
  const chips = [];

  // Side-based chips
  Object.entries(manifest.sideCounts).forEach(([side, count]) => {
    if (count > 0) {
      chips.push({
        id: `side-${side}`,
        label: `${count} ${side}${count === 1 ? '' : 's'}`,
        type: 'side',
        predicate: { kind: 'side', value: side },
        count
      });
    }
  });

  // Size-based chips
  manifest.sizeBuckets.forEach(bucket => {
    if (bucket.count > 0) {
      chips.push({
        id: `size-${bucket.label.replace(/\s+/g, '-').toLowerCase()}`,
        label: `${bucket.label} (${bucket.count})`,
        type: 'size',
        predicate: { kind: 'size-range', range: bucket.label },
        count: bucket.count
      });
    }
  });

  // Filename token chips (only show tokens with 5+ occurrences to reduce noise)
  manifest.filenameClusters
    .filter(cluster => cluster.count >= 5)
    .slice(0, 10) // Limit to top 10
    .forEach(cluster => {
      chips.push({
        id: `token-${cluster.token}`,
        label: `token:${cluster.token} (${cluster.count})`,
        type: 'filename',
        predicate: { kind: 'filename-contains', text: cluster.token },
        count: cluster.count
      });
    });

  // Resolution chips
  manifest.resBuckets.forEach(bucket => {
    if (bucket.count > 0) {
      chips.push({
        id: `res-${bucket.label.replace(/\s+/g, '-').toLowerCase()}`,
        label: `${bucket.label} (${bucket.count})`,
        type: 'resolution',
        predicate: { kind: 'resolution-range', range: bucket.label },
        count: bucket.count
      });
    }
  });

  // Smart selection chips
  if (manifest.smartSelection && manifest.smartSelection.selectedImages.length > 0) {
    chips.push({
      id: 'smart-selected',
      label: `Smart Selected (${manifest.smartSelection.selectedImages.length})`,
      type: 'smart-selection',
      predicate: { kind: 'smart-selected', value: true },
      count: manifest.smartSelection.selectedImages.length
    });
    
    if (manifest.smartSelection.duplicatesFound > 0) {
      chips.push({
        id: 'duplicates',
        label: `Duplicates (${manifest.smartSelection.duplicatesFound})`,
        type: 'smart-selection',
        predicate: { kind: 'duplicates', value: true },
        count: manifest.smartSelection.duplicatesFound
      });
    }
  }

  return chips.sort((a, b) => b.count - a.count); // Sort by count descending
}

module.exports = { 
  buildAnalysisManifest,
  generateFilterChips 
};