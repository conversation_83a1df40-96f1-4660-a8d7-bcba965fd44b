import { test, expect } from '@playwright/test';

// Test hover preview functionality
test.describe('Hover Preview Functionality', () => {
  test('should test hover preview in OCR Testing Playground', async ({ page }) => {
    // Navigate to OCR Testing page
    await page.goto('http://localhost:3001/ocr-testing');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Take a screenshot before hover
    await page.screenshot({ path: 'before-hover.png' });
    
    // Find the first image container
    const imageContainers = page.locator('label').filter({ hasText: /US DL|@huaquan/ });
    const firstContainer = imageContainers.first();
    
    if (await firstContainer.isVisible()) {
      console.log('✅ Found image container for hover test');
      
      // Get the container's bounding box
      const boundingBox = await firstContainer.boundingBox();
      console.log('Container bounding box:', boundingBox);
      
      // Hover over the first image container
      await firstContainer.hover();
      console.log('✅ Hovered over first image container');
      
      // Wait for hover effect to activate
      await page.waitForTimeout(1000);
      
      // Take a screenshot after hover
      await page.screenshot({ path: 'after-hover.png' });
      
      // Check for hover preview elements
      const hoverPreview = page.locator('div.absolute, .hover-preview, [class*="preview"]');
      const hoverCount = await hoverPreview.count();
      console.log(`Found ${hoverCount} hover preview elements`);
      
      // Look for any preview images that might appear
      const previewImages = page.locator('img').filter({ hasText: /preview|large/ });
      const previewCount = await previewImages.count();
      console.log(`Found ${previewCount} preview images`);
      
      // Check for any overlays or popups
      const overlays = page.locator('[class*="overlay"], [class*="popup"], [class*="modal"]');
      const overlayCount = await overlays.count();
      console.log(`Found ${overlayCount} overlay elements`);
      
      // Test for any z-index changes (elements moving to front)
      const highZIndex = page.locator('[style*="z-index"]');
      const zIndexCount = await highZIndex.count();
      console.log(`Found ${zIndexCount} elements with z-index styles`);
      
      // Move mouse away to test hover out
      await page.mouse.move(0, 0);
      await page.waitForTimeout(1000);
      
      // Take a screenshot after hover out
      await page.screenshot({ path: 'after-hover-out.png' });
      
      console.log('✅ Hover preview test completed');
    } else {
      console.log('❌ No image containers found for hover test');
    }
  });
  
  test('should test hover preview with different image types', async ({ page }) => {
    await page.goto('http://localhost:3001/ocr-testing');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Test hovering over different types of images
    const imageTypes = ['US DL', '@huaquan'];
    
    for (const imageType of imageTypes) {
      console.log(`Testing hover on ${imageType} images...`);
      
      const containers = page.locator('label').filter({ hasText: new RegExp(imageType, 'i') });
      const containerCount = await containers.count();
      console.log(`Found ${containerCount} ${imageType} containers`);
      
      if (containerCount > 0) {
        const firstContainer = containers.first();
        await firstContainer.hover();
        await page.waitForTimeout(500);
        
        // Check for any visual changes
        const allImages = page.locator('img');
        const imageCount = await allImages.count();
        console.log(`Total images visible during ${imageType} hover: ${imageCount}`);
        
        // Check for any position changes
        const absoluteElements = page.locator('div.absolute, .fixed, [style*="absolute"], [style*="fixed"]');
        const absoluteCount = await absoluteElements.count();
        console.log(`Absolute positioned elements during ${imageType} hover: ${absoluteCount}`);
        
        // Move away
        await page.mouse.move(0, 0);
        await page.waitForTimeout(500);
      }
    }
  });
  
  test('should test image loading after hover', async ({ page }) => {
    await page.goto('http://localhost:3001/ocr-testing');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Get initial image count
    const initialImages = page.locator('img');
    const initialCount = await initialImages.count();
    console.log(`Initial image count: ${initialCount}`);
    
    // Hover over first image
    const firstContainer = page.locator('label').first();
    if (await firstContainer.isVisible()) {
      await firstContainer.hover();
      await page.waitForTimeout(1000);
      
      // Check if any new images loaded
      const afterHoverImages = page.locator('img');
      const afterHoverCount = await afterHoverImages.count();
      console.log(`Image count after hover: ${afterHoverCount}`);
      
      if (afterHoverCount > initialCount) {
        console.log(`✅ ${afterHoverCount - initialCount} new images loaded on hover`);
        
        // Check if the new images are actually displaying
        for (let i = initialCount; i < afterHoverCount; i++) {
          const image = afterHoverImages.nth(i);
          const src = await image.getAttribute('src');
          const naturalWidth = await image.evaluate((img: HTMLImageElement) => img.naturalWidth);
          const naturalHeight = await image.evaluate((img: HTMLImageElement) => img.naturalHeight);
          
          console.log(`New image ${i}: src="${src}", dimensions=${naturalWidth}x${naturalHeight}`);
          
          if (naturalWidth > 0 && naturalHeight > 0) {
            console.log(`✅ New image ${i} loaded successfully`);
          } else {
            console.log(`❌ New image ${i} failed to load`);
          }
        }
      } else {
        console.log('ℹ️ No new images loaded on hover');
      }
    }
  });
});