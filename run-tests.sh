#!/bin/bash

# Test images array
declare -a images=(
  "C:/Users/<USER>/Downloads/Telegram Desktop/100x us dl w selfie/100x us dl w selfie/combined_to_do/100/i100/US DL 1535.jpeg"
  "C:/Users/<USER>/Downloads/Telegram Desktop/100x us dl w selfie/100x us dl w selfie/combined_to_do/100/i102/US DL 1541.jpeg"
  "C:/Users/<USER>/Downloads/Telegram Desktop/100x us dl w selfie/100x us dl w selfie/combined_to_do/100/i103/US DL 1540.jpeg"
  "C:/Users/<USER>/Downloads/Telegram Desktop/100x us dl w selfie/100x us dl w selfie/combined_to_do/100/i104/US DL 1546.jpeg"
  "C:/Users/<USER>/Downloads/Telegram Desktop/100x us dl w selfie/100x us dl w selfie/combined_to_do/100/i105/US DL 1545.jpeg"
)

# Models array
declare -a models=(
  "gpt-4o-mini"
  "claude-3-haiku"
  "gpt-4o"
)

# Create results directory
mkdir -p ocr_test_results

echo "🚀 Starting OCR Model Comparison Tests"
echo "======================================"

counter=0
total=$((${#images[@]} * ${#models[@]}))

for image in "${images[@]}"; do
  filename=$(basename "$image")
  echo ""
  echo "📸 Testing image: $filename"
  echo "────────────────────────────────────────────────────────────"
  
  for model in "${models[@]}"; do
    counter=$((counter + 1))
    echo "  🤖 Testing with $model... ($counter/$total)"
    
    # Create safe filename
    safe_filename=$(echo "$filename" | sed 's/[^a-zA-Z0-9._-]/_/g')
    output_file="ocr_test_results/${safe_filename}_${model}.json"
    
    # Run OCR test
    curl -s -X POST \
      -F "image=@$image" \
      -F "modelId=$model" \
      -F "extractionType=driver_license" \
      http://localhost:3003/api/ocr/analyze > "$output_file"
    
    # Parse and display results
    if [ -f "$output_file" ]; then
      success=$(jq -r '.success' "$output_file" 2>/dev/null)
      cost=$(jq -r '.cost' "$output_file" 2>/dev/null)
      processingTime=$(jq -r '.processingTime' "$output_file" 2>/dev/null)
      
      firstName=$(jq -r '.result.firstName' "$output_file" 2>/dev/null)
      lastName=$(jq -r '.result.lastName' "$output_file" 2>/dev/null)
      licenseNumber=$(jq -r '.result.licenseNumber' "$output_file" 2>/dev/null)
      state=$(jq -r '.result.state' "$output_file" 2>/dev/null)
      
      if [ "$success" = "true" ]; then
        echo "    ✅ Success - Cost: \$${cost}, Time: ${processingTime}ms"
        echo "    📋 Name: $firstName $lastName, License: $licenseNumber, State: $state"
      else
        echo "    ❌ Failed"
      fi
    else
      echo "    ❌ No output file generated"
    fi
    
    # Rate limiting delay
    sleep 2
  done
done

echo ""
echo "🏆 Test completed! Results saved in ocr_test_results/"
echo "📊 Processing results..."

# Generate summary report
echo "# OCR Model Comparison Report" > ocr_test_results/summary.md
echo "Generated: $(date)" >> ocr_test_results/summary.md
echo "" >> ocr_test_results/summary.md

for model in "${models[@]}"; do
  echo "## $model Results" >> ocr_test_results/summary.md
  echo "" >> ocr_test_results/summary.md
  
  for image in "${images[@]}"; do
    filename=$(basename "$image")
    safe_filename=$(echo "$filename" | sed 's/[^a-zA-Z0-9._-]/_/g')
    result_file="ocr_test_results/${safe_filename}_${model}.json"
    
    if [ -f "$result_file" ]; then
      echo "### $filename" >> ocr_test_results/summary.md
      echo '```json' >> ocr_test_results/summary.md
      cat "$result_file" >> ocr_test_results/summary.md
      echo '```' >> ocr_test_results/summary.md
      echo "" >> ocr_test_results/summary.md
    fi
  done
done

echo "✅ Summary report generated: ocr_test_results/summary.md"