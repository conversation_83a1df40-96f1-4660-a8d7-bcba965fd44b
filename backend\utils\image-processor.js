const sharp = require('sharp');
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

// Add constants for file access checks
const fsConstants = require('fs').constants;

class ImageProcessor {
  constructor(options = {}) {
    this.thumbnailSize = options.thumbnailSize || 200;
    this.previewMaxWidth = options.previewMaxWidth || 800;
    this.previewMaxHeight = options.previewMaxHeight || 600;
    this.quality = options.quality || 85;
    this.cacheDir = options.cacheDir || path.join(__dirname, '../../data/cache');
    this.thumbnailDir = options.thumbnailDir || path.join(__dirname, '../../data/thumbnails');
    this.previewDir = options.previewDir || path.join(__dirname, '../../data/previews');
    
    this.initializeDirectories();
  }
  
  async initializeDirectories() {
    const dirs = [this.cacheDir, this.thumbnailDir, this.previewDir];
    for (const dir of dirs) {
      try {
        await fs.mkdir(dir, { recursive: true });
      } catch (error) {
        console.error(`Error creating directory ${dir}:`, error);
      }
    }
  }
  
  // Generate a cache key for an image
  generateCacheKey(imagePath, operation, params = {}) {
    const hash = crypto.createHash('md5');
    hash.update(imagePath);
    hash.update(operation);
    hash.update(JSON.stringify(params));
    return hash.digest('hex');
  }
  
  // Check if cached version exists and is newer than source
  async isCacheValid(cacheKey, sourcePath) {
    try {
      const cacheFile = path.join(this.cacheDir, `${cacheKey}.json`);
      const cacheInfo = JSON.parse(await fs.readFile(cacheFile, 'utf8'));
      
      const sourceStats = await fs.stat(sourcePath);
      const cacheTime = new Date(cacheInfo.created);
      
      return sourceStats.mtime <= cacheTime;
    } catch (error) {
      return false;
    }
  }
  
  // Save cache information
  async saveCacheInfo(cacheKey, info) {
    try {
      const cacheFile = path.join(this.cacheDir, `${cacheKey}.json`);
      await fs.writeFile(cacheFile, JSON.stringify({
        ...info,
        created: new Date().toISOString()
      }));
    } catch (error) {
      console.error('Error saving cache info:', error);
    }
  }
  
  // Get image metadata
  async getImageMetadata(imagePath) {
    try {
      const metadata = await sharp(imagePath).metadata();
      const stats = await fs.stat(imagePath);
      
      return {
        width: metadata.width,
        height: metadata.height,
        format: metadata.format,
        channels: metadata.channels,
        density: metadata.density,
        hasAlpha: metadata.hasAlpha,
        fileSize: stats.size,
        lastModified: stats.mtime,
        created: stats.birthtime
      };
    } catch (error) {
      console.error(`Error getting metadata for ${imagePath}:`, error);
      throw error;
    }
  }
  
  // Generate thumbnail with caching
  async generateThumbnail(imagePath, options = {}) {
    const size = options.size || this.thumbnailSize;
    const quality = options.quality || this.quality;
    const forceRegenerate = options.forceRegenerate || false;
    
    const cacheKey = this.generateCacheKey(imagePath, 'thumbnail', { size, quality });
    const outputPath = path.join(this.thumbnailDir, `${cacheKey}.jpg`);
    
    // Check if cached version exists and is valid (skip if force regeneration)
    if (!forceRegenerate && await this.isCacheValid(cacheKey, imagePath)) {
      try {
        await fs.access(outputPath);
        return {
          path: outputPath,
          url: `/thumbnails/${cacheKey}.jpg`,
          cached: true
        };
      } catch (error) {
        // Cache file doesn't exist, regenerate
      }
    }
    
    try {
      const image = sharp(imagePath);
      
      // Generate thumbnail - NO auto-rotation, load exactly as stored on disk
      await image
        .resize(size, size, {
          fit: 'inside',
          withoutEnlargement: true,
          background: { r: 255, g: 255, b: 255, alpha: 1 }
        })
        .jpeg({ 
          quality,
          progressive: true,
          mozjpeg: true
        })
        .toFile(outputPath);
      
      // Save cache info
      await this.saveCacheInfo(cacheKey, {
        operation: 'thumbnail',
        sourcePath: imagePath,
        outputPath,
        size,
        quality
      });
      
      return {
        path: outputPath,
        url: `/thumbnails/${cacheKey}.jpg`,
        cached: false
      };
    } catch (error) {
      console.error(`Error generating thumbnail for ${imagePath}:`, error);
      throw error;
    }
  }
  
  // Generate preview image with caching
  async generatePreview(imagePath, options = {}) {
    const maxWidth = options.maxWidth || this.previewMaxWidth;
    const maxHeight = options.maxHeight || this.previewMaxHeight;
    const quality = options.quality || this.quality;
    const forceRegenerate = options.forceRegenerate || false;
    
    const cacheKey = this.generateCacheKey(imagePath, 'preview', { maxWidth, maxHeight, quality });
    const outputPath = path.join(this.previewDir, `${cacheKey}.jpg`);
    
    // Check if cached version exists and is valid (skip if force regeneration)
    if (!forceRegenerate && await this.isCacheValid(cacheKey, imagePath)) {
      try {
        await fs.access(outputPath);
        return {
          path: outputPath,
          url: `/previews/${cacheKey}.jpg`,
          cached: true
        };
      } catch (error) {
        // Cache file doesn't exist, regenerate
      }
    }
    
    try {
      const image = sharp(imagePath);
      
      // Generate preview - NO auto-rotation, load exactly as stored on disk
      await image
        .resize(maxWidth, maxHeight, {
          fit: 'inside',
          withoutEnlargement: true,
          background: { r: 255, g: 255, b: 255, alpha: 1 }
        })
        .jpeg({ 
          quality,
          progressive: true,
          mozjpeg: true
        })
        .toFile(outputPath);
      
      // Save cache info
      await this.saveCacheInfo(cacheKey, {
        operation: 'preview',
        sourcePath: imagePath,
        outputPath,
        maxWidth,
        maxHeight,
        quality
      });
      
      return {
        path: outputPath,
        url: `/previews/${cacheKey}.jpg`,
        cached: false
      };
    } catch (error) {
      console.error(`Error generating preview for ${imagePath}:`, error);
      throw error;
    }
  }
  
  // Rotate image in place with robust retry logic for Windows file locking
  async rotateImage(imagePath, degrees, options = {}) {
    const maxRetries = 3;
    let lastError = null;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Attempting to rotate ${imagePath} (attempt ${attempt}/${maxRetries})`);
        
        // Check if file is accessible
        await fs.access(imagePath, fsConstants.R_OK | fsConstants.W_OK);
        
        // Read the original image with retry logic
        let imageBuffer;
        try {
          imageBuffer = await fs.readFile(imagePath);
        } catch (readError) {
          if (attempt < maxRetries && (readError.code === 'EBUSY' || readError.code === 'UNKNOWN')) {
            console.log(`File read failed (attempt ${attempt}), retrying...`);
            await new Promise(resolve => setTimeout(resolve, 200 * attempt)); // Exponential backoff
            continue;
          }
          throw readError;
        }
        
        // CRITICAL: Load image with ZERO auto-rotation - exactly as stored on disk
        const sharpInstance = sharp(imageBuffer, { 
          failOnError: false,
          density: 72
        });
        
        // Get metadata but ignore EXIF orientation completely
        const metadata = await sharpInstance.metadata();
        
        // Apply ONLY the manual rotation degrees - NO EXIF auto-rotation at all
        const rotatedBuffer = await sharpInstance
          .rotate(degrees, { background: { r: 255, g: 255, b: 255, alpha: 1 } })
          .withMetadata({ orientation: 1 }) // Force EXIF orientation to 1 (no rotation)
          .toBuffer();
        
        // Write back to the original file with retry logic
        try {
          // Try to write to a temporary file first, then rename
          const tempPath = imagePath + '.tmp';
          await fs.writeFile(tempPath, rotatedBuffer);
          
          // Ensure the temp file is fully written
          const fd = await fs.open(tempPath, 'r+');
          try {
            await fd.sync();
          } finally {
            await fd.close();
          }
          
          // Rename temp file to original (atomic operation on most systems)
          await fs.rename(tempPath, imagePath);
          
        } catch (writeError) {
          if (attempt < maxRetries && (writeError.code === 'EBUSY' || writeError.code === 'UNKNOWN' || writeError.code === 'EACCES')) {
            console.log(`File write failed (attempt ${attempt}), retrying...`);
            // Clean up temp file if it exists
            try {
              await fs.unlink(imagePath + '.tmp');
            } catch (cleanupError) {
              // Ignore cleanup errors
            }
            await new Promise(resolve => setTimeout(resolve, 300 * attempt)); // Longer delay for write operations
            continue;
          }
          throw writeError;
        }
        
        // Verify the file was written correctly
        const finalStats = await fs.stat(imagePath);
        if (finalStats.size === 0) {
          throw new Error('File was corrupted during write operation');
        }
        
        // Additional delay to ensure file system stability
        await new Promise(resolve => setTimeout(resolve, 150));
        
        // Invalidate cache for this image - aggressive approach
        await this.invalidateCache(imagePath);
        
        // Force immediate regeneration of thumbnail and preview to ensure they're rotated
        try {
          await this.generateThumbnail(imagePath, { forceRegenerate: true });
          await this.generatePreview(imagePath, { forceRegenerate: true });
          console.log(`Regenerated thumbnail and preview for rotated image: ${imagePath}`);
        } catch (regenError) {
          console.warn(`Failed to regenerate thumbnail/preview after rotation: ${regenError.message}`);
        }
        
        console.log(`Successfully rotated ${imagePath} by ${degrees} degrees`);
        return {
          success: true,
          rotated: degrees,
          message: `Image rotated ${degrees} degrees successfully`
        };
        
      } catch (error) {
        lastError = error;
        console.error(`Rotation attempt ${attempt} failed for ${imagePath}:`, error.message);
        
        // If this isn't the last attempt and it's a retryable error, continue
        if (attempt < maxRetries && this.isRetryableError(error)) {
          const delay = 400 * attempt; // Exponential backoff
          console.log(`Waiting ${delay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
        
        // If we've exhausted retries or it's a non-retryable error, break
        break;
      }
    }
    
    // If we get here, all retries failed
    console.error(`All ${maxRetries} rotation attempts failed for ${imagePath}`);
    
    // Provide more specific error messages
    if (lastError.code === 'ENOENT') {
      throw new Error('Image file not found');
    } else if (lastError.code === 'EACCES') {
      throw new Error('Permission denied - file may be locked by another process');
    } else if (lastError.code === 'EBUSY') {
      throw new Error('File is busy - please try again in a moment');
    } else if (lastError.code === 'UNKNOWN') {
      throw new Error('File access error - file may be locked or corrupted');
    } else {
      throw new Error(`Image processing failed after ${maxRetries} attempts: ${lastError.message}`);
    }
  }
  
  // Helper function to determine if an error is retryable
  isRetryableError(error) {
    const retryableCodes = ['EBUSY', 'UNKNOWN', 'EACCES', 'EAGAIN', 'EMFILE', 'ENFILE'];
    return retryableCodes.includes(error.code) || 
           error.message.includes('locked') || 
           error.message.includes('busy') ||
           error.message.includes('access');
  }
  
  // Invalidate cache for a specific image
  async invalidateCache(imagePath) {
    try {
      console.log(`Starting cache invalidation for: ${imagePath}`);
      
      // Use the same base64 imageId approach as the filesystem routes for backwards compatibility
      const imageId = Buffer.from(imagePath).toString('base64');
      
      // Remove legacy thumbnail and preview files (these might exist from old versions)
      const legacyThumbnailPath = path.join(this.thumbnailDir, `${imageId}.jpg`);
      const legacyPreviewPath = path.join(this.previewDir, `${imageId}.jpg`);
      
      try {
        await fs.unlink(legacyThumbnailPath);
        console.log(`Removed legacy thumbnail cache: ${legacyThumbnailPath}`);
      } catch (error) {
        // Legacy thumbnail might not exist
      }
      
      try {
        await fs.unlink(legacyPreviewPath);
        console.log(`Removed legacy preview cache: ${legacyPreviewPath}`);
      } catch (error) {
        // Legacy preview might not exist
      }
      
      // Remove cache files from the current cache system (this is the correct approach)
      const operations = ['thumbnail', 'preview'];
      
      for (const operation of operations) {
        // Generate cache keys with different parameter combinations to catch all possible cached versions
        const parameterCombinations = [
          {}, // Default parameters
          { size: this.thumbnailSize, quality: this.quality }, // Thumbnail with specific params
          { maxWidth: this.previewMaxWidth, maxHeight: this.previewMaxHeight, quality: this.quality }, // Preview with specific params
          { size: 150, quality: 85 }, // Common alternative thumbnail size
          { size: 200, quality: 85 }, // Another common thumbnail size
          { size: 250, quality: 85 }, // Larger thumbnail size
        ];
        
        for (const params of parameterCombinations) {
          const cacheKey = this.generateCacheKey(imagePath, operation, params);
          const cacheFile = path.join(this.cacheDir, `${cacheKey}.json`);
          const outputFile = path.join(
            operation === 'thumbnail' ? this.thumbnailDir : this.previewDir,
            `${cacheKey}.jpg`
          );
          
          // Remove cache metadata files
          try {
            await fs.unlink(cacheFile);
            console.log(`Removed cache metadata: ${cacheFile}`);
          } catch (error) {
            // Cache file might not exist, which is fine
          }
          
          // Remove actual cached image files
          try {
            await fs.unlink(outputFile);
            console.log(`Removed cached ${operation}: ${outputFile}`);
          } catch (error) {
            // Output file might not exist, which is fine
          }
        }
      }
      
      console.log(`Cache invalidation completed for: ${imagePath}`);
    } catch (error) {
      console.error(`Error invalidating cache for ${imagePath}:`, error);
    }
  }
  
  // Batch process images
  async batchProcess(imagePaths, operations = ['thumbnail', 'preview'], options = {}) {
    const concurrency = options.concurrency || 3;
    const results = [];
    
    // Process images in batches to avoid overwhelming the system
    for (let i = 0; i < imagePaths.length; i += concurrency) {
      const batch = imagePaths.slice(i, i + concurrency);
      
      const batchPromises = batch.map(async (imagePath) => {
        const result = { imagePath, operations: {} };
        
        try {
          for (const operation of operations) {
            if (operation === 'thumbnail') {
              result.operations.thumbnail = await this.generateThumbnail(imagePath, options.thumbnail);
            } else if (operation === 'preview') {
              result.operations.preview = await this.generatePreview(imagePath, options.preview);
            } else if (operation === 'metadata') {
              result.operations.metadata = await this.getImageMetadata(imagePath);
            }
          }
          
          result.success = true;
        } catch (error) {
          result.success = false;
          result.error = error.message;
        }
        
        return result;
      });
      
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      // Small delay between batches to prevent system overload
      if (i + concurrency < imagePaths.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    return results;
  }
  
  // Clean up old cache files
  async cleanupCache(maxAge = 7 * 24 * 60 * 60 * 1000) { // 7 days
    const directories = [this.cacheDir, this.thumbnailDir, this.previewDir];
    let cleanedCount = 0;
    
    for (const directory of directories) {
      try {
        const entries = await fs.readdir(directory);
        const now = Date.now();
        
        for (const entry of entries) {
          const filePath = path.join(directory, entry);
          const stats = await fs.stat(filePath);
          
          if (now - stats.mtime.getTime() > maxAge) {
            await fs.unlink(filePath);
            cleanedCount++;
          }
        }
      } catch (error) {
        console.warn(`Error cleaning up cache in: ${directory}`, error.message);
      }
    }
    
    console.log(`Cleaned up ${cleanedCount} old cache files`);
    return cleanedCount;
  }
  
  // Get cache statistics
  async getCacheStats() {
    const directories = [
      { name: 'cache', path: this.cacheDir },
      { name: 'thumbnails', path: this.thumbnailDir },
      { name: 'previews', path: this.previewDir }
    ];
    
    const stats = {};
    
    for (const { name, path: dirPath } of directories) {
      try {
        const entries = await fs.readdir(dirPath);
        let totalSize = 0;
        
        for (const entry of entries) {
          const filePath = path.join(dirPath, entry);
          const fileStats = await fs.stat(filePath);
          totalSize += fileStats.size;
        }
        
        stats[name] = {
          fileCount: entries.length,
          totalSize,
          totalSizeMB: Math.round(totalSize / (1024 * 1024) * 100) / 100
        };
      } catch (error) {
        stats[name] = { error: error.message };
      }
    }
    
    return stats;
  }
  
  // Optimize image for OCR processing
  async optimizeForOCR(imagePath, options = {}) {
    const outputPath = options.outputPath || `${imagePath}.ocr.jpg`;
    
    try {
      const image = sharp(imagePath);
      const metadata = await image.metadata();
      
      // Apply OCR-specific optimizations - NO auto-rotation
      await image
        .resize(null, 1200, { // Resize to optimal height for OCR
          fit: 'inside',
          withoutEnlargement: true
        })
        .normalize() // Normalize contrast
        .sharpen() // Enhance edges
        .gamma(1.2) // Adjust gamma for better text recognition
        .jpeg({ quality: 95 }) // High quality for OCR
        .toFile(outputPath);
      
      return {
        path: outputPath,
        originalSize: { width: metadata.width, height: metadata.height },
        optimized: true
      };
    } catch (error) {
      console.error(`Error optimizing image for OCR: ${imagePath}`, error);
      throw error;
    }
  }
}

module.exports = ImageProcessor;