import { NextRequest, NextResponse } from "next/server";
import { getAppConfig } from "../../../../../config/app-config";

export async function GET(
  request: NextRequest,
  { params }: { params: { imageId: string } }
) {
  try {
    const config = getAppConfig();
    const BACKEND_URL = config.backendUrl;
    const { imageId } = params;

    if (!imageId) {
      return NextResponse.json(
        {
          success: false,
          error: "Image ID is required",
        },
        { status: 400 }
      );
    }

    console.log(
      "🔄 OCR Saved Data (imageId) API Route: Received request for imageId:",
      imageId
    );
    console.log(
      "🎯 OCR Saved Data (imageId) API Route: Forwarding to backend",
      `${BACKEND_URL}/api/ocr/saved-data/${imageId}`
    );

    // Forward the request to the backend
    const response = await fetch(
      `${BACKEND_URL}/api/ocr/saved-data/${imageId}`,
      {
        method: "GET",
        headers: {
          "User-Agent": request.headers.get("User-Agent") || "",
        },
      }
    );

    // Get response data
    const data = await response.text();

    // Return response with same status and headers
    return new NextResponse(data, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("API Proxy Error (ocr/saved-data):", error);
    return NextResponse.json(
      {
        success: false,
        error: "Proxy error: Failed to forward saved data request to backend",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { imageId: string } }
) {
  try {
    const config = getAppConfig();
    const BACKEND_URL = config.backendUrl;
    const { imageId } = params;

    if (!imageId) {
      return NextResponse.json(
        {
          success: false,
          error: "Image ID is required",
        },
        { status: 400 }
      );
    }

    // Get the request body
    const body = await request.text();

    console.log(
      "🔄 OCR Saved Data PUT (imageId) API Route: Received request for imageId:",
      imageId
    );
    console.log(
      "🎯 OCR Saved Data PUT (imageId) API Route: Forwarding to backend",
      `${BACKEND_URL}/api/ocr/saved-data/${imageId}`
    );

    // Forward the request to the backend
    const response = await fetch(
      `${BACKEND_URL}/api/ocr/saved-data/${imageId}`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          "User-Agent": request.headers.get("User-Agent") || "",
        },
        body: body,
      }
    );

    // Get response data
    const data = await response.text();

    // Return response with same status and headers
    return new NextResponse(data, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("API Proxy Error (ocr/saved-data PUT):", error);
    return NextResponse.json(
      {
        success: false,
        error:
          "Proxy error: Failed to forward saved data update request to backend",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
