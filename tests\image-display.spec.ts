import { test, expect } from '@playwright/test';

// Image display functionality tests
test.describe('Image Display Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
  });

  test('should display image thumbnails in OCR Testing Playground', async ({ page }) => {
    // Navigate to OCR Testing page
    await page.click('text=OCR Testing');
    await page.waitForLoadState('networkidle');

    // Wait for the OCR Testing Playground to load
    await expect(page.locator('h1')).toContainText('OCR Testing Playground');

    // Check if the "Select Test Images" section is visible
    const selectImagesSection = page.locator('text=Select Test Images');
    await expect(selectImagesSection).toBeVisible();

    // Wait for images to load (give it some time)
    await page.waitForTimeout(3000);

    // Check if there are any image elements
    const images = page.locator('img');
    const imageCount = await images.count();
    
    console.log(`Found ${imageCount} images on the page`);

    // Check for specific image elements in the test images area
    const testImagesArea = page.locator('text=Select Test Images').locator('..').locator('..');
    const testImages = testImagesArea.locator('img');
    const testImageCount = await testImages.count();

    console.log(`Found ${testImageCount} test images`);

    // Verify that we have at least some images
    expect(testImageCount).toBeGreaterThan(0);

    // Check if images are actually loading (not showing broken image icons)
    for (let i = 0; i < Math.min(testImageCount, 5); i++) {
      const image = testImages.nth(i);
      await expect(image).toBeVisible();
      
      // Check if the image has loaded properly
      const naturalWidth = await image.evaluate((img: HTMLImageElement) => img.naturalWidth);
      const naturalHeight = await image.evaluate((img: HTMLImageElement) => img.naturalHeight);
      
      console.log(`Image ${i}: ${naturalWidth}x${naturalHeight}`);
      
      // A properly loaded image should have dimensions
      expect(naturalWidth).toBeGreaterThan(0);
      expect(naturalHeight).toBeGreaterThan(0);
    }
  });

  test('should test hover preview functionality', async ({ page }) => {
    // Navigate to OCR Testing page
    await page.click('text=OCR Testing');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // Find the test images area
    const testImagesArea = page.locator('text=Select Test Images').locator('..').locator('..');
    const imageContainers = testImagesArea.locator('label');

    if (await imageContainers.count() > 0) {
      const firstImageContainer = imageContainers.first();
      
      // Hover over the first image
      await firstImageContainer.hover();
      
      // Wait for the hover preview to appear
      await page.waitForTimeout(1000);
      
      // Check if a preview image appears on hover
      // The preview should be in an absolute positioned div
      const previewDiv = page.locator('div.absolute').filter({ hasText: /US DL|@huaquan/ });
      
      // Note: The preview might not always be visible due to CSS timing
      // This is more of a smoke test
      console.log('Hover test completed');
    }
  });

  test('should test main app image grid functionality', async ({ page }) => {
    // This test would require creating a project and selecting a folder
    // For now, let's test the project creation flow
    
    // Look for project creation elements
    const createButton = page.getByRole('button', { name: /create.*project/i });
    
    if (await createButton.isVisible()) {
      console.log('Project creation button found - main app image grid test would require project setup');
      // We would need to:
      // 1. Create a project
      // 2. Select a folder with images
      // 3. Test the image grid display
      // 4. Test hover previews in the main app
    }
  });

  test('should verify backend thumbnail endpoints', async ({ page }) => {
    // Test a few known thumbnail URLs
    const testPaths = [
      'C:\\\\Users\\\\<USER>\\\\Downloads\\\\Telegram Desktop\\\\100x us dl w selfie\\\\100x us dl w selfie\\\\combined_to_do\\\\100\\\\i100\\\\US DL 1535.jpeg',
      'C:\\\\Users\\\\<USER>\\\\Downloads\\\\Telegram Desktop\\\\100x us dl w selfie\\\\100x us dl w selfie\\\\combined_to_do\\\\100\\\\i100\\\\@huaquan (81).jpg'
    ];

    for (const testPath of testPaths) {
      // Encode the path to base64
      const base64Path = btoa(testPath);
      const thumbnailUrl = `http://localhost:3003/thumbnails/${base64Path}.jpg`;
      const previewUrl = `http://localhost:3003/previews/${base64Path}.jpg`;

      // Test thumbnail endpoint
      const thumbnailResponse = await page.request.get(thumbnailUrl);
      console.log(`Thumbnail ${thumbnailUrl}: ${thumbnailResponse.status()}`);
      
      // Test preview endpoint  
      const previewResponse = await page.request.get(previewUrl);
      console.log(`Preview ${previewUrl}: ${previewResponse.status()}`);

      // At least one should be successful (200)
      expect(thumbnailResponse.status() === 200 || previewResponse.status() === 200).toBeTruthy();
    }
  });

  test('should verify image URLs are correctly generated', async ({ page }) => {
    // Navigate to OCR Testing page
    await page.click('text=OCR Testing');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // Check the src attributes of images
    const images = page.locator('img');
    const imageCount = await images.count();

    console.log(`Checking ${imageCount} images for correct URLs`);

    for (let i = 0; i < Math.min(imageCount, 5); i++) {
      const image = images.nth(i);
      const src = await image.getAttribute('src');
      
      console.log(`Image ${i} src: ${src}`);
      
      if (src) {
        // Check if it's a thumbnail URL
        if (src.includes('/thumbnails/')) {
          expect(src).toMatch(/^http:\/\/localhost:3003\/thumbnails\/[A-Za-z0-9+/=]+\.jpg$/);
        }
        
        // Check if it's a preview URL
        if (src.includes('/previews/')) {
          expect(src).toMatch(/^http:\/\/localhost:3003\/previews\/[A-Za-z0-9+/=]+\.jpg$/);
        }
      }
    }
  });
});