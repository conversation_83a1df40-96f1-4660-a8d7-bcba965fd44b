import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const backendPort = process.env.BACKEND_PORT || '3003';
    
    console.log('🔄 Smart Analyzer API: Proxying analyze request to backend:', body);
    
    const response = await fetch(`http://localhost:${backendPort}/api/smart-analyzer/analyze`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('❌ Smart Analyzer API: Backend error:', errorData);
      return NextResponse.json(errorData, { status: response.status });
    }

    const data = await response.json();
    console.log('✅ Smart Analyzer API: Backend response:', data);
    
    return NextResponse.json(data);
    
  } catch (error) {
    console.error('❌ Smart Analyzer API: Proxy error:', error);
    return NextResponse.json(
      { error: 'Failed to proxy request to backend', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}