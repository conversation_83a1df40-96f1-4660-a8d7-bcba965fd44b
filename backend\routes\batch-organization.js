const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');
const { promisify } = require('util');
const logger = require('../utils/logger');
const SecurityMiddleware = require('../middleware/security');
const ErrorHandler = require('../middleware/error-handler');
const OCRService = require('../services/ocr-service');

// Initialize OCR service
const ocrService = new OCRService();

// Batch OCR analysis for organization planning
router.post('/analyze', SecurityMiddleware.validateRequestBody(['images', 'countryMode']), ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { images, countryMode = 'us', modelId = 'gpt-4o-mini' } = req.body;
    
    if (!Array.isArray(images) || images.length === 0) {
      return res.status(400).json({ success: false, error: 'images must be a non-empty array' });
    }
    
    logger.info('Starting batch OCR analysis for organization', { 
      imageCount: images.length, 
      countryMode, 
      modelId 
    });
    
    const results = new Map();
    const errors = [];
    let processed = 0;
    
    // Process images sequentially to avoid overwhelming the OCR service
    for (const image of images) {
      try {
        const { id: imageId, path: imagePath } = image;
        
        if (!imageId || !imagePath) {
          errors.push({ imageId, error: 'Missing imageId or imagePath' });
          continue;
        }
        
        // Decode and validate image path
        const decodedPath = Buffer.from(imageId, 'base64').toString();
        const validatedPath = SecurityMiddleware.validatePath(decodedPath);
        
        // Verify file exists
        const stats = await fs.stat(validatedPath);
        if (!stats.isFile()) {
          errors.push({ imageId, error: 'Image file not found' });
          continue;
        }
        
        // Read image file
        const imageBuffer = await fs.readFile(validatedPath);
        
        // Perform OCR analysis
        const ocrResult = await ocrService.processImage(imageBuffer, {
          extractionType: countryMode === 'australian' ? 'australian_license' : 'us_license',
          modelId: modelId,
          includeRawText: true,
          includeSideDetection: true
        });
        
        if (ocrResult && ocrResult.success) {
          // Detect front/back side
          const side = detectLicenseSide(ocrResult.rawText || '', ocrResult.result || {});
          
          results.set(imageId, {
            ...ocrResult.result,
            side: side,
            confidence: ocrResult.confidence || 0.8,
            rawText: ocrResult.rawText,
            processedAt: new Date().toISOString()
          });
        } else {
          errors.push({ 
            imageId, 
            error: `OCR failed: ${ocrResult?.error || 'Unknown error'}` 
          });
        }
        
        processed++;
        
        // Log progress for long-running operations
        if (processed % 10 === 0 || processed === images.length) {
          logger.info('Batch OCR analysis progress', { 
            processed, 
            total: images.length, 
            errors: errors.length 
          });
        }
        
      } catch (error) {
        errors.push({ 
          imageId: image.id, 
          error: `Processing failed: ${error.message}` 
        });
        
        logger.error('Error in batch OCR analysis', { 
          error: error.message, 
          imageId: image.id 
        });
      }
    }
    
    // Convert Map to Object for JSON response
    const resultsObj = Object.fromEntries(results);
    
    logger.info('Batch OCR analysis completed', {
      totalImages: images.length,
      successCount: results.size,
      errorCount: errors.length
    });
    
    res.json({
      success: errors.length === 0,
      message: `OCR analysis completed: ${results.size} successful, ${errors.length} errors`,
      results: resultsObj,
      errors,
      stats: {
        totalImages: images.length,
        successCount: results.size,
        errorCount: errors.length,
        processed
      }
    });
    
  } catch (error) {
    logger.error('Error in batch OCR analysis', { error: error.message });
    throw error;
  }
}));

// Execute organization plan
router.post('/organize', SecurityMiddleware.validateRequestBody(['organizationPlan']), ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { organizationPlan } = req.body;
    
    if (!organizationPlan || !organizationPlan.operations || !Array.isArray(organizationPlan.operations)) {
      return res.status(400).json({ success: false, error: 'Invalid organization plan' });
    }
    
    const { sourceFolder, operations, rules } = organizationPlan;
    
    logger.info('Starting organization execution', { 
      sourceFolder, 
      operationCount: operations.length,
      rulesCount: rules?.length || 0
    });
    
    const results = [];
    const errors = [];
    const createdFolders = new Set();
    
    // Group operations by target folder to optimize folder creation
    const operationsByFolder = new Map();
    for (const operation of operations) {
      const { targetFolder } = operation;
      if (!operationsByFolder.has(targetFolder)) {
        operationsByFolder.set(targetFolder, []);
      }
      operationsByFolder.get(targetFolder).push(operation);
    }
    
    // Execute operations folder by folder
    for (const [targetFolder, folderOperations] of operationsByFolder) {
      try {
        // Validate and create target folder
        const validatedTargetFolder = SecurityMiddleware.validatePath(targetFolder);
        
        if (!createdFolders.has(validatedTargetFolder)) {
          try {
            const stats = await fs.stat(validatedTargetFolder);
            if (!stats.isDirectory()) {
              errors.push({ 
                targetFolder: validatedTargetFolder, 
                error: 'Target path exists but is not a directory' 
              });
              continue;
            }
          } catch (error) {
            // Directory doesn't exist, create it
            await fs.mkdir(validatedTargetFolder, { recursive: true });
            logger.info('Created target folder', { path: validatedTargetFolder });
          }
          createdFolders.add(validatedTargetFolder);
        }
        
        // Execute operations for this folder
        for (const operation of folderOperations) {
          try {
            const { imageId, imagePath, newName, ocrData } = operation;
            
            // Validate source image path
            const validatedImagePath = SecurityMiddleware.validatePath(imagePath);
            
            // Verify source file exists
            const stats = await fs.stat(validatedImagePath);
            if (!stats.isFile()) {
              errors.push({ imageId, error: 'Source image file not found' });
              continue;
            }
            
            // Determine target filename
            const originalFilename = path.basename(validatedImagePath);
            const targetFilename = newName || originalFilename;
            const sanitizedFilename = SecurityMiddleware.sanitizeText(targetFilename);
            const targetPath = path.join(validatedTargetFolder, sanitizedFilename);
            
            // Check if target file already exists
            try {
              await fs.stat(targetPath);
              // File exists, generate unique name
              const ext = path.extname(sanitizedFilename);
              const nameWithoutExt = path.basename(sanitizedFilename, ext);
              let counter = 1;
              let uniquePath = targetPath;
              
              while (true) {
                try {
                  await fs.stat(uniquePath);
                  uniquePath = path.join(validatedTargetFolder, `${nameWithoutExt}_${counter}${ext}`);
                  counter++;
                } catch (error) {
                  break;
                }
              }
              
              // Move to unique path
              await fs.rename(validatedImagePath, uniquePath);
              
              results.push({
                imageId,
                originalPath: validatedImagePath,
                targetPath: uniquePath,
                targetFolder: validatedTargetFolder,
                finalFilename: path.basename(uniquePath),
                wasRenamed: true,
                ocrData,
                success: true
              });
              
            } catch (error) {
              // Target doesn't exist, move directly
              await fs.rename(validatedImagePath, targetPath);
              
              results.push({
                imageId,
                originalPath: validatedImagePath,
                targetPath: targetPath,
                targetFolder: validatedTargetFolder,
                finalFilename: path.basename(targetPath),
                wasRenamed: false,
                ocrData,
                success: true
              });
            }
            
            logger.info('Image organized successfully', { 
              imageId, 
              originalPath: validatedImagePath, 
              targetPath: targetPath || targetPath 
            });
            
          } catch (error) {
            errors.push({ 
              imageId: operation.imageId, 
              error: `Failed to organize: ${error.message}` 
            });
            
            logger.error('Error organizing image', { 
              error: error.message, 
              imageId: operation.imageId 
            });
          }
        }
        
      } catch (error) {
        logger.error('Error processing folder operations', { 
          error: error.message, 
          targetFolder 
        });
        
        // Add errors for all operations in this folder
        for (const operation of folderOperations) {
          errors.push({ 
            imageId: operation.imageId, 
            error: `Folder processing failed: ${error.message}` 
          });
        }
      }
    }
    
    // Generate summary report
    const folderSummary = new Map();
    for (const result of results) {
      const folder = result.targetFolder;
      if (!folderSummary.has(folder)) {
        folderSummary.set(folder, { count: 0, files: [] });
      }
      const summary = folderSummary.get(folder);
      summary.count++;
      summary.files.push(result.finalFilename);
    }
    
    const totalAttempted = operations.length;
    const successCount = results.length;
    const errorCount = errors.length;
    const foldersCreated = createdFolders.size;
    
    logger.info('Organization execution completed', {
      totalAttempted,
      successCount,
      errorCount,
      foldersCreated
    });
    
    res.json({
      success: errorCount === 0,
      message: `Organization completed: ${successCount} files organized into ${foldersCreated} folders, ${errorCount} errors`,
      results,
      errors,
      summary: {
        totalAttempted,
        successCount,
        errorCount,
        foldersCreated,
        folderSummary: Object.fromEntries(folderSummary)
      }
    });
    
  } catch (error) {
    logger.error('Error in organization execution', { error: error.message });
    throw error;
  }
}));

// Helper function to detect license side
function detectLicenseSide(ocrText, extractedData) {
  const text = ocrText.toLowerCase();
  
  // Strong indicators for front side
  const frontIndicators = [
    'driver license', 'driver licence', 'driving license', 'driving licence',
    'date of birth', 'dob', 'address', 'height', 'weight', 'sex', 'eyes',
    'photo', 'picture', 'restrictions', 'class', 'endorsements'
  ];
  
  // Strong indicators for back side  
  const backIndicators = [
    'organ donor', 'veteran', 'conditions', 'restrictions',
    'magnetic stripe', 'barcode', '2d barcode', 'pdf417',
    'emergency contact', 'medical information', 'veteran status'
  ];
  
  // Check for specific patterns
  const frontScore = frontIndicators.reduce((score, indicator) => 
    text.includes(indicator) ? score + 1 : score, 0);
  const backScore = backIndicators.reduce((score, indicator) => 
    text.includes(indicator) ? score + 1 : score, 0);
  
  // Additional heuristics
  const hasPersonalInfo = !!(extractedData.firstName || extractedData.lastName || extractedData.dateOfBirth);
  const hasLicenseNumber = !!extractedData.licenseNumber;
  
  if (frontScore > backScore || (hasPersonalInfo && hasLicenseNumber)) {
    return 'front';
  } else if (backScore > frontScore) {
    return 'back';
  }
  
  return 'unknown';
}

// Get organization suggestions based on folder content
router.post('/suggest-rules', SecurityMiddleware.validateRequestBody(['sourceFolder']), ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { sourceFolder, sampleSize = 10 } = req.body;
    
    const validatedPath = SecurityMiddleware.validatePath(sourceFolder);
    
    // Verify folder exists
    const stats = await fs.stat(validatedPath);
    if (!stats.isDirectory()) {
      return res.status(400).json({ success: false, error: 'Path is not a directory' });
    }
    
    // Get sample of images for analysis
    const files = await fs.readdir(validatedPath);
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff'];
    const imageFiles = files
      .filter(file => imageExtensions.includes(path.extname(file).toLowerCase()))
      .slice(0, sampleSize)
      .map(file => path.join(validatedPath, file));
    
    if (imageFiles.length === 0) {
      return res.json({
        success: true,
        suggestions: [],
        message: 'No images found in the folder'
      });
    }
    
    // Analyze sample images to determine patterns
    const patterns = {
      states: new Set(),
      hasMultipleStates: false,
      hasExpirationYears: new Set(),
      avgConfidence: 0,
      documentTypes: new Set()
    };
    
    let analyzedCount = 0;
    
    for (const imagePath of imageFiles.slice(0, 3)) { // Limit to 3 for quick analysis
      try {
        const imageBuffer = await fs.readFile(imagePath);
        const ocrResult = await ocrService.processImage(imageBuffer, {
          extractionType: 'us_license', // Default to US for pattern detection
          includeRawText: false
        });
        
        if (ocrResult && ocrResult.success && ocrResult.result) {
          const data = ocrResult.result;
          
          if (data.state) patterns.states.add(data.state);
          if (data.expirationDate) {
            const year = new Date(data.expirationDate).getFullYear();
            if (!isNaN(year)) patterns.hasExpirationYears.add(year);
          }
          patterns.avgConfidence += ocrResult.confidence || 0.8;
          patterns.documentTypes.add('driver_license');
          analyzedCount++;
        }
      } catch (error) {
        logger.warn('Error analyzing sample image for suggestions', { 
          error: error.message, 
          imagePath 
        });
      }
    }
    
    if (analyzedCount > 0) {
      patterns.avgConfidence /= analyzedCount;
      patterns.hasMultipleStates = patterns.states.size > 1;
    }
    
    // Generate rule suggestions based on patterns
    const suggestions = [];
    
    // Basic person-based grouping (always recommended)
    suggestions.push({
      id: 'by-person',
      name: 'Group by Person',
      pattern: '{lastName}-{firstName}',
      description: 'Creates folders named: Smith-John, Doe-Jane',
      priority: 'high',
      reason: 'Basic organization by person name'
    });
    
    // State-based grouping if multiple states detected
    if (patterns.hasMultipleStates) {
      suggestions.push({
        id: 'by-state',
        name: 'Group by State',
        pattern: '{state}/{lastName}-{firstName}',
        description: 'Creates state folders with person subfolders',
        priority: 'medium',
        reason: `Multiple states detected: ${Array.from(patterns.states).join(', ')}`
      });
    }
    
    // Expiration-based grouping if multiple years detected
    if (patterns.hasExpirationYears.size > 1) {
      suggestions.push({
        id: 'by-expiration',
        name: 'Group by Expiration Year',
        pattern: 'Expires-{expirationYear}/{lastName}-{firstName}',
        description: 'Groups by license expiration year',
        priority: 'low',
        reason: `Multiple expiration years detected: ${Array.from(patterns.hasExpirationYears).join(', ')}`
      });
    }
    
    // Date-based grouping for large collections
    if (imageFiles.length > 50) {
      suggestions.push({
        id: 'chronological',
        name: 'Chronological',
        pattern: '{year}/{month}-{day}/{lastName}-{firstName}',
        description: 'Organizes by processing date',
        priority: 'low',
        reason: `Large collection detected (${imageFiles.length} images)`
      });
    }
    
    res.json({
      success: true,
      suggestions,
      patterns: {
        totalImages: imageFiles.length,
        analyzedImages: analyzedCount,
        uniqueStates: Array.from(patterns.states),
        expirationYears: Array.from(patterns.hasExpirationYears),
        avgConfidence: patterns.avgConfidence
      }
    });
    
  } catch (error) {
    logger.error('Error generating organization suggestions', { error: error.message });
    throw error;
  }
}));

module.exports = router;