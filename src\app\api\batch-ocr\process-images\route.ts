import { NextRequest, NextResponse } from 'next/server'
import { getAppConfig } from '@/config/app-config'

interface BatchImageRequest {
  id: string;
  filename: string;
  path: string;
}

interface ReadySearchOptions {
  enabled: boolean;
  useFullGivenNames?: boolean;
  includeBirthYear?: boolean;
}

interface BatchProcessRequest {
  images: BatchImageRequest[];
  modelId?: string;
  extractionType?: string;
  mode?: string;
  exportFormats?: string[];
  readySearchOptions?: ReadySearchOptions;
}

export async function POST(request: NextRequest) {
  try {
    const config = getAppConfig();
    const BACKEND_URL = config.backend.url;

    console.log('🔄 Batch OCR API Route: Received batch processing request');

    // Parse request body
    const body: BatchProcessRequest = await request.json();
    const { 
      images, 
      modelId = 'gpt-4o-mini', 
      extractionType = 'us_driver_license',
      mode = 'us',
      exportFormats = ['json', 'txt'],
      readySearchOptions = { enabled: false }
    } = body;

    if (!images || !Array.isArray(images) || images.length === 0) {
      return NextResponse.json(
        { error: 'Images array is required and must not be empty' },
        { status: 400 }
      );
    }

    console.log(`📤 Batch OCR API Route: Processing ${images.length} images`);
    console.log('🎯 Batch OCR API Route: Forwarding to backend', `${BACKEND_URL}/api/batch-ocr/process-images`);

    // Forward the request to the backend
    const response = await fetch(`${BACKEND_URL}/api/batch-ocr/process-images`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': request.headers.get('User-Agent') || '',
      },
      body: JSON.stringify({
        images,
        modelId,
        extractionType,
        mode,
        exportFormats,
        readySearchOptions
      }),
    });

    console.log('📥 Batch OCR API Route: Backend response status:', response.status);

    // Get response data
    const data = await response.text();
    
    console.log('📥 Batch OCR API Route: Response preview:', data.substring(0, 200) + '...');

    // Return response with same status and headers
    return new NextResponse(data, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        'Content-Type': response.headers.get('Content-Type') || 'application/json',
      },
    });

  } catch (error) {
    console.error('❌ Batch OCR API Route Error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process batch OCR request',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
}
