"use client"

import React, { useState, useEffect, use<PERSON><PERSON>back, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { 
  Play, 
  Download, 
  RefreshCw, 
  Eye, 
  DollarSign, 
  Clock, 
  Target, 
  AlertCircle,
  CheckCircle,
  ArrowLeft,
  Plus,
  Minus,
  X,
  Image as ImageIcon,
  FileText,
  Settings,
  BarChart3,
  BarChart,
  TestTube,
  Folder,
  Upload,
  Search,
  Filter,
  Save,
  Copy,
  ChevronDown,
  ChevronRight,
  Trash2,
  Calendar,
  Shield
} from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { OCRResult } from '@/types'
import { cn } from '@/lib/utils'
import { ModelValidationPanel } from '@/components/model-validation/model-validation-panel'
import { useSettingsStandalone } from '@/hooks/use-settings-sync'

// Enhanced interfaces for the testing playground
interface TestImage {
  id: string
  originalId?: string // For API calls when composite ID is used
  filename: string
  path: string
  folder: string
  project?: string
  thumbnailUrl: string
  previewUrl: string
  lastModified: Date
  fileSize: number
}

interface CustomModel {
  id: string
  name: string
  provider: string
  costPer1k?: number
  description?: string
  tier?: 'free' | 'paid' | 'premium'
  isRecommended?: boolean
  contextWindow?: number
  maxOutputTokens?: number
}

interface TestPrompt {
  id: string
  name: string
  content: string
  description: string
  category: string
  mode?: string
}

interface ModelTestResult {
  modelId: string
  modelName: string
  promptId: string
  promptName: string
  result: OCRResult | null
  processingTime: number
  cost: number
  success: boolean
  error?: string
  rawResponse?: string
  confidence?: number
  timestamp: Date
}

interface TestSession {
  id: string
  name: string
  timestamp: string
  imageId: string
  imageName: string
  selectedModels: string[]
  selectedPrompts: string[]
  results: ModelTestResult[]
  totalCost: number
  totalTime: number
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
}

interface ComparisonAnalysis {
  bestModel: string
  bestPrompt: string
  avgAccuracy: number
  avgCost: number
  avgTime: number
  fieldComparison: Record<string, Record<string, number>>
}

// Available vision models with enhanced metadata
const AVAILABLE_MODELS = [
  // Free models
  { 
    id: 'google/gemini-flash-1.5', 
    name: 'Gemini Flash 1.5', 
    provider: 'Google', 
    costPer1k: 0, 
    tier: 'free',
    description: 'Fast and efficient vision model',
    contextWindow: 1000000,
    maxOutputTokens: 8192,
    isRecommended: true
  },
  { 
    id: 'meta-llama/llama-3.2-11b-vision-instruct', 
    name: 'Llama 3.2 11B Vision', 
    provider: 'Meta', 
    costPer1k: 0, 
    tier: 'free',
    description: 'Meta\'s vision-capable language model',
    contextWindow: 131072,
    maxOutputTokens: 2048
  },
  // Paid models
  { 
    id: 'openai/gpt-4o', 
    name: 'GPT-4o', 
    provider: 'OpenAI', 
    costPer1k: 15.0, 
    tier: 'paid',
    description: 'Most capable multimodal model',
    contextWindow: 128000,
    maxOutputTokens: 4096,
    isRecommended: true
  },
  { 
    id: 'openai/gpt-4o-mini', 
    name: 'GPT-4o Mini', 
    provider: 'OpenAI', 
    costPer1k: 0.15, 
    tier: 'paid',
    description: 'Affordable multimodal model',
    contextWindow: 128000,
    maxOutputTokens: 16384
  },
  { 
    id: 'anthropic/claude-3.5-sonnet', 
    name: 'Claude 3.5 Sonnet', 
    provider: 'Anthropic', 
    costPer1k: 3.0, 
    tier: 'paid',
    description: 'Excellent vision and reasoning',
    contextWindow: 200000,
    maxOutputTokens: 4096
  },
  { 
    id: 'google/gemini-pro-1.5', 
    name: 'Gemini Pro 1.5', 
    provider: 'Google', 
    costPer1k: 7.0, 
    tier: 'paid',
    description: 'Large context window for complex documents',
    contextWindow: 2000000,
    maxOutputTokens: 8192
  },
  { 
    id: 'anthropic/claude-3-5-haiku', 
    name: 'Claude 3.5 Haiku', 
    provider: 'Anthropic', 
    costPer1k: 0.25, 
    tier: 'paid',
    description: 'Fast and efficient vision model',
    contextWindow: 200000,
    maxOutputTokens: 4096
  }
]

// Default test prompts will be loaded from API
let DEFAULT_PROMPTS: TestPrompt[] = []

// Generate truly unique IDs for React keys
let uniqueIdCounter = 0
const generateUniqueId = () => `unique-${Date.now()}-${Math.random().toString(36).substr(2, 9)}-${++uniqueIdCounter}`

export default function EnhancedOCRTestingPlayground() {
  // Settings integration
  const {
    openRouterConfig,
    isLoading: isSettingsLoading,
    loadSettings
  } = useSettingsStandalone()
  // State management
  const [testSessions, setTestSessions] = useState<TestSession[]>([])
  const [currentSession, setCurrentSession] = useState<TestSession | null>(null)
  const [isRunning, setIsRunning] = useState(false)
  const [expandedSessions, setExpandedSessions] = useState<Set<string>>(new Set())
  const [selectedModels, setSelectedModels] = useState<string[]>([])
  const [selectedPrompts, setSelectedPrompts] = useState<string[]>([])
  const [actualPrompts, setActualPrompts] = useState<TestPrompt[]>([])
  const [isLoadingPrompts, setIsLoadingPrompts] = useState(false)
  const [isLoadingOpenRouterModels, setIsLoadingOpenRouterModels] = useState(false)
  const [openRouterModels, setOpenRouterModels] = useState<any[]>([])
  const [showModelManager, setShowModelManager] = useState(false)
  const [modelSearchTerm, setModelSearchTerm] = useState('')
  const [selectedImages, setSelectedImages] = useState<string[]>([])
  const [availableImages, setAvailableImages] = useState<TestImage[]>([])
  const [customModels, setCustomModels] = useState<CustomModel[]>([])
  const [customPrompts, setCustomPrompts] = useState<TestPrompt[]>([])
  const [error, setError] = useState<string | null>(null)
  const [editingPrompt, setEditingPrompt] = useState<TestPrompt | null>(null)
  const [showPromptEditor, setShowPromptEditor] = useState(false)
  const [newPrompt, setNewPrompt] = useState<Partial<TestPrompt>>({
    name: '',
    category: 'Custom',
    description: '',
    content: '',
    mode: 'us'
  })
  const [searchTerm, setSearchTerm] = useState('')
  const [filterProject, setFilterProject] = useState<string>('all')
  const [expandedPrompts, setExpandedPrompts] = useState<Set<string>>(new Set())
  const [editingPrompts, setEditingPrompts] = useState<Set<string>>(new Set())
  const [promptEditContent, setPromptEditContent] = useState<Record<string, string>>({})
  const [userProjects, setUserProjects] = useState<any[]>([])
  const [costStats, setCostStats] = useState<any>(null)
  const [isLoadingImages, setIsLoadingImages] = useState(false)
  const [isLoadingProjects, setIsLoadingProjects] = useState(false)
  
  // Prevent double initialization in React Strict Mode
  const initializationRef = useRef(false)

  // Function declarations (moved before useEffect hooks to avoid TypeScript hoisting errors)
  const loadImagesFromProjects = useCallback(async (projects: any[]) => {
    if (isLoadingImages) {
      console.log('Image loading already in progress, skipping...')
      return
    }
    
    setIsLoadingImages(true)
    const allImages: TestImage[] = []
    
    try {
      for (const project of projects) {
        try {
          // Scan project folders
          const scanResponse = await fetch('/api/filesystem/scan', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
              rootPath: project.rootPath,
              maxDepth: 3,
              includeStats: true
            })
          })
          
          if (scanResponse.ok) {
            const scanData = await scanResponse.json()
            
            // Load images from each folder
            for (const folder of scanData.folders || []) {
              if (folder.imageCount > 0) {
                try {
                  const imagesResponse = await fetch('/api/filesystem/images', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                      folderPath: folder.path,
                      generateThumbnails: true
                    })
                  })
                  
                  if (imagesResponse.ok) {
                    const imagesData = await imagesResponse.json()
                    
                    const projectImages: TestImage[] = imagesData.images.map((img: any, index: number) => ({
                      id: generateUniqueId(), // Generate truly unique ID for React keys
                      originalId: img.id, // Keep original ID for API calls
                      filename: img.filename,
                      path: img.path,
                      folder: folder.name,
                      project: project.name,
                      thumbnailUrl: `http://localhost:3003${img.thumbnailUrl || `/thumbnails/${img.id}.jpg`}`,
                      previewUrl: `http://localhost:3003${img.previewUrl || `/previews/${img.id}.jpg`}`,
                      lastModified: new Date(img.lastModified),
                      fileSize: img.fileSize || 0
                    }))
                    
                    console.log(`Loaded ${projectImages.length} images from ${project.name}/${folder.name}`, projectImages.slice(0, 2).map(img => ({ id: img.id, originalId: img.originalId, filename: img.filename })))
                    
                    allImages.push(...projectImages)
                  }
                } catch (folderError) {
                  console.warn(`Failed to load images from folder ${folder.path}:`, folderError)
                }
              }
            }
          }
        } catch (projectError) {
          console.warn(`Failed to scan project ${project.name}:`, projectError)
        }
      }
      
      // Deduplicate images by path to prevent duplicates from multiple loading calls
      const uniqueImages = allImages.reduce((acc: TestImage[], current) => {
        const exists = acc.find(img => img.path === current.path && img.project === current.project)
        if (!exists) {
          acc.push(current)
        }
        return acc
      }, [])
      
      console.log(`Loaded ${allImages.length} images, deduplicated to ${uniqueImages.length} unique images`)
      
      // Only update state if images actually changed to prevent unnecessary re-renders
      setAvailableImages(prevImages => {
        if (prevImages.length !== uniqueImages.length) {
          return uniqueImages
        }
        // Check if images are actually different (basic check by first few filenames)
        const prevNames = prevImages.slice(0, 5).map(img => img.filename).join(',')
        const newNames = uniqueImages.slice(0, 5).map(img => img.filename).join(',')
        if (prevNames !== newNames) {
          return uniqueImages
        }
        console.log('Images unchanged, keeping existing state')
        return prevImages
      })
    } catch (error) {
      console.error('Error loading images from projects:', error)
      setError('Failed to load images from projects')
    } finally {
      setIsLoadingImages(false)
    }
  }, [isLoadingImages])

  const loadUserProjects = useCallback(async () => {
    setIsLoadingProjects(true)
    try {
      // Load projects from localStorage first
      const storedProjects = localStorage.getItem('dl-organizer-projects-v1')
      if (storedProjects) {
        const projects = JSON.parse(storedProjects)
        setUserProjects(projects)
        
        // Load images from all projects
        await loadImagesFromProjects(projects)
      }
      
      // Also try to load from API
      try {
        const response = await fetch('/api/projects')
        if (response.ok) {
          const apiData = await response.json()
          if (apiData.success && apiData.data) {
            const apiProjects = apiData.data.map((project: any) => ({
              id: project.id,
              name: project.name,
              rootPath: project.root_path,
              createdAt: project.created_at,
              updatedAt: project.updated_at
            }))
            setUserProjects(apiProjects)
            await loadImagesFromProjects(apiProjects)
          }
        }
      } catch (apiError) {
        console.warn('Failed to load projects from API:', apiError)
      }
    } catch (error) {
      console.error('Error loading projects:', error)
      setError('Failed to load user projects')
    } finally {
      setIsLoadingProjects(false)
    }
  }, [loadImagesFromProjects])

  const loadActualPrompts = useCallback(async () => {
    setIsLoadingPrompts(true)
    try {
      console.log('🎯 Loading OCR prompts from backend...')
      const response = await fetch('/api/ocr/prompts')
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: Failed to load OCR prompts`)
      }
      
      const data = await response.json()
      console.log('📋 Backend prompts response:', data)
      
      if (data.success && data.prompts && Array.isArray(data.prompts)) {
        // Transform backend prompts to frontend format
        const transformedPrompts = data.prompts.map((prompt: any) => ({
          id: prompt.id,
          name: prompt.name,
          content: prompt.content,
          description: prompt.description,
          category: prompt.category || 'System',
          mode: prompt.mode || 'us',
          extractionType: prompt.extractionType || 'driver_license'
        }))
        
        console.log(`✅ Loaded ${transformedPrompts.length} OCR prompts:`, transformedPrompts.map((p: TestPrompt) => p.name))
        setActualPrompts(transformedPrompts)
        DEFAULT_PROMPTS = transformedPrompts // Update global array
        
        // Set default selected prompts if none selected
        if (selectedPrompts.length === 0 && transformedPrompts.length > 0) {
          const defaultPrompt = transformedPrompts.find((p: TestPrompt) => p.id === 'us-driver-license') || transformedPrompts[0]
          setSelectedPrompts([defaultPrompt.id])
          console.log(`🎯 Auto-selected default prompt: ${defaultPrompt.name}`)
        }
      } else {
        console.warn('⚠️ No OCR prompts found in response:', data)
        setActualPrompts([])
      }
    } catch (error) {
      console.error('❌ Error loading OCR prompts:', error)
      setError(error instanceof Error ? error.message : 'Failed to load OCR prompts')
      setActualPrompts([])
    } finally {
      setIsLoadingPrompts(false)
    }
  }, [selectedPrompts.length])

  // Load user projects on component mount
  useEffect(() => {
    const initialize = async () => {
      if (initializationRef.current) {
        console.log('Initialization already completed, skipping...')
        return
      }
      
      initializationRef.current = true
      console.log('Starting OCR playground initialization...')
      
      await Promise.all([
        loadSettings(),
        loadActualPrompts(),
        loadUserProjects()
      ])
      loadPreviousSessions()
      loadCustomSettings()
    }
    
    initialize()
  }, [loadActualPrompts, loadSettings, loadUserProjects])

  // Update available models when settings change
  useEffect(() => {
    if (openRouterConfig.selectedModel && !selectedModels.includes(openRouterConfig.selectedModel)) {
      setSelectedModels([openRouterConfig.selectedModel])
    }
  }, [openRouterConfig.selectedModel, selectedModels])

  // Listen for settings updates
  useEffect(() => {
    const handleSettingsUpdate = () => {
      loadSettings()
      loadActualPrompts() // Reload prompts when settings change
    }

    window.addEventListener('settings-updated', handleSettingsUpdate)
    return () => {
      window.removeEventListener('settings-updated', handleSettingsUpdate)
    }
  }, [loadActualPrompts, loadSettings])

  const loadPreviousSessions = () => {
    try {
      const saved = localStorage.getItem('enhanced-ocr-test-sessions')
      if (saved) {
        setTestSessions(JSON.parse(saved))
      }
    } catch (error) {
      console.error('Error loading previous sessions:', error)
    }
  }


  // Load OpenRouter models
  const loadOpenRouterModels = useCallback(async () => {
    if (!openRouterConfig.apiKey) {
      setError('OpenRouter API key required. Please configure in settings.')
      return
    }

    setIsLoadingOpenRouterModels(true)
    try {
      const response = await fetch('https://openrouter.ai/api/v1/models', {
        headers: {
          'Authorization': `Bearer ${openRouterConfig.apiKey}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch OpenRouter models')
      }

      const data = await response.json()
      const visionModels = data.data.filter((model: any) => 
        model.architecture?.modality?.includes('vision') ||
        model.name.toLowerCase().includes('vision') ||
        model.name.toLowerCase().includes('4o') ||
        model.name.toLowerCase().includes('claude') ||
        model.name.toLowerCase().includes('gemini')
      )
      
      setOpenRouterModels(visionModels)
    } catch (error) {
      console.error('Error loading OpenRouter models:', error)
      setError(error instanceof Error ? error.message : 'Failed to load OpenRouter models')
    } finally {
      setIsLoadingOpenRouterModels(false)
    }
  }, [openRouterConfig.apiKey])

  // Add model to available models
  const addModelToList = (model: any) => {
    // Handle different pricing formats from OpenRouter API
    let costPer1k = 0
    if (model.pricing?.prompt) {
      // If pricing is already per 1K tokens
      if (model.pricing.prompt > 1) {
        costPer1k = model.pricing.prompt
      } else {
        // If pricing is per token, multiply by 1000
        costPer1k = model.pricing.prompt * 1000
      }
    }
    
    const newModel: CustomModel = {
      id: model.id,
      name: model.name,
      provider: 'OpenRouter',
      costPer1k: costPer1k,
      description: model.description || `${model.name} via OpenRouter`
    }
    
    // Check if model already exists
    const exists = [...AVAILABLE_MODELS, ...customModels].some(m => m.id === model.id)
    if (!exists) {
      setCustomModels([...customModels, newModel])
      saveCustomSettings()
    }
  }

  // Remove model from available models
  const removeModelFromList = (modelId: string) => {
    const updatedModels = customModels.filter(m => m.id !== modelId)
    setCustomModels(updatedModels)
    
    // Also remove from selected models if selected
    setSelectedModels(selectedModels.filter(id => id !== modelId))
    
    saveCustomSettings()
  }

  const filteredOpenRouterModels = openRouterModels.filter(model =>
    model.name.toLowerCase().includes(modelSearchTerm.toLowerCase()) ||
    model.id.toLowerCase().includes(modelSearchTerm.toLowerCase())
  )

  const loadCustomSettings = () => {
    try {
      const savedModels = localStorage.getItem('custom-ocr-models')
      if (savedModels) {
        setCustomModels(JSON.parse(savedModels))
      }
      
      const savedPrompts = localStorage.getItem('custom-ocr-prompts')
      if (savedPrompts) {
        setCustomPrompts(JSON.parse(savedPrompts))
      }
    } catch (error) {
      console.error('Error loading custom settings:', error)
    }
  }

  const saveCustomSettings = () => {
    localStorage.setItem('custom-ocr-models', JSON.stringify(customModels))
    localStorage.setItem('custom-ocr-prompts', JSON.stringify(customPrompts))
  }

  const getAllModels = () => [...AVAILABLE_MODELS, ...customModels]
  const getAllPrompts = () => [...actualPrompts, ...customPrompts]

  // Prompt management functions
  const savePrompt = (prompt: TestPrompt) => {
    if (editingPrompt) {
      // Update existing prompt
      const updatedPrompts = customPrompts.map(p => 
        p.id === prompt.id ? prompt : p
      )
      setCustomPrompts(updatedPrompts)
      localStorage.setItem('custom-ocr-prompts', JSON.stringify(updatedPrompts))
    } else {
      // Add new prompt
      const newPromptWithId = {
        ...prompt,
        id: `custom-${Date.now()}`
      }
      const updatedPrompts = [...customPrompts, newPromptWithId]
      setCustomPrompts(updatedPrompts)
      localStorage.setItem('custom-ocr-prompts', JSON.stringify(updatedPrompts))
    }
    
    setEditingPrompt(null)
    setShowPromptEditor(false)
    setNewPrompt({
      name: '',
      category: 'Custom',
      description: '',
      content: '',
      mode: 'us'
    })
  }

  const editPrompt = (prompt: TestPrompt) => {
    setEditingPrompt(prompt)
    setNewPrompt(prompt)
    setShowPromptEditor(true)
  }

  const deletePrompt = (promptId: string) => {
    if (confirm('Are you sure you want to delete this prompt?')) {
      const updatedPrompts = customPrompts.filter(p => p.id !== promptId)
      setCustomPrompts(updatedPrompts)
      localStorage.setItem('custom-ocr-prompts', JSON.stringify(updatedPrompts))
      
      // Remove from selected if it was selected
      setSelectedPrompts(selectedPrompts.filter(id => id !== promptId))
    }
  }

  const duplicatePrompt = (prompt: TestPrompt) => {
    const duplicatedPrompt = {
      ...prompt,
      id: `custom-${Date.now()}`,
      name: `${prompt.name} (Copy)`,
      category: 'Custom'
    }
    const updatedPrompts = [...customPrompts, duplicatedPrompt]
    setCustomPrompts(updatedPrompts)
    localStorage.setItem('custom-ocr-prompts', JSON.stringify(updatedPrompts))
  }

  const runOCRTest = async (
    imageId: string, 
    modelId: string, 
    promptId: string
  ): Promise<ModelTestResult> => {
    const image = availableImages.find(img => img.id === imageId)
    const model = getAllModels().find(m => m.id === modelId)
    const prompt = getAllPrompts().find(p => p.id === promptId)
    
    if (!image || !model || !prompt) {
      throw new Error(`Missing image, model, or prompt`)
    }

    const startTime = Date.now()

    try {
      // Use the analyze-by-path endpoint
      console.log('🔍 OCR Test Debug:', {
        imageId: image.id,
        originalId: image.originalId,
        filename: image.filename,
        modelId,
        promptId: prompt.id
      })

      const requestPayload = {
        imagePath: image.originalId || image.id, // Use originalId for API calls if available
        modelId: modelId,
        extractionType: 'driver_license',
        mode: openRouterConfig.ocrMode, // Use actual mode from settings
        cardSide: null,
        forceRefresh: true,
        customPrompt: prompt.content // Add custom prompt support
      }

      console.log('📤 OCR Request Payload:', requestPayload)

      const response = await fetch('/api/ocr/analyze-by-path', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestPayload)
      })

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`
        try {
          const errorData = await response.json()
          errorMessage = errorData.error || errorData.message || errorMessage
        } catch (parseError) {
          console.warn('Failed to parse error response:', parseError)
        }
        throw new Error(errorMessage)
      }

      const data = await response.json()
      console.log('📥 OCR API Response:', {
        success: data.success,
        hasResult: !!data.result,
        error: data.error,
        modelUsed: data.modelUsed,
        cost: data.cost,
        resultPreview: data.result ? {
          firstName: data.result.firstName,
          lastName: data.result.lastName
        } : null
      })
      
      const processingTime = Date.now() - startTime

      return {
        modelId,
        modelName: model.name,
        promptId,
        promptName: prompt.name,
        result: data.result || null,
        processingTime,
        cost: data.cost || 0,
        success: data.success !== false,
        error: data.error,
        rawResponse: data.rawResponse || JSON.stringify(data, null, 2), // Store full raw response
        confidence: data.result?.confidence || 0,
        timestamp: new Date()
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      console.error(`❌ Model test failed for ${model.name} with ${prompt.name}:`, errorMessage)
      
      return {
        modelId,
        modelName: model.name,
        promptId,
        promptName: prompt.name,
        result: null,
        processingTime: Date.now() - startTime,
        cost: 0,
        success: false,
        error: errorMessage,
        rawResponse: `Error: ${errorMessage}`,
        confidence: 0,
        timestamp: new Date()
      }
    }
  }

  const runTestSession = async () => {
    if (selectedImages.length === 0 || selectedModels.length === 0 || selectedPrompts.length === 0) {
      setError('Please select at least one image, model, and prompt')
      return
    }

    if (selectedModels.length > 5) {
      setError('Maximum 5 models can be tested simultaneously')
      return
    }

    const sessionId = `session-${Date.now()}`
    const session: TestSession = {
      id: sessionId,
      name: `Test Session ${new Date().toLocaleString()}`,
      timestamp: new Date().toISOString(),
      imageId: selectedImages[0], // For now, test first selected image
      imageName: availableImages.find(img => img.id === selectedImages[0])?.filename || 'Unknown',
      selectedModels: [...selectedModels],
      selectedPrompts: [...selectedPrompts],
      results: [],
      totalCost: 0,
      totalTime: 0,
      status: 'running',
      progress: 0
    }

    setCurrentSession(session)
    setIsRunning(true)
    setError(null)

    const totalTests = selectedImages.length * selectedModels.length * selectedPrompts.length
    let completedTests = 0

    try {
      for (const imageId of selectedImages) {
        for (const modelId of selectedModels) {
          for (const promptId of selectedPrompts) {
            try {
              const result = await runOCRTest(imageId, modelId, promptId)
              session.results.push(result)
              session.totalCost += result.cost
              session.totalTime += result.processingTime
              
              completedTests++
              session.progress = (completedTests / totalTests) * 100
              
              setCurrentSession({ ...session })
              
              // Rate limiting delay
              await new Promise(resolve => setTimeout(resolve, 1000))
            } catch (testError) {
              console.error(`Test failed for ${imageId} with ${modelId} and ${promptId}:`, testError)
              
              const model = getAllModels().find(m => m.id === modelId)
              const prompt = getAllPrompts().find(p => p.id === promptId)
              
              session.results.push({
                modelId,
                modelName: model?.name || modelId,
                promptId,
                promptName: prompt?.name || promptId,
                result: null,
                processingTime: 0,
                cost: 0,
                success: false,
                error: testError instanceof Error ? testError.message : 'Unknown error',
                timestamp: new Date()
              })
              
              completedTests++
              session.progress = (completedTests / totalTests) * 100
              setCurrentSession({ ...session })
            }
          }
        }
      }

      session.status = 'completed'
      session.progress = 100

      // Save session
      const updatedSessions = [...testSessions, session]
      setTestSessions(updatedSessions)
      localStorage.setItem('enhanced-ocr-test-sessions', JSON.stringify(updatedSessions))

    } catch (error) {
      session.status = 'failed'
      setError(error instanceof Error ? error.message : 'Test session failed')
    } finally {
      setIsRunning(false)
      setCurrentSession(session)
    }
  }

  const calculateAccuracy = (result: OCRResult | null): number => {
    if (!result) return 0
    
    // Complete field set matching actual application (OCRResult interface)
    const fields = [
      'firstName', 'middleName', 'lastName', 'dateOfBirth', 'address', 
      'licenseNumber', 'cardNumber', 'expirationDate', 'issueDate', 'state', 
      'confidence', 'mode', 'cardSide', 'surname', 'givenNames'
    ]
    const filledFields = fields.filter(field => {
      const value = result[field as keyof OCRResult]
      return value && String(value).trim().length > 0
    })
    
    return (filledFields.length / fields.length) * 100
  }

  const generateComparisonAnalysis = (session: TestSession): ComparisonAnalysis => {
    const results = session.results.filter(r => r.success)
    
    if (results.length === 0) {
      return {
        bestModel: 'N/A',
        bestPrompt: 'N/A',
        avgAccuracy: 0,
        avgCost: 0,
        avgTime: 0,
        fieldComparison: {}
      }
    }

    // Calculate metrics by model
    const modelMetrics = results.reduce((acc, result) => {
      if (!acc[result.modelId]) {
        acc[result.modelId] = {
          accuracy: [],
          cost: [],
          time: [],
          count: 0
        }
      }
      
      acc[result.modelId].accuracy.push(calculateAccuracy(result.result))
      acc[result.modelId].cost.push(result.cost)
      acc[result.modelId].time.push(result.processingTime)
      acc[result.modelId].count++
      
      return acc
    }, {} as Record<string, any>)

    // Find best model by average accuracy
    let bestModel = 'N/A'
    let bestAccuracy = 0
    
    Object.entries(modelMetrics).forEach(([modelId, metrics]) => {
      const avgAccuracy = metrics.accuracy.reduce((sum: number, acc: number) => sum + acc, 0) / metrics.accuracy.length
      if (avgAccuracy > bestAccuracy) {
        bestAccuracy = avgAccuracy
        bestModel = getAllModels().find(m => m.id === modelId)?.name || modelId
      }
    })

    // Calculate prompt metrics
    const promptMetrics = results.reduce((acc, result) => {
      if (!acc[result.promptId]) {
        acc[result.promptId] = {
          accuracy: [],
          count: 0
        }
      }
      
      acc[result.promptId].accuracy.push(calculateAccuracy(result.result))
      acc[result.promptId].count++
      
      return acc
    }, {} as Record<string, any>)

    // Find best prompt
    let bestPrompt = 'N/A'
    let bestPromptAccuracy = 0
    
    Object.entries(promptMetrics).forEach(([promptId, metrics]) => {
      const avgAccuracy = metrics.accuracy.reduce((sum: number, acc: number) => sum + acc, 0) / metrics.accuracy.length
      if (avgAccuracy > bestPromptAccuracy) {
        bestPromptAccuracy = avgAccuracy
        bestPrompt = getAllPrompts().find(p => p.id === promptId)?.name || promptId
      }
    })

    // Calculate overall averages
    const avgAccuracy = results.reduce((sum, r) => sum + calculateAccuracy(r.result), 0) / results.length
    const avgCost = results.reduce((sum, r) => sum + r.cost, 0) / results.length
    const avgTime = results.reduce((sum, r) => sum + r.processingTime, 0) / results.length

    return {
      bestModel,
      bestPrompt,
      avgAccuracy,
      avgCost,
      avgTime,
      fieldComparison: {} // Detailed field comparison would be implemented here
    }
  }

  const filteredImages = availableImages.filter(image => {
    const matchesSearch = searchTerm === '' || 
      image.filename.toLowerCase().includes(searchTerm.toLowerCase()) ||
      image.folder.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (image.project && image.project.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesProject = filterProject === 'all' || image.project === filterProject
    
    return matchesSearch && matchesProject
  })

  const exportResults = () => {
    const exportData = {
      sessions: testSessions,
      summary: {
        totalSessions: testSessions.length,
        totalTests: testSessions.reduce((sum, session) => sum + session.results.length, 0),
        totalCost: testSessions.reduce((sum, session) => sum + session.totalCost, 0),
        totalTime: testSessions.reduce((sum, session) => sum + session.totalTime, 0),
        modelPerformance: getAllModels().map(model => {
          const modelResults = testSessions.flatMap(session => 
            session.results.filter(r => r.modelId === model.id)
          )
          return {
            modelId: model.id,
            modelName: model.name,
            totalTests: modelResults.length,
            successRate: modelResults.length > 0 ? modelResults.filter(r => r.success).length / modelResults.length * 100 : 0,
            avgCost: modelResults.length > 0 ? modelResults.reduce((sum, r) => sum + r.cost, 0) / modelResults.length : 0,
            avgTime: modelResults.length > 0 ? modelResults.reduce((sum, r) => sum + r.processingTime, 0) / modelResults.length : 0,
            avgAccuracy: modelResults.length > 0 ? modelResults.reduce((sum, r) => sum + calculateAccuracy(r.result), 0) / modelResults.length : 0
          }
        })
      }
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `enhanced-ocr-test-results-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  // Enhanced session management functions
  const toggleSessionExpansion = (sessionId: string) => {
    setExpandedSessions(prev => {
      const newSet = new Set(prev)
      if (newSet.has(sessionId)) {
        newSet.delete(sessionId)
      } else {
        newSet.add(sessionId)
      }
      return newSet
    })
  }

  const exportSession = (session: TestSession) => {
    const exportData = {
      session,
      metadata: {
        exportedAt: new Date().toISOString(),
        sessionSummary: {
          totalTests: session.results.length,
          successfulTests: session.results.filter(r => r.success).length,
          totalCost: session.totalCost,
          totalTime: session.totalTime,
          avgAccuracy: session.results.length > 0 
            ? session.results.reduce((sum, r) => sum + calculateAccuracy(r.result), 0) / session.results.length 
            : 0
        }
      }
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `ocr-test-session-${session.id}-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  const deleteSession = (sessionId: string) => {
    const updatedSessions = testSessions.filter(s => s.id !== sessionId)
    setTestSessions(updatedSessions)
    localStorage.setItem('enhanced-ocr-test-sessions', JSON.stringify(updatedSessions))
    
    // Remove from expanded sessions if it was expanded
    setExpandedSessions(prev => {
      const newSet = new Set(prev)
      newSet.delete(sessionId)
      return newSet
    })
  }

  const clearAllSessions = () => {
    if (confirm('Are you sure you want to delete all test sessions? This action cannot be undone.')) {
      setTestSessions([])
      setExpandedSessions(new Set())
      localStorage.setItem('enhanced-ocr-test-sessions', JSON.stringify([]))
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6 max-w-none px-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Main
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Enhanced OCR Testing Playground</h1>
            <p className="text-muted-foreground">Compare multiple models and prompts on custom images from your projects</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button onClick={exportResults} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Results
          </Button>
          <Button 
            onClick={runTestSession} 
            disabled={isRunning || selectedImages.length === 0 || selectedModels.length === 0 || selectedPrompts.length === 0}
            variant="info"
          >
            {isRunning ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Running Tests...
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Run Test Session
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Progress */}
      {isRunning && currentSession && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="font-medium">{currentSession.name}</h4>
                  <p className="text-sm text-muted-foreground">
                    Testing {selectedModels.length} models × {selectedPrompts.length} prompts × {selectedImages.length} images
                  </p>
                </div>
                <Badge variant="secondary">{currentSession.progress.toFixed(1)}%</Badge>
              </div>
              <Progress value={currentSession.progress} className="h-2" />
              <div className="text-sm text-muted-foreground">
                {currentSession.results.length} / {selectedImages.length * selectedModels.length * selectedPrompts.length} tests completed
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Enhanced Error Display */}
      {error && (
        <Alert className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/50">
          <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
          <AlertDescription className="text-red-800 dark:text-red-300">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <span className="font-medium">Error:</span> {error}
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/20"
                onClick={() => setError(null)}
                aria-label="Dismiss error"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="setup" className="space-y-4">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="setup">Test Setup</TabsTrigger>
          <TabsTrigger value="prompts" className="flex items-center gap-1">
            <FileText className="h-3 w-3" />
            Prompts {getAllPrompts().length > 0 && <Badge variant="secondary" className="text-xs ml-1">{getAllPrompts().length}</Badge>}
          </TabsTrigger>
          <TabsTrigger value="validation" className="flex items-center gap-1">
            <Shield className="h-3 w-3" />
            Model Validation
          </TabsTrigger>
          <TabsTrigger value="results">Results</TabsTrigger>
          <TabsTrigger value="comparison">Comparison</TabsTrigger>
          <TabsTrigger value="statistics">Statistics</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="setup" className="space-y-8">
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8 w-full">
            {/* Image Selection */}
            <Card className="xl:col-span-1 min-w-0">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ImageIcon className="h-5 w-5" />
                  Select Test Images
                  <Badge variant="secondary">{selectedImages.length}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Search and Filter */}
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <div className="flex-1 relative">
                      <Search className="h-4 w-4 absolute left-3 top-3 text-muted-foreground" />
                      <Input
                        placeholder="Search images..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-9"
                      />
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedImages(filteredImages.map(img => img.id))}
                    >
                      Select All
                    </Button>
                  </div>
                  
                  <Select value={filterProject} onValueChange={setFilterProject}>
                    <SelectTrigger>
                      <SelectValue placeholder="Filter by project" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Projects</SelectItem>
                      {userProjects.map(project => (
                        <SelectItem key={project.id} value={project.name}>
                          {project.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Loading States */}
                {(isLoadingProjects || isLoadingImages) && (
                  <div className="flex items-center justify-center p-8">
                    <div className="text-center">
                      <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
                      <p className="text-sm text-muted-foreground">
                        {isLoadingProjects ? 'Loading projects...' : 'Loading images...'}
                      </p>
                    </div>
                  </div>
                )}

                {/* Image Grid */}
                <div className="grid grid-cols-1 gap-2 max-h-96 overflow-y-auto">
                  {filteredImages.map(image => (
                    <div
                      key={image.id}
                      className={cn(
                        "flex items-center space-x-3 p-3 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors group",
                        selectedImages.includes(image.id) && "bg-primary/10 border-primary ring-2 ring-primary/20 shadow-sm"
                      )}
                      onClick={() => {
                        if (selectedImages.includes(image.id)) {
                          setSelectedImages(selectedImages.filter(id => id !== image.id))
                        } else {
                          setSelectedImages([...selectedImages, image.id])
                        }
                      }}
                    >
                      <Checkbox 
                        checked={selectedImages.includes(image.id)}
                        onChange={() => {}} // Handled by parent onClick
                      />
                      <div className="relative">
                        <Image
                          src={image.thumbnailUrl}
                          alt={image.filename}
                          width={48}
                          height={48}
                          className="rounded border object-cover bg-muted"
                          onError={(e) => {
                            console.warn(`Failed to load thumbnail for ${image.filename}`);
                            // Show a styled fallback instead of hiding
                            const target = e.target as HTMLImageElement;
                            target.className = 'w-12 h-12 rounded border bg-muted flex items-center justify-center text-muted-foreground text-xl';
                            target.alt = '📷 Image unavailable';
                            target.style.display = 'flex';
                            target.style.fontSize = '20px';
                            target.style.alignItems = 'center';
                            target.style.justifyContent = 'center';
                            // Replace src with a data URL to prevent reload attempts
                            target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiByeD0iNCIgZmlsbD0iI0Y5RkFGQiIvPgo8cGF0aCBkPSJNMjQgMjBDMjYuMjA5MSAyMCAyOCAyMS43OTA5IDI4IDI0QzI4IDI2LjIwOTEgMjYuMjA5MSAyOCAyNCAyOEMyMS43OTA5IDI4IDIwIDI2LjIwOTEgMjAgMjRDMjAgMjEuNzkwOSAyMS43OTA5IDIwIDI0IDIwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMTIgMTZDMTAgMTYgOCAxNy43OTA5IDggMjBWMzJDOCAzNC4yMDkxIDEwIDM2IDEyIDM2SDM2QzM4IDM2IDQwIDM0LjIwOTEgNDAgMzJWMjBDNDAgMTcuNzkwOSAzOCAxNiAzNiAxNkgzMkwyOCAxMkgyMEwxNiAxNkgxMloiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+';
                          }}
                          onLoad={() => {
                            // Image loaded successfully
                            console.log(`Thumbnail loaded: ${image.filename}`);
                          }}
                        />
                        {/* Enhanced hover preview with improved positioning and loading */}
                        <div className="absolute left-12 top-0 z-50 hidden group-hover:block pointer-events-none">
                          <div className="bg-background border border-border rounded-lg shadow-2xl p-3 max-w-sm animate-in fade-in-0 zoom-in-95 duration-200">
                            <p className="text-sm font-medium mb-2 truncate">{image.filename}</p>
                            <div className="relative bg-muted rounded overflow-hidden">
                              <Image
                                src={image.previewUrl}
                                alt={image.filename}
                                width={320}
                                height={240}
                                className="rounded object-contain w-full h-auto max-h-[40vh] transition-opacity duration-300"
                                priority={false}
                                loading="lazy"
                                placeholder="blur"
                                blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQEhIQFhAVEhISFhQVFBUVGhgYGhgYGhf/xAAVAQEBAAAAAAAAAAAAAAAAAAABBf/EAB4QAAICAwEBAQEAAAAAAAAAAAAAEQECIUEDEjFR/9oACAEBAAA/AMRr2x2EGWgAAAAA/9k="
                                onError={(e) => {
                                  console.warn(`Failed to load preview for ${image.filename}`);
                                  // Create a better fallback UI
                                  const target = e.target as HTMLImageElement;
                                  const parent = target.parentElement;
                                  if (parent && !parent.querySelector('.preview-error')) {
                                    const errorDiv = document.createElement('div');
                                    errorDiv.className = 'preview-error w-full h-40 bg-muted/50 border border-dashed border-muted-foreground/30 rounded flex flex-col items-center justify-center text-muted-foreground';
                                    errorDiv.innerHTML = `
                                      <div class="text-3xl mb-2">🖼️</div>
                                      <div class="text-sm font-medium">Preview unavailable</div>
                                      <div class="text-xs mt-1 opacity-75">${image.filename}</div>
                                    `;
                                    target.style.display = 'none';
                                    parent.appendChild(errorDiv);
                                  }
                                }}
                                onLoad={() => {
                                  console.log(`Preview loaded: ${image.filename}`);
                                }}
                              />
                            </div>
                            <div className="mt-2 text-xs text-muted-foreground space-y-1">
                              <p><span className="font-medium">Project:</span> {image.project}</p>
                              <p><span className="font-medium">Folder:</span> {image.folder}</p>
                              <p><span className="font-medium">Size:</span> {(image.fileSize / 1024).toFixed(1)} KB</p>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{image.filename}</p>
                        <p className="text-xs text-muted-foreground">{image.project} / {image.folder}</p>
                      </div>
                    </div>
                  ))}
                </div>

                {filteredImages.length === 0 && !isLoadingImages && (
                  <div className="text-center p-8 text-muted-foreground">
                    <ImageIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>No images found</p>
                    <p className="text-xs">Make sure you have projects with images loaded.</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Model Selection */}
            <Card className="min-w-0">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TestTube className="h-5 w-5" />
                  Select Models (Max 5)
                  <Badge variant="secondary">{selectedModels.length}/5</Badge>
                  {openRouterConfig.selectedModel && (
                    <Badge variant="info" className="text-xs">
                      Current: {AVAILABLE_MODELS.find(m => m.id === openRouterConfig.selectedModel)?.name || openRouterConfig.selectedModel}
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {/* Free Models */}
                  <div>
                    <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                      Free Models
                      <Badge className="badge-free">No Cost</Badge>
                    </h4>
                    {getAllModels().filter(m => m.tier === 'free').map(model => (
                      <div key={model.id} 
                           className="flex items-start space-x-3 p-2 border rounded hover:bg-muted/50 cursor-pointer transition-colors" 
                           onClick={(e) => {
                             e.preventDefault()
                             const isSelected = selectedModels.includes(model.id)
                             if (!isSelected && selectedModels.length < 5) {
                               setSelectedModels([...selectedModels, model.id])
                             } else if (isSelected) {
                               setSelectedModels(selectedModels.filter(id => id !== model.id))
                             }
                           }}>
                        <Checkbox
                          checked={selectedModels.includes(model.id)}
                          onCheckedChange={() => {}} // Handled by parent div click
                          disabled={!selectedModels.includes(model.id) && selectedModels.length >= 5}
                        />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium">{model.name}</span>
                            <Badge variant="outline" className="text-xs">{model.provider}</Badge>
                            {model.isRecommended && (
                              <Badge variant="info" className="text-xs">Recommended</Badge>
                            )}
                          </div>
                          <p className="text-xs text-muted-foreground">{model.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Paid Models */}
                  <div>
                    <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                      Paid Models
                      <Badge className="badge-paid">Pay per Use</Badge>
                    </h4>
                    {getAllModels().filter(m => m.tier === 'paid').map(model => (
                      <div key={model.id} 
                           className="flex items-start space-x-3 p-2 border rounded hover:bg-muted/50 cursor-pointer transition-colors"
                           onClick={(e) => {
                             e.preventDefault()
                             const isSelected = selectedModels.includes(model.id)
                             if (!isSelected && selectedModels.length < 5) {
                               setSelectedModels([...selectedModels, model.id])
                             } else if (isSelected) {
                               setSelectedModels(selectedModels.filter(id => id !== model.id))
                             }
                           }}>
                        <Checkbox
                          checked={selectedModels.includes(model.id)}
                          onCheckedChange={() => {}} // Handled by parent div click
                          disabled={!selectedModels.includes(model.id) && selectedModels.length >= 5}
                        />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium">{model.name}</span>
                            <Badge variant="outline" className="text-xs">{model.provider}</Badge>
                            <Badge className="badge-paid text-xs">
                              ${model.costPer1k}/1K
                            </Badge>
                            {model.isRecommended && (
                              <Badge variant="info" className="text-xs">Recommended</Badge>
                            )}
                          </div>
                          <p className="text-xs text-muted-foreground">{model.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Custom Models */}
                  {customModels.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                        Custom Models
                        <Badge className="badge-premium">Custom</Badge>
                      </h4>
                      {customModels.map(model => (
                        <div key={model.id} 
                             className="flex items-start space-x-3 p-2 border rounded hover:bg-muted/50 cursor-pointer transition-colors"
                             onClick={(e) => {
                               e.preventDefault()
                               const isSelected = selectedModels.includes(model.id)
                               if (!isSelected && selectedModels.length < 5) {
                                 setSelectedModels([...selectedModels, model.id])
                               } else if (isSelected) {
                                 setSelectedModels(selectedModels.filter(id => id !== model.id))
                               }
                             }}>
                          <Checkbox
                            checked={selectedModels.includes(model.id)}
                            onCheckedChange={() => {}} // Handled by parent div click
                            disabled={!selectedModels.includes(model.id) && selectedModels.length >= 5}
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="text-sm font-medium">{model.name}</span>
                              <Badge variant="outline" className="text-xs">{model.provider}</Badge>
                              {model.costPer1k && (
                                <Badge variant="outline" className="text-xs text-orange-600">
                                  ${model.costPer1k}/1K
                                </Badge>
                              )}
                            </div>
                            <p className="text-xs text-muted-foreground">{model.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* OpenRouter Model Management */}
                <div className="pt-4 border-t">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium flex items-center gap-2">
                      OpenRouter Models
                      <Badge variant="outline" className="bg-blue-100 text-blue-800">Live</Badge>
                    </h4>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setShowModelManager(!showModelManager)
                        if (!showModelManager && openRouterModels.length === 0) {
                          loadOpenRouterModels()
                        }
                      }}
                      disabled={!openRouterConfig.apiKey}
                    >
                      <Settings className="h-4 w-4 mr-2" />
                      {showModelManager ? 'Hide Manager' : 'Manage Models'}
                    </Button>
                  </div>

                  {!openRouterConfig.apiKey && (
                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        Configure your OpenRouter API key in settings to manage live models.
                      </AlertDescription>
                    </Alert>
                  )}

                  {showModelManager && openRouterConfig.apiKey && (
                    <div className="space-y-3 p-3 bg-muted/30 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Input
                          placeholder="Search OpenRouter models..."
                          value={modelSearchTerm}
                          onChange={(e) => setModelSearchTerm(e.target.value)}
                          className="flex-1"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={loadOpenRouterModels}
                          disabled={isLoadingOpenRouterModels}
                        >
                          {isLoadingOpenRouterModels ? (
                            <RefreshCw className="h-4 w-4 animate-spin" />
                          ) : (
                            <RefreshCw className="h-4 w-4" />
                          )}
                        </Button>
                      </div>

                      {isLoadingOpenRouterModels && (
                        <div className="text-center py-4">
                          <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
                          <p className="text-sm text-muted-foreground">Loading OpenRouter models...</p>
                        </div>
                      )}

                      {!isLoadingOpenRouterModels && filteredOpenRouterModels.length > 0 && (
                        <div className="space-y-2 max-h-60 overflow-y-auto">
                          {filteredOpenRouterModels.map(model => {
                            const isAlreadyAdded = [...AVAILABLE_MODELS, ...customModels].some(m => m.id === model.id)
                            return (
                              <div key={model.id} className="flex items-center justify-between p-2 bg-background rounded border">
                                <div className="flex-1 min-w-0 mr-3">
                                  <div className="flex items-center gap-2 mb-1">
                                    <span className="text-sm font-medium truncate">{model.name}</span>
                                    {model.pricing?.prompt && (
                                      <Badge variant="outline" className="text-xs">
                                        ${model.pricing.prompt > 1 ? model.pricing.prompt.toFixed(2) : (model.pricing.prompt * 1000).toFixed(2)}/1K
                                      </Badge>
                                    )}
                                  </div>
                                  <p className="text-xs text-muted-foreground truncate">
                                    {model.description || model.id}
                                  </p>
                                </div>
                                <div className="flex items-center gap-2">
                                  {isAlreadyAdded ? (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => removeModelFromList(model.id)}
                                      className="text-red-600 hover:text-red-700"
                                    >
                                      <Minus className="h-4 w-4" />
                                    </Button>
                                  ) : (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => addModelToList(model)}
                                      className="text-green-600 hover:text-green-700"
                                    >
                                      <Plus className="h-4 w-4" />
                                    </Button>
                                  )}
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      )}

                      {!isLoadingOpenRouterModels && filteredOpenRouterModels.length === 0 && openRouterModels.length > 0 && (
                        <p className="text-sm text-muted-foreground text-center py-4">
                          No models found matching &quot;{modelSearchTerm}&quot;
                        </p>
                      )}

                      {!isLoadingOpenRouterModels && openRouterModels.length === 0 && (
                        <p className="text-sm text-muted-foreground text-center py-4">
                          No vision models available. Try refreshing or check your API key.
                        </p>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Prompt Selection */}
            <Card className="min-w-0">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Select Prompts
                  <Badge variant="secondary">{selectedPrompts.length}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {/* Actual Project Prompts */}
                  <div>
                    <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                      Official Project Prompts
                      <Badge variant="success" className="text-xs">Live</Badge>
                      {isLoadingPrompts && <RefreshCw className="h-3 w-3 animate-spin" />}
                    </h4>
                    {isLoadingPrompts ? (
                      <div className="flex items-center justify-center p-8">
                        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                        <span className="text-sm text-muted-foreground">Loading actual project prompts...</span>
                      </div>
                    ) : actualPrompts.length === 0 ? (
                      <div className="text-center p-4 text-sm text-muted-foreground">
                        No project prompts available. Check your settings.
                      </div>
                    ) : actualPrompts.map(prompt => (
                      <div key={prompt.id} 
                           className="flex items-start space-x-3 p-2 border rounded hover:bg-muted/50 cursor-pointer transition-colors"
                           onClick={(e) => {
                             e.preventDefault()
                             const isSelected = selectedPrompts.includes(prompt.id)
                             if (!isSelected) {
                               setSelectedPrompts([...selectedPrompts, prompt.id])
                             } else {
                               setSelectedPrompts(selectedPrompts.filter(id => id !== prompt.id))
                             }
                           }}>
                        <Checkbox
                          checked={selectedPrompts.includes(prompt.id)}
                          onCheckedChange={() => {}} // Handled by parent div click 
                        />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium">{prompt.name}</span>
                            <Badge variant="info" className="text-xs">{prompt.category}</Badge>
                            {prompt.mode && (
                              <Badge variant="outline" className="text-xs">{prompt.mode}</Badge>
                            )}
                          </div>
                          <p className="text-xs text-muted-foreground mb-2">{prompt.description}</p>
                          <p className="text-xs text-muted-foreground line-clamp-2">{prompt.content.substring(0, 150)}...</p>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Custom Prompts */}
                  {customPrompts.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                        Custom Prompts
                        <Badge variant="outline" className="bg-purple-100 text-purple-800">Custom</Badge>
                      </h4>
                      {customPrompts.map(prompt => (
                        <div key={prompt.id} 
                             className="flex items-start space-x-3 p-2 border rounded hover:bg-muted/50 cursor-pointer transition-colors"
                             onClick={(e) => {
                               e.preventDefault()
                               const isSelected = selectedPrompts.includes(prompt.id)
                               if (!isSelected) {
                                 setSelectedPrompts([...selectedPrompts, prompt.id])
                               } else {
                                 setSelectedPrompts(selectedPrompts.filter(id => id !== prompt.id))
                               }
                             }}>
                          <Checkbox
                            checked={selectedPrompts.includes(prompt.id)}
                            onCheckedChange={() => {}} // Handled by parent div click
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="text-sm font-medium">{prompt.name}</span>
                              <Badge variant="outline" className="text-xs">{prompt.category}</Badge>
                            </div>
                            <p className="text-xs text-muted-foreground mb-2">{prompt.description}</p>
                            <p className="text-xs text-muted-foreground line-clamp-2">{prompt.content}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setSelectedPrompts(getAllPrompts().map(p => p.id))}
                  >
                    Select All
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setSelectedPrompts([])}
                  >
                    Clear All
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Test Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Test Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{selectedImages.length}</div>
                  <div className="text-sm text-muted-foreground">Images</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{selectedModels.length}</div>
                  <div className="text-sm text-muted-foreground">Models</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{selectedPrompts.length}</div>
                  <div className="text-sm text-muted-foreground">Prompts</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {selectedImages.length * selectedModels.length * selectedPrompts.length}
                  </div>
                  <div className="text-sm text-muted-foreground">Total Tests</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="prompts" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* System Prompts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    System Prompts ({actualPrompts.length})
                  </div>
                  <Button
                    onClick={loadActualPrompts}
                    disabled={isLoadingPrompts}
                    variant="outline"
                    size="sm"
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${isLoadingPrompts ? 'animate-spin' : ''}`} />
                    Refresh
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoadingPrompts ? (
                  <div className="text-center py-8">
                    <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">Loading OCR prompts...</p>
                  </div>
                ) : actualPrompts.length > 0 ? (
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {actualPrompts.map(prompt => {
                      const isExpanded = expandedPrompts.has(prompt.id)
                      const isEditing = editingPrompts.has(prompt.id)
                      const editContent = promptEditContent[prompt.id] || prompt.content
                      
                      const toggleExpanded = () => {
                        setExpandedPrompts(prev => {
                          const newSet = new Set(prev)
                          if (newSet.has(prompt.id)) {
                            newSet.delete(prompt.id)
                          } else {
                            newSet.add(prompt.id)
                          }
                          return newSet
                        })
                      }
                      
                      const toggleEditing = () => {
                        setEditingPrompts(prev => {
                          const newSet = new Set(prev)
                          if (newSet.has(prompt.id)) {
                            newSet.delete(prompt.id)
                          } else {
                            newSet.add(prompt.id)
                            setPromptEditContent(prevContent => ({
                              ...prevContent,
                              [prompt.id]: prompt.content
                            }))
                          }
                          return newSet
                        })
                      }
                      
                      const updateEditContent = (content: string) => {
                        setPromptEditContent(prev => ({
                          ...prev,
                          [prompt.id]: content
                        }))
                      }
                      
                      const saveChanges = () => {
                        // Update prompt content (this would need backend integration)
                        prompt.content = editContent
                        setEditingPrompts(prev => {
                          const newSet = new Set(prev)
                          newSet.delete(prompt.id)
                          return newSet
                        })
                        // TODO: Call API to save changes
                      }
                      
                      const cancelEditing = () => {
                        setPromptEditContent(prev => ({
                          ...prev,
                          [prompt.id]: prompt.content
                        }))
                        setEditingPrompts(prev => {
                          const newSet = new Set(prev)
                          newSet.delete(prompt.id)
                          return newSet
                        })
                      }
                      
                      return (
                        <div key={prompt.id} className="p-4 rounded-lg border bg-card text-card-foreground">
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-2">
                                <span className="font-medium text-lg">{prompt.name}</span>
                                <Badge variant="outline" className="text-xs">{prompt.category}</Badge>
                                {prompt.mode && (
                                  <Badge className="badge-free text-xs">{prompt.mode.toUpperCase()}</Badge>
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground mb-3">{prompt.description}</p>
                              
                              {/* Expandable/Editable Content */}
                              <div className="space-y-2">
                                <div className="flex items-center justify-between">
                                  <h4 className="text-sm font-medium">Prompt Content:</h4>
                                  <div className="flex items-center gap-2">
                                    <Button
                                      onClick={toggleExpanded}
                                      variant="ghost"
                                      size="sm"
                                      className="h-6 text-xs"
                                    >
                                      {isExpanded ? 'Collapse' : 'Expand'}
                                    </Button>
                                    <Button
                                      onClick={toggleEditing}
                                      variant="ghost"
                                      size="sm"
                                      className="h-6 text-xs"
                                    >
                                      {isEditing ? 'Cancel' : 'Edit'}
                                    </Button>
                                  </div>
                                </div>
                                
                                {isEditing ? (
                                  <div className="space-y-2">
                                    <textarea
                                      value={editContent}
                                      onChange={(e) => updateEditContent(e.target.value)}
                                      className="w-full min-h-32 max-h-96 text-xs font-mono bg-background rounded border p-3 resize-y focus:outline-none focus:ring-2 focus:ring-primary/20"
                                    />
                                    <div className="flex items-center gap-2">
                                      <Button
                                        onClick={saveChanges}
                                        size="sm"
                                        className="h-7 text-xs"
                                      >
                                        Save Changes
                                      </Button>
                                      <Button
                                        onClick={cancelEditing}
                                        variant="outline"
                                        size="sm"
                                        className="h-7 text-xs"
                                      >
                                        Cancel
                                      </Button>
                                    </div>
                                  </div>
                                ) : (
                                  <div className="relative">
                                    <textarea
                                      readOnly
                                      value={isExpanded ? prompt.content : prompt.content.substring(0, 200) + (prompt.content.length > 200 ? '...' : '')}
                                      className={`w-full text-xs font-mono bg-muted/30 rounded border p-3 resize-none focus:outline-none ${
                                        isExpanded ? 'min-h-32 max-h-96' : 'h-20'
                                      }`}
                                    />
                                  </div>
                                )}
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-1 ml-3">
                              <Button
                                onClick={() => duplicatePrompt(prompt)}
                                variant="ghost"
                                size="sm"
                                title="Duplicate to Custom Prompts"
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 mx-auto mb-2 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground mb-2">No system prompts loaded</p>
                    <Button onClick={loadActualPrompts} variant="outline" size="sm">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Load Prompts
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Custom Prompts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Plus className="h-5 w-5" />
                    Custom Prompts ({customPrompts.length})
                  </div>
                  <Button
                    onClick={() => {
                      setEditingPrompt(null)
                      setNewPrompt({
                        name: '',
                        category: 'Custom',
                        description: '',
                        content: '',
                        mode: 'us'
                      })
                      setShowPromptEditor(true)
                    }}
                    size="sm"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    New Prompt
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {customPrompts.length > 0 ? (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {customPrompts.map(prompt => (
                      <div key={prompt.id} className="info-panel">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-medium">{prompt.name}</span>
                              <Badge className="badge-premium text-xs">{prompt.category}</Badge>
                              {prompt.mode && (
                                <Badge className="badge-free text-xs">{prompt.mode.toUpperCase()}</Badge>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground mb-2">{prompt.description}</p>
                            <div className="code-bubble text-xs max-h-20 overflow-y-auto">
                              {prompt.content.substring(0, 150)}...
                            </div>
                          </div>
                          <div className="flex items-center gap-1 ml-3">
                            <Button
                              onClick={() => editPrompt(prompt)}
                              variant="ghost"
                              size="sm"
                              title="Edit prompt"
                            >
                              <Settings className="h-4 w-4" />
                            </Button>
                            <Button
                              onClick={() => duplicatePrompt(prompt)}
                              variant="ghost"
                              size="sm"
                              title="Duplicate prompt"
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                            <Button
                              onClick={() => deletePrompt(prompt.id)}
                              variant="ghost"
                              size="sm"
                              className="text-red-600 hover:text-red-700"
                              title="Delete prompt"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Plus className="h-12 w-12 mx-auto mb-2 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground mb-2">No custom prompts created</p>
                    <p className="text-xs text-muted-foreground mb-4">
                      Create custom prompts to test specific extraction patterns
                    </p>
                    <Button
                      onClick={() => {
                        setEditingPrompt(null)
                        setNewPrompt({
                          name: '',
                          category: 'Custom',
                          description: '',
                          content: '',
                          mode: 'us'
                        })
                        setShowPromptEditor(true)
                      }}
                      size="sm"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Create First Prompt
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Prompt Editor Dialog */}
          {showPromptEditor && (
            <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center">
              <div className="bg-card border border-border rounded-lg shadow-lg w-full max-w-4xl max-h-[80vh] overflow-y-auto m-4">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-lg font-semibold">
                      {editingPrompt ? 'Edit Prompt' : 'Create New Prompt'}
                    </h2>
                    <Button
                      onClick={() => setShowPromptEditor(false)}
                      variant="ghost"
                      size="sm"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="prompt-name">Name</Label>
                        <Input
                          id="prompt-name"
                          value={newPrompt.name || ''}
                          onChange={(e) => setNewPrompt({ ...newPrompt, name: e.target.value })}
                          placeholder="Enter prompt name"
                        />
                      </div>
                      <div>
                        <Label htmlFor="prompt-category">Category</Label>
                        <Select
                          value={newPrompt.category || 'Custom'}
                          onValueChange={(value) => setNewPrompt({ ...newPrompt, category: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Custom">Custom</SelectItem>
                            <SelectItem value="Official">Official</SelectItem>
                            <SelectItem value="Experimental">Experimental</SelectItem>
                            <SelectItem value="Templates">Templates</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="prompt-description">Description</Label>
                      <Input
                        id="prompt-description"
                        value={newPrompt.description || ''}
                        onChange={(e) => setNewPrompt({ ...newPrompt, description: e.target.value })}
                        placeholder="Describe what this prompt does"
                      />
                    </div>

                    <div>
                      <Label htmlFor="prompt-mode">Mode</Label>
                      <Select
                        value={newPrompt.mode || 'us'}
                        onValueChange={(value) => setNewPrompt({ ...newPrompt, mode: value })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="us">US Driver License</SelectItem>
                          <SelectItem value="australian">Australian Driver License</SelectItem>
                          <SelectItem value="generic">Generic Document</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="prompt-content">Prompt Content</Label>
                      <Textarea
                        id="prompt-content"
                        value={newPrompt.content || ''}
                        onChange={(e) => setNewPrompt({ ...newPrompt, content: e.target.value })}
                        placeholder="Enter the OCR extraction prompt..."
                        rows={10}
                        className="font-mono text-sm"
                      />
                    </div>

                    <div className="flex justify-end gap-2">
                      <Button
                        onClick={() => setShowPromptEditor(false)}
                        variant="outline"
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={() => {
                          if (newPrompt.name && newPrompt.content) {
                            savePrompt(newPrompt as TestPrompt)
                          }
                        }}
                        disabled={!newPrompt.name || !newPrompt.content}
                      >
                        {editingPrompt ? 'Update Prompt' : 'Create Prompt'}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="validation" className="space-y-4">
          <div className="grid grid-cols-1 gap-6">
            <ModelValidationPanel 
              models={AVAILABLE_MODELS} 
              onModelsFixed={(fixedModels) => {
                console.log('🔧 Models fixed:', fixedModels)
                // You could update the OCR_MODELS here if needed
              }}
              showTitle={false}
              autoValidateOnMount={true}
            />
            
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Model Configuration
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Currently Configured Models ({AVAILABLE_MODELS.length})</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                      {AVAILABLE_MODELS.map((model) => (
                        <div key={model.id} className="model-card">
                          <div className="flex items-center justify-between mb-2">
                            <div className="code-bubble-dark">
                              {model.id}
                            </div>
                            <Badge className={model.tier === 'free' ? 'badge-free' : 'badge-paid'}>
                              {model.tier}
                            </Badge>
                          </div>
                          <p className="text-sm font-medium">{model.name}</p>
                          <p className="text-xs text-muted-foreground">{model.provider}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div className="p-4 rounded-lg border bg-card text-card-foreground">
                    <h5 className="font-semibold mb-3 flex items-center gap-2">
                      <span className="text-primary">💡</span>
                      Model Validation Benefits
                    </h5>
                    <div className="benefits-list">
                      <div className="benefits-item">
                        <div className="benefits-bullet"></div>
                        <span>Ensures all configured models are available on OpenRouter</span>
                      </div>
                      <div className="benefits-item">
                        <div className="benefits-bullet"></div>
                        <span>Automatically suggests replacements for unavailable models</span>
                      </div>
                      <div className="benefits-item">
                        <div className="benefits-bullet"></div>
                        <span>Prevents &quot;model not found&quot; errors during testing</span>
                      </div>
                      <div className="benefits-item">
                        <div className="benefits-bullet"></div>
                        <span>Keeps model configuration synchronized with OpenRouter API</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          {/* Enhanced Results Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <TestTube className="h-5 w-5" />
              <h2 className="text-xl font-semibold">Test Results</h2>
              <Badge variant="secondary">{testSessions.length} sessions</Badge>
            </div>
            <div className="flex gap-2">
              <Button onClick={exportResults} variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export All
              </Button>
              {testSessions.length > 0 && (
                <Button onClick={clearAllSessions} variant="destructive" size="sm">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear All
                </Button>
              )}
            </div>
          </div>

          {testSessions.length === 0 ? (
            <Card>
              <CardContent className="pt-6 text-center">
                <TestTube className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">No test results yet. Run some tests to see results here.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {testSessions.map(session => {
                const isExpanded = expandedSessions.has(session.id)
                const successfulTests = session.results.filter(r => r.success).length
                const avgAccuracy = session.results.length > 0 
                  ? session.results.reduce((sum, r) => sum + calculateAccuracy(r.result), 0) / session.results.length 
                  : 0

                return (
                  <Card key={session.id} className="transition-all duration-200">
                    {/* Compact Header */}
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3 flex-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleSessionExpansion(session.id)}
                            className="p-1 h-8 w-8"
                          >
                            {isExpanded ? (
                              <ChevronDown className="h-4 w-4" />
                            ) : (
                              <ChevronRight className="h-4 w-4" />
                            )}
                          </Button>
                          
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className="font-semibold text-base">{session.name}</h3>
                              <Badge variant={
                                session.status === 'completed' ? 'default' :
                                session.status === 'running' ? 'secondary' :
                                session.status === 'failed' ? 'destructive' : 'outline'
                              } className="text-xs">
                                {session.status}
                              </Badge>
                            </div>
                            
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {new Date(session.timestamp).toLocaleDateString()}
                              </div>
                              <div className="flex items-center gap-1">
                                <TestTube className="h-3 w-3" />
                                {session.results.length} tests
                              </div>
                              <div className="flex items-center gap-1">
                                <CheckCircle className="h-3 w-3" />
                                {successfulTests}/{session.results.length}
                              </div>
                              <div className="flex items-center gap-1">
                                <DollarSign className="h-3 w-3" />
                                ${session.totalCost.toFixed(4)}
                              </div>
                              <div className="flex items-center gap-1">
                                <Target className="h-3 w-3" />
                                {avgAccuracy.toFixed(1)}%
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => exportSession(session)}
                            className="h-8"
                          >
                            <Download className="h-3 w-3 mr-1" />
                            Export
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteSession(session.id)}
                            className="h-8 text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    
                    {/* Expandable Content */}
                    {isExpanded && (
                      <CardContent className="pt-0 border-t">
                        <div className="space-y-4 mt-4">
                          {session.results.map((result, index) => (
                            <Card key={index} className={cn(
                              "border-l-4 transition-all duration-200",
                              result.success 
                                ? "border-l-green-500 hover:shadow-md" 
                                : "border-l-red-500 hover:shadow-md"
                            )}>
                              <CardHeader className="pb-3">
                            <div className="flex justify-between items-start">
                              <div className="space-y-2 flex-1">
                                <div className="flex items-center gap-3">
                                  <h3 className="text-lg font-semibold text-foreground">{result.modelName}</h3>
                                  {result.success ? (
                                    <CheckCircle className="h-5 w-5 text-green-500" />
                                  ) : (
                                    <AlertCircle className="h-5 w-5 text-red-500" />
                                  )}
                                </div>
                                <div className="flex items-center gap-2">
                                  <Badge variant="secondary" className="text-xs">
                                    Prompt: {result.promptName}
                                  </Badge>
                                  <Badge variant={result.success ? "success" : "destructive"} className="text-xs">
                                    {result.success ? "Success" : "Failed"}
                                  </Badge>
                                </div>
                              </div>
                              <div className="flex items-start gap-4">
                                {/* Test Image Thumbnail */}
                                <div className="relative group">
                                  <div className="w-20 h-16 rounded-lg overflow-hidden border bg-muted flex items-center justify-center">
                                    <Image 
                                      src={`/api/filesystem/thumbnail/${session.imageId}`}
                                      alt={session.imageName}
                                      width={80}
                                      height={64}
                                      className="w-full h-full object-cover cursor-pointer transition-transform hover:scale-105"
                                      onError={(e) => {
                                        e.currentTarget.style.display = 'none';
                                        const parent = e.currentTarget.parentElement;
                                        if (parent) {
                                          parent.innerHTML = '<div class="text-xs text-muted-foreground">No image</div>';
                                        }
                                      }}
                                    />
                                  </div>
                                  {/* Hover Preview */}
                                  <div className="hover-preview opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                                    <Image 
                                      src={`/api/filesystem/image/${session.imageId}`}
                                      alt={session.imageName}
                                      width={400}
                                      height={320}
                                      className="max-w-md max-h-80 rounded-lg shadow-lg object-contain"
                                      loading="lazy"
                                      onError={(e) => {
                                        console.warn(`Failed to load hover preview: ${session.imageName}`);
                                      }}
                                    />
                                  </div>
                                </div>
                                
                                <div className="text-right space-y-1 flex-1">
                                  <div className="flex items-center justify-end gap-3 text-sm">
                                    <div className="flex items-center gap-1">
                                      <DollarSign className="h-3 w-3 text-green-600" />
                                      <span className="font-medium">${result.cost.toFixed(4)}</span>
                                    </div>
                                  <div className="flex items-center gap-1">
                                    <Clock className="h-3 w-3 text-blue-600" />
                                    <span className="font-medium">{(result.processingTime / 1000).toFixed(1)}s</span>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <Target className="h-3 w-3 text-purple-600" />
                                    <span className="font-medium">{calculateAccuracy(result.result).toFixed(1)}%</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                            </div>
                          </CardHeader>
                          <CardContent className="pt-0">
                          
                          {result.success && result.result ? (
                            <div className="space-y-4">
                              {/* Structured Fields - Same as Actual Application */}
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div className="space-y-1">
                                  <h4 className="font-medium text-primary mb-2">Personal Information</h4>
                                  {result.result.mode === 'australian' ? (
                                    <>
                                      <p><strong>Surname:</strong> {result.result.surname || result.result.lastName}</p>
                                      <p><strong>Given Names:</strong> {result.result.givenNames || `${result.result.firstName} ${result.result.middleName || ''}`.trim()}</p>
                                    </>
                                  ) : (
                                    <>
                                      <p><strong>First Name:</strong> {result.result.firstName}</p>
                                      {result.result.middleName && <p><strong>Middle Name:</strong> {result.result.middleName}</p>}
                                      <p><strong>Last Name:</strong> {result.result.lastName}</p>
                                    </>
                                  )}
                                  <p><strong>Date of Birth:</strong> {result.result.dateOfBirth}</p>
                                  <p><strong>Address:</strong> {result.result.address}</p>
                                </div>
                                <div className="space-y-1">
                                  <h4 className="font-medium text-primary mb-2">Document Information</h4>
                                  <p><strong>License Number:</strong> {result.result.licenseNumber}</p>
                                  {result.result.cardNumber && <p><strong>Card Number:</strong> {result.result.cardNumber}</p>}
                                  <p><strong>State:</strong> {result.result.state}</p>
                                  {result.result.issueDate && <p><strong>Issue Date:</strong> {result.result.issueDate}</p>}
                                  <p><strong>Expiration Date:</strong> {result.result.expirationDate}</p>
                                  <div><strong>Mode:</strong> <Badge variant="outline" className="text-xs">{result.result.mode}</Badge></div>
                                  {result.result.cardSide && <p><strong>Card Side:</strong> {result.result.cardSide}</p>}
                                  <div><strong>Confidence:</strong> <Badge variant={result.result.confidence > 0.8 ? 'success' : result.result.confidence > 0.6 ? 'warning' : 'destructive'} className="text-xs">
                                    {(result.result.confidence * 100).toFixed(1)}%
                                  </Badge></div>
                                </div>
                              </div>
                              
                              {/* Raw Output Section */}
                              <div className="border-t pt-4">
                                <div className="flex items-center justify-between mb-2">
                                  <h4 className="font-medium text-primary">Raw Model Output</h4>
                                  <Badge variant="outline" className="text-xs">Before Field Filtering</Badge>
                                </div>
                                <div className="space-y-2">
                                  <div className="bg-muted/30 rounded-lg p-3">
                                    <p className="text-xs font-medium text-muted-foreground mb-2">Extracted Text:</p>
                                    <div className="relative">
                                      <textarea 
                                        readOnly
                                        value={result.result.rawText || 'No raw text available'}
                                        className="w-full min-h-32 max-h-96 text-xs whitespace-pre-wrap font-mono bg-background rounded border p-2 pr-20 resize-y focus:outline-none focus:ring-2 focus:ring-primary/20"
                                        />
                                      <div className="absolute bottom-2 right-2 text-xs text-muted-foreground/60 bg-background/80 px-2 py-1 rounded-md backdrop-blur-sm border border-border/50 pointer-events-none select-none">
                                        <span className="flex items-center gap-1">
                                          📏 Drag to resize
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                  {result.rawResponse && (
                                    <div className="bg-muted/30 rounded-lg p-3">
                                      <p className="text-xs font-medium text-muted-foreground mb-2">Raw JSON Response:</p>
                                      <div className="relative">
                                        <textarea 
                                          readOnly
                                          value={typeof result.rawResponse === 'string' ? result.rawResponse : JSON.stringify(result.rawResponse, null, 2)}
                                          className="w-full min-h-32 max-h-96 text-xs whitespace-pre-wrap font-mono bg-background rounded border p-2 pr-20 resize-y focus:outline-none focus:ring-2 focus:ring-primary/20"
                                            />
                                        <div className="absolute bottom-2 right-2 text-xs text-muted-foreground/60 bg-background/80 px-2 py-1 rounded-md backdrop-blur-sm border border-border/50 pointer-events-none select-none">
                                          <span className="flex items-center gap-1">
                                            📏 Drag to resize
                                          </span>
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          ) : (
                            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                              <div className="flex items-start gap-3">
                                <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
                                <div className="flex-1">
                                  <h4 className="font-medium text-red-800 dark:text-red-400 mb-2">Processing Failed</h4>
                                  <p className="text-sm text-red-700 dark:text-red-300 mb-3">
                                    {result.error || 'Unknown error occurred during OCR processing'}
                                  </p>
                                  
                                  {/* Additional error details */}
                                  <div className="text-xs text-red-600 dark:text-red-400 space-y-1">
                                    <p><span className="font-medium">Model:</span> {result.modelName}</p>
                                    <p><span className="font-medium">Prompt:</span> {result.promptName}</p>
                                    <p><span className="font-medium">Processing Time:</span> {result.processingTime}ms</p>
                                    
                                    {/* Troubleshooting suggestions */}
                                    <div className="mt-3 p-2 bg-red-100/50 dark:bg-red-900/30 rounded border border-red-300/50 dark:border-red-700/50">
                                      <p className="font-medium mb-1">💡 Troubleshooting Tips:</p>
                                      <ul className="text-xs space-y-1 list-disc list-inside">
                                        <li>Check if the model is still available on OpenRouter</li>
                                        <li>Verify your API key has access to this model</li>
                                        <li>Try a different model or prompt configuration</li>
                                        <li>Check the raw response below for more details</li>
                                      </ul>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                          </CardContent>
                        </Card>
                      ))}
                        </div>
                      </CardContent>
                    )}
                  </Card>
                )
              })}
            </div>
          )}
        </TabsContent>

        <TabsContent value="comparison" className="space-y-4">
          {testSessions.length === 0 ? (
            <Card>
              <CardContent className="pt-6 text-center">
                <BarChart className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">No test results yet. Run some tests to see comparisons here.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6">
              {/* Model Performance Comparison */}
              <Card>
                <CardHeader>
                  <CardTitle>Model Performance Comparison</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {getAllModels().map(model => {
                      const modelResults = testSessions.flatMap(session => 
                        session.results.filter(r => r.modelId === model.id)
                      )
                      
                      if (modelResults.length === 0) return null

                      const successRate = modelResults.filter(r => r.success).length / modelResults.length * 100
                      const avgCost = modelResults.reduce((sum, r) => sum + r.cost, 0) / modelResults.length
                      const avgTime = modelResults.reduce((sum, r) => sum + r.processingTime, 0) / modelResults.length
                      const avgAccuracy = modelResults.reduce((sum, r) => sum + calculateAccuracy(r.result), 0) / modelResults.length

                      return (
                        <Card key={model.id}>
                          <CardContent className="pt-6">
                            <div className="flex items-center justify-between mb-4">
                              <div className="flex items-center gap-2">
                                <span className="font-medium">{model.name}</span>
                                <Badge variant="outline">{model.provider}</Badge>
                                <Badge variant={model.tier === 'free' ? 'default' : 'outline'}>
                                  {model.tier === 'free' ? 'FREE' : `$${model.costPer1k}/1K`}
                                </Badge>
                              </div>
                              <span className="text-sm text-muted-foreground">
                                {modelResults.length} tests
                              </span>
                            </div>
                            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                              <div className="text-center">
                                <div className="text-2xl font-bold text-blue-500">{successRate.toFixed(1)}%</div>
                                <div className="text-sm text-muted-foreground">Success Rate</div>
                              </div>
                              <div className="text-center">
                                <div className="text-2xl font-bold text-green-500">${avgCost.toFixed(4)}</div>
                                <div className="text-sm text-muted-foreground">Avg Cost</div>
                              </div>
                              <div className="text-center">
                                <div className="text-2xl font-bold text-orange-500">{(avgTime / 1000).toFixed(1)}s</div>
                                <div className="text-sm text-muted-foreground">Avg Time</div>
                              </div>
                              <div className="text-center">
                                <div className="text-2xl font-bold text-purple-500">{avgAccuracy.toFixed(1)}%</div>
                                <div className="text-sm text-muted-foreground">Avg Accuracy</div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>

              {/* Prompt Performance Comparison */}
              <Card>
                <CardHeader>
                  <CardTitle>Prompt Performance Comparison</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {getAllPrompts().map(prompt => {
                      const promptResults = testSessions.flatMap(session => 
                        session.results.filter(r => r.promptId === prompt.id)
                      )
                      
                      if (promptResults.length === 0) return null

                      const successRate = promptResults.filter(r => r.success).length / promptResults.length * 100
                      const avgCost = promptResults.reduce((sum, r) => sum + r.cost, 0) / promptResults.length
                      const avgTime = promptResults.reduce((sum, r) => sum + r.processingTime, 0) / promptResults.length
                      const avgAccuracy = promptResults.reduce((sum, r) => sum + calculateAccuracy(r.result), 0) / promptResults.length

                      return (
                        <Card key={prompt.id}>
                          <CardContent className="pt-6">
                            <div className="flex items-center justify-between mb-4">
                              <div className="flex flex-col">
                                <div className="flex items-center gap-2">
                                  <span className="font-medium">{prompt.name}</span>
                                  <Badge variant="outline">{prompt.category}</Badge>
                                </div>
                                <p className="text-sm text-muted-foreground mt-1">{prompt.description}</p>
                              </div>
                              <span className="text-sm text-muted-foreground">
                                {promptResults.length} tests
                              </span>
                            </div>
                            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                              <div className="text-center">
                                <div className="text-2xl font-bold text-blue-500">{successRate.toFixed(1)}%</div>
                                <div className="text-sm text-muted-foreground">Success Rate</div>
                              </div>
                              <div className="text-center">
                                <div className="text-2xl font-bold text-green-500">${avgCost.toFixed(4)}</div>
                                <div className="text-sm text-muted-foreground">Avg Cost</div>
                              </div>
                              <div className="text-center">
                                <div className="text-2xl font-bold text-orange-500">{(avgTime / 1000).toFixed(1)}s</div>
                                <div className="text-sm text-muted-foreground">Avg Time</div>
                              </div>
                              <div className="text-center">
                                <div className="text-2xl font-bold text-purple-500">{avgAccuracy.toFixed(1)}%</div>
                                <div className="text-sm text-muted-foreground">Avg Accuracy</div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="statistics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Overall Statistics */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Total Tests</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {testSessions.reduce((sum, session) => sum + session.results.length, 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Across {testSessions.length} sessions
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Total Cost</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  ${testSessions.reduce((sum, session) => sum + session.totalCost, 0).toFixed(4)}
                </div>
                <p className="text-xs text-muted-foreground">
                  All tests combined
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Success Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {(() => {
                    const allResults = testSessions.flatMap(s => s.results)
                    const successRate = allResults.length > 0 
                      ? (allResults.filter(r => r.success).length / allResults.length * 100)
                      : 0
                    return successRate.toFixed(1)
                  })()}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Successful extractions
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Avg Accuracy</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">
                  {(() => {
                    const allResults = testSessions.flatMap(s => s.results.filter(r => r.success))
                    const avgAccuracy = allResults.length > 0 
                      ? allResults.reduce((sum, r) => sum + calculateAccuracy(r.result), 0) / allResults.length
                      : 0
                    return avgAccuracy.toFixed(1)
                  })()}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Field completion rate
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Cost Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle>Cost Breakdown by Model</CardTitle>
            </CardHeader>
            <CardContent>
              {(() => {
                const costByModel = testSessions.flatMap(s => s.results).reduce((acc, result) => {
                  if (!acc[result.modelId]) {
                    acc[result.modelId] = {
                      name: result.modelName,
                      totalCost: 0,
                      testCount: 0
                    }
                  }
                  acc[result.modelId].totalCost += result.cost
                  acc[result.modelId].testCount += 1
                  return acc
                }, {} as Record<string, any>)

                return (
                  <div className="space-y-3">
                    {Object.entries(costByModel).map(([modelId, data]) => (
                      <div key={modelId} className="flex items-center justify-between p-3 border rounded">
                        <div>
                          <span className="font-medium">{data.name}</span>
                          <span className="text-sm text-muted-foreground ml-2">({data.testCount} tests)</span>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">${data.totalCost.toFixed(4)}</div>
                          <div className="text-sm text-muted-foreground">
                            ${(data.totalCost / data.testCount).toFixed(4)} avg
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )
              })()}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          {/* Custom Models */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Custom Models
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                {customModels.map((model, index) => (
                  <div key={model.id} className="flex items-center justify-between p-3 border rounded">
                    <div>
                      <span className="font-medium">{model.name}</span>
                      <Badge variant="outline" className="ml-2">{model.provider}</Badge>
                      {model.costPer1k && (
                        <Badge variant="outline" className="ml-2">${model.costPer1k}/1K</Badge>
                      )}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const newModels = customModels.filter((_, i) => i !== index)
                        setCustomModels(newModels)
                        saveCustomSettings()
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>

              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Custom Model
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add Custom OpenRouter Model</DialogTitle>
                  </DialogHeader>
                  <form onSubmit={(e) => {
                    e.preventDefault()
                    const formData = new FormData(e.currentTarget)
                    const newModel: CustomModel = {
                      id: formData.get('id') as string,
                      name: formData.get('name') as string,
                      provider: formData.get('provider') as string,
                      costPer1k: parseFloat(formData.get('costPer1k') as string) || undefined,
                      description: formData.get('description') as string || undefined
                    }
                    setCustomModels([...customModels, newModel])
                    saveCustomSettings()
                  }} className="space-y-4">
                    <div>
                      <Label htmlFor="id">Model ID (from OpenRouter)</Label>
                      <Input name="id" placeholder="e.g., openai/gpt-4-vision-preview" required />
                    </div>
                    <div>
                      <Label htmlFor="name">Display Name</Label>
                      <Input name="name" placeholder="e.g., GPT-4 Vision Preview" required />
                    </div>
                    <div>
                      <Label htmlFor="provider">Provider</Label>
                      <Input name="provider" placeholder="e.g., OpenAI" required />
                    </div>
                    <div>
                      <Label htmlFor="costPer1k">Cost per 1K tokens</Label>
                      <Input name="costPer1k" type="number" step="0.001" placeholder="e.g., 0.01" />
                    </div>
                    <div>
                      <Label htmlFor="description">Description</Label>
                      <Input name="description" placeholder="Brief description of the model" />
                    </div>
                    <Button type="submit">Add Model</Button>
                  </form>
                </DialogContent>
              </Dialog>
            </CardContent>
          </Card>

          {/* Custom Prompts */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Custom Prompts
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                {customPrompts.map((prompt, index) => (
                  <div key={prompt.id} className="p-3 border rounded">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{prompt.name}</span>
                        <Badge variant="outline">{prompt.category}</Badge>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const newPrompts = customPrompts.filter((_, i) => i !== index)
                          setCustomPrompts(newPrompts)
                          saveCustomSettings()
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">{prompt.description}</p>
                    <p className="text-xs text-muted-foreground line-clamp-2">{prompt.content}</p>
                  </div>
                ))}
              </div>

              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Custom Prompt
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Add Custom Prompt</DialogTitle>
                  </DialogHeader>
                  <form onSubmit={(e) => {
                    e.preventDefault()
                    const formData = new FormData(e.currentTarget)
                    const newPrompt: TestPrompt = {
                      id: `custom-${Date.now()}`,
                      name: formData.get('name') as string,
                      content: formData.get('content') as string,
                      description: formData.get('description') as string,
                      category: formData.get('category') as string
                    }
                    setCustomPrompts([...customPrompts, newPrompt])
                    saveCustomSettings()
                  }} className="space-y-4">
                    <div>
                      <Label htmlFor="name">Prompt Name</Label>
                      <Input name="name" placeholder="e.g., High Accuracy Extraction" required />
                    </div>
                    <div>
                      <Label htmlFor="category">Category</Label>
                      <Input name="category" placeholder="e.g., Custom, Specialized" required />
                    </div>
                    <div>
                      <Label htmlFor="description">Description</Label>
                      <Input name="description" placeholder="Brief description of what this prompt does" required />
                    </div>
                    <div>
                      <Label htmlFor="content">Prompt Content</Label>
                      <Textarea 
                        name="content" 
                        placeholder="Enter the full prompt text here..."
                        className="min-h-32"
                        required 
                      />
                    </div>
                    <Button type="submit">Add Prompt</Button>
                  </form>
                </DialogContent>
              </Dialog>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}