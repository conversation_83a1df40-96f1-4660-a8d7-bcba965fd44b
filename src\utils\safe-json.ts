/**
 * Safe JSON utilities to handle circular references and React elements
 * Prevents "Converting circular structure to JSON" errors
 */

export function safeStringify(obj: any, space?: number): string {
  const seen = new WeakSet();
  
  const replacer = (key: string, value: any) => {
    // Skip React fiber properties that cause circular references
    if (key.startsWith('__reactFiber$') || 
        key.startsWith('__reactInternalInstance$') ||
        key.startsWith('__reactContainer$') ||
        key === '_owner' || 
        key === '_source' || 
        key === '_self') {
      return undefined;
    }
    
    // Handle circular references
    if (typeof value === 'object' && value !== null) {
      if (seen.has(value)) {
        return '[Circular Reference]';
      }
      seen.add(value);
    }
    
    // Skip DOM elements and React refs/elements
    if (value instanceof HTMLElement || 
        value instanceof Node || 
        (value && typeof value === 'object' && value.$$typeof)) {
      return '[DOM Element/React Ref]';
    }
    
    return value;
  };
  
  return JSON.stringify(obj, replacer, space);
}

export function createCircularSafeReplacer() {
  const ancestors: any[] = [];
  return function(this: any, key: string, value: any) {
    if (typeof value !== "object" || value === null) {
      return value;
    }
    
    // Skip React-specific properties that can cause circular references
    if (key.startsWith('__reactFiber$') || 
        key.startsWith('__reactInternalInstance$') ||
        key.startsWith('__reactContainer$') ||
        key === '_owner' || 
        key === '_source' || 
        key === '_self' ||
        key === 'stateNode' ||
        key === 'return' ||
        key === 'child' ||
        key === 'sibling') {
      return undefined;
    }
    
    // Handle DOM elements
    if (value instanceof HTMLElement || value instanceof Node) {
      return `[${value.constructor.name}]`;
    }
    
    // Handle React elements/components
    if (value && typeof value === 'object' && value.$$typeof) {
      return '[React Element]';
    }
    
    // Handle circular references
    while (ancestors.length > 0 && ancestors.at(-1) !== this) {
      ancestors.pop();
    }
    if (ancestors.includes(value)) {
      return "[Circular]";
    }
    ancestors.push(value);
    return value;
  };
}

/**
 * Safely extract data from objects that might contain React refs
 * Returns only serializable data
 */
export function extractSerializableData(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }
  
  if (typeof obj !== 'object') {
    return obj;
  }
  
  // Handle arrays
  if (Array.isArray(obj)) {
    return obj.map(extractSerializableData).filter(item => item !== undefined);
  }
  
  // Handle DOM elements
  if (obj instanceof HTMLElement) {
    return {
      tagName: obj.tagName,
      id: obj.id || undefined,
      className: obj.className || undefined
    };
  }
  
  if (obj instanceof Node) {
    return {
      nodeName: obj.nodeName,
      nodeType: obj.nodeType
    };
  }
  
  // Handle React elements
  if (obj.$$typeof) {
    return '[React Element]';
  }
  
  // Handle regular objects
  const result: any = {};
  for (const [key, value] of Object.entries(obj)) {
    // Skip React-specific properties
    if (key.startsWith('__react') || 
        key.startsWith('_') || 
        key === 'stateNode' || 
        key === 'return' || 
        key === 'child' || 
        key === 'sibling') {
      continue;
    }
    
    const serializedValue = extractSerializableData(value);
    if (serializedValue !== undefined) {
      result[key] = serializedValue;
    }
  }
  
  return result;
}

/**
 * Console log function that safely handles objects with circular references
 */
export function safeConsoleLog(message: string, obj?: any) {
  if (obj) {
    try {
      console.log(message, safeStringify(obj, 2));
    } catch (error) {
      console.log(message, '[Object with circular references - using fallback]');
      console.log(extractSerializableData(obj));
    }
  } else {
    console.log(message);
  }
}

const SafeJSON = {
  safeStringify,
  createCircularSafeReplacer,
  extractSerializableData,
  safeConsoleLog
};

export default SafeJSON;
