const fs = require('fs').promises;
const path = require('path');

class CostTracker {
  constructor() {
    this.dataFile = path.join(process.cwd(), 'data', 'cost-tracking.json');
    this.data = {
      transactions: [],
      limits: {
        daily: 2.0,  // $2 per day
        monthly: 10.0, // $10 per month
        total: 100.0   // $100 total
      },
      settings: {
        enableTracking: true,
        enableLimits: true,
        enableAlerts: true,
        alertThresholds: {
          daily: 0.8,    // 80% of daily limit
          monthly: 0.8,  // 80% of monthly limit
          total: 0.9     // 90% of total limit
        }
      },
      statistics: {
        totalSpent: 0,
        totalRequests: 0,
        averageCostPerRequest: 0,
        modelUsage: {},
        providerUsage: {}
      }
    };
    this.rateLimits = new Map();
    this.isInitialized = false;
    // Don't call initialize in constructor - let the service initialize it
  }

  async initialize() {
    if (this.isInitialized) return;
    
    try {
      await this.loadData();
      await this.cleanup();
      this.isInitialized = true;
      console.log('Cost tracker initialized');
    } catch (error) {
      console.error('Failed to initialize cost tracker:', error);
      await this.saveData();
      this.isInitialized = true;
    }
  }

  async loadData() {
    try {
      const data = await fs.readFile(this.dataFile, 'utf-8');
      const loadedData = JSON.parse(data);
      
      // Merge loaded data with defaults, ensuring critical arrays are always initialized
      this.data = { 
        ...this.data, 
        ...loadedData,
        // Ensure transactions is always an array
        transactions: Array.isArray(loadedData.transactions) ? loadedData.transactions : [],
        // Ensure other critical structures exist
        limits: { ...this.data.limits, ...(loadedData.limits || {}) },
        settings: { ...this.data.settings, ...(loadedData.settings || {}) },
        statistics: { ...this.data.statistics, ...(loadedData.statistics || {}) }
      };
    } catch (error) {
      console.log('Creating new cost tracking file');
      await this.ensureDataDirectory();
      await this.saveData();
    }
  }

  async saveData() {
    await this.ensureDataDirectory();
    await fs.writeFile(this.dataFile, JSON.stringify(this.data, null, 2));
  }

  async ensureDataDirectory() {
    const dataDir = path.dirname(this.dataFile);
    try {
      await fs.mkdir(dataDir, { recursive: true });
    } catch (error) {
      // Directory already exists
    }
  }

  async cleanup() {
    // Remove transactions older than 90 days
    const cutoff = new Date();
    cutoff.setDate(cutoff.getDate() - 90);
    
    const initialCount = this.data.transactions.length;
    this.data.transactions = this.data.transactions.filter(t => 
      new Date(t.timestamp) > cutoff
    );
    
    const removed = initialCount - this.data.transactions.length;
    if (removed > 0) {
      console.log(`Cleaned up ${removed} old transactions`);
      await this.saveData();
    }
  }

  async recordTransaction(transaction) {
    if (!this.data.settings.enableTracking) return;

    const enrichedTransaction = {
      id: this.generateTransactionId(),
      timestamp: new Date().toISOString(),
      ...transaction
    };

    this.data.transactions.push(enrichedTransaction);
    this.updateStatistics(enrichedTransaction);
    
    await this.saveData();
    await this.checkAlerts(enrichedTransaction);
    
    return enrichedTransaction;
  }

  generateTransactionId() {
    return `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  updateStatistics(transaction) {
    const { cost, modelId, providerId, success } = transaction;
    
    if (success) {
      this.data.statistics.totalSpent += cost;
      this.data.statistics.totalRequests += 1;
      this.data.statistics.averageCostPerRequest = 
        this.data.statistics.totalSpent / this.data.statistics.totalRequests;
      
      // Update model usage
      if (!this.data.statistics.modelUsage[modelId]) {
        this.data.statistics.modelUsage[modelId] = { requests: 0, cost: 0 };
      }
      this.data.statistics.modelUsage[modelId].requests += 1;
      this.data.statistics.modelUsage[modelId].cost += cost;
      
      // Update provider usage
      if (!this.data.statistics.providerUsage[providerId]) {
        this.data.statistics.providerUsage[providerId] = { requests: 0, cost: 0 };
      }
      this.data.statistics.providerUsage[providerId].requests += 1;
      this.data.statistics.providerUsage[providerId].cost += cost;
    }
  }

  async checkAlerts(transaction) {
    if (!this.data.settings.enableAlerts) return;

    const today = new Date().toISOString().split('T')[0];
    const thisMonth = today.substring(0, 7);
    
    const dailySpend = this.getDailySpend(today);
    const monthlySpend = this.getMonthlySpend(thisMonth);
    const totalSpend = this.data.statistics.totalSpent;
    
    const alerts = [];
    
    // Daily limit check
    if (dailySpend >= this.data.limits.daily * this.data.settings.alertThresholds.daily) {
      alerts.push({
        type: 'daily_threshold',
        current: dailySpend,
        limit: this.data.limits.daily,
        threshold: this.data.settings.alertThresholds.daily
      });
    }
    
    // Monthly limit check
    if (monthlySpend >= this.data.limits.monthly * this.data.settings.alertThresholds.monthly) {
      alerts.push({
        type: 'monthly_threshold',
        current: monthlySpend,
        limit: this.data.limits.monthly,
        threshold: this.data.settings.alertThresholds.monthly
      });
    }
    
    // Total limit check
    if (totalSpend >= this.data.limits.total * this.data.settings.alertThresholds.total) {
      alerts.push({
        type: 'total_threshold',
        current: totalSpend,
        limit: this.data.limits.total,
        threshold: this.data.settings.alertThresholds.total
      });
    }
    
    // Process alerts
    for (const alert of alerts) {
      await this.processAlert(alert, transaction);
    }
  }

  async processAlert(alert, transaction) {
    console.warn(`💰 COST ALERT: ${alert.type.toUpperCase()}`, {
      current: `$${alert.current.toFixed(4)}`,
      limit: `$${alert.limit.toFixed(2)}`,
      threshold: `${(alert.threshold * 100).toFixed(0)}%`,
      model: transaction.modelId,
      provider: transaction.providerId
    });
    
    // Could integrate with email/Slack notifications here
    // Could also trigger automatic model switching to cheaper alternatives
  }

  checkLimits(modelId, providerId, estimatedCost = 0) {
    if (!this.data.settings.enableLimits) return { allowed: true };

    const today = new Date().toISOString().split('T')[0];
    const thisMonth = today.substring(0, 7);
    
    const dailySpend = this.getDailySpend(today);
    const monthlySpend = this.getMonthlySpend(thisMonth);
    const totalSpend = this.data.statistics.totalSpent;
    
    const checks = {
      daily: {
        current: dailySpend,
        limit: this.data.limits.daily,
        wouldExceed: dailySpend + estimatedCost > this.data.limits.daily
      },
      monthly: {
        current: monthlySpend,
        limit: this.data.limits.monthly,
        wouldExceed: monthlySpend + estimatedCost > this.data.limits.monthly
      },
      total: {
        current: totalSpend,
        limit: this.data.limits.total,
        wouldExceed: totalSpend + estimatedCost > this.data.limits.total
      }
    };
    
    const violations = [];
    
    for (const [type, check] of Object.entries(checks)) {
      if (check.wouldExceed) {
        violations.push({
          type,
          current: check.current,
          limit: check.limit,
          estimatedCost
        });
      }
    }
    
    return {
      allowed: violations.length === 0,
      violations,
      checks
    };
  }

  getDailySpend(date) {
    return this.data.transactions
      .filter(t => t.timestamp.startsWith(date) && t.success)
      .reduce((sum, t) => sum + t.cost, 0);
  }

  getMonthlySpend(month) {
    return this.data.transactions
      .filter(t => t.timestamp.startsWith(month) && t.success)
      .reduce((sum, t) => sum + t.cost, 0);
  }

  // Rate limiting functionality
  checkRateLimit(modelId, limit) {
    const key = `rate_${modelId}`;
    const now = Date.now();
    const windowStart = now - (limit.period * 1000);
    
    if (!this.rateLimits.has(key)) {
      this.rateLimits.set(key, []);
    }
    
    const requests = this.rateLimits.get(key);
    
    // Clean old requests
    const validRequests = requests.filter(time => time > windowStart);
    this.rateLimits.set(key, validRequests);
    
    // Check if limit would be exceeded
    if (validRequests.length >= limit.requests) {
      const oldestRequest = Math.min(...validRequests);
      const resetTime = oldestRequest + (limit.period * 1000);
      
      return {
        allowed: false,
        remaining: 0,
        resetAt: new Date(resetTime).toISOString(),
        retryAfter: Math.ceil((resetTime - now) / 1000)
      };
    }
    
    return {
      allowed: true,
      remaining: limit.requests - validRequests.length,
      resetAt: new Date(now + (limit.period * 1000)).toISOString(),
      retryAfter: 0
    };
  }

  recordRateLimit(modelId) {
    const key = `rate_${modelId}`;
    const now = Date.now();
    
    if (!this.rateLimits.has(key)) {
      this.rateLimits.set(key, []);
    }
    
    const requests = this.rateLimits.get(key);
    requests.push(now);
    this.rateLimits.set(key, requests);
  }

  getSpendingReport(period = '7d') {
    const periods = {
      '1d': 1,
      '7d': 7,
      '30d': 30,
      '90d': 90
    };
    
    const days = periods[period] || 7;
    const cutoff = new Date();
    cutoff.setDate(cutoff.getDate() - days);
    
    const transactions = this.data.transactions.filter(t => 
      new Date(t.timestamp) > cutoff && t.success
    );
    
    const totalCost = transactions.reduce((sum, t) => sum + t.cost, 0);
    const totalRequests = transactions.length;
    
    // Group by model
    const modelBreakdown = {};
    transactions.forEach(t => {
      if (!modelBreakdown[t.modelId]) {
        modelBreakdown[t.modelId] = { requests: 0, cost: 0 };
      }
      modelBreakdown[t.modelId].requests += 1;
      modelBreakdown[t.modelId].cost += t.cost;
    });
    
    // Group by provider
    const providerBreakdown = {};
    transactions.forEach(t => {
      if (!providerBreakdown[t.providerId]) {
        providerBreakdown[t.providerId] = { requests: 0, cost: 0 };
      }
      providerBreakdown[t.providerId].requests += 1;
      providerBreakdown[t.providerId].cost += t.cost;
    });
    
    // Daily breakdown
    const dailyBreakdown = {};
    transactions.forEach(t => {
      const date = t.timestamp.split('T')[0];
      if (!dailyBreakdown[date]) {
        dailyBreakdown[date] = { requests: 0, cost: 0 };
      }
      dailyBreakdown[date].requests += 1;
      dailyBreakdown[date].cost += t.cost;
    });
    
    return {
      period,
      totalCost,
      totalRequests,
      averageCostPerRequest: totalRequests > 0 ? totalCost / totalRequests : 0,
      modelBreakdown: Object.entries(modelBreakdown)
        .map(([model, data]) => ({ model, ...data }))
        .sort((a, b) => b.cost - a.cost),
      providerBreakdown: Object.entries(providerBreakdown)
        .map(([provider, data]) => ({ provider, ...data }))
        .sort((a, b) => b.cost - a.cost),
      dailyBreakdown: Object.entries(dailyBreakdown)
        .map(([date, data]) => ({ date, ...data }))
        .sort((a, b) => a.date.localeCompare(b.date))
    };
  }

  getCurrentLimits() {
    const today = new Date().toISOString().split('T')[0];
    const thisMonth = today.substring(0, 7);
    
    return {
      daily: {
        spent: this.getDailySpend(today),
        limit: this.data.limits.daily,
        remaining: Math.max(0, this.data.limits.daily - this.getDailySpend(today))
      },
      monthly: {
        spent: this.getMonthlySpend(thisMonth),
        limit: this.data.limits.monthly,
        remaining: Math.max(0, this.data.limits.monthly - this.getMonthlySpend(thisMonth))
      },
      total: {
        spent: this.data.statistics.totalSpent,
        limit: this.data.limits.total,
        remaining: Math.max(0, this.data.limits.total - this.data.statistics.totalSpent)
      }
    };
  }

  async updateLimits(newLimits) {
    this.data.limits = { ...this.data.limits, ...newLimits };
    await this.saveData();
    return this.data.limits;
  }

  async updateSettings(newSettings) {
    this.data.settings = { ...this.data.settings, ...newSettings };
    await this.saveData();
    return this.data.settings;
  }

  exportData(format = 'json') {
    if (format === 'json') {
      return JSON.stringify(this.data, null, 2);
    } else if (format === 'csv') {
      const headers = ['timestamp', 'modelId', 'providerId', 'cost', 'tokens', 'success'];
      const rows = this.data.transactions.map(t => [
        t.timestamp,
        t.modelId,
        t.providerId,
        t.cost,
        t.tokens || 0,
        t.success
      ]);
      
      return [headers, ...rows].map(row => row.join(',')).join('\n');
    }
    
    throw new Error(`Unsupported export format: ${format}`);
  }

  getStatistics() {
    const transactions = this.data.transactions || [];
    return {
      ...this.data.statistics,
      currentLimits: this.getCurrentLimits(),
      recentTransactions: transactions
        .slice(-10)
        .reverse()
        .map(t => ({
          timestamp: t.timestamp,
          modelId: t.modelId,
          cost: t.cost,
          success: t.success
        }))
    };
  }
}

module.exports = CostTracker;