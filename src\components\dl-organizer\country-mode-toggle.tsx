"use client"

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

// Custom flag components
const USFlag = ({ size = 20 }: { size?: number }) => (
  <svg width={size} height={size * 0.75} viewBox="0 0 27 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="27" height="20" fill="#B22234"/>
    <rect y="1.54" width="27" height="1.54" fill="white"/>
    <rect y="4.62" width="27" height="1.54" fill="white"/>
    <rect y="7.69" width="27" height="1.54" fill="white"/>
    <rect y="10.77" width="27" height="1.54" fill="white"/>
    <rect y="13.85" width="27" height="1.54" fill="white"/>
    <rect y="16.92" width="27" height="1.54" fill="white"/>
    <rect width="10.8" height="10.77" fill="#3C3B6E"/>
  </svg>
)

const AUSFlag = ({ size = 20 }: { size?: number }) => (
  <svg width={size} height={size * 0.75} viewBox="0 0 27 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="27" height="20" fill="#012169"/>
    <rect width="10.8" height="10" fill="#012169"/>
    <polygon points="0,0 10.8,0 0,10" fill="#CE1124"/>
    <polygon points="10.8,0 10.8,10 0,10" fill="#CE1124"/>
    <rect x="4.32" y="0" width="2.16" height="10" fill="white"/>
    <rect x="0" y="4" width="10.8" height="2" fill="white"/>
    <rect x="4.86" y="0" width="1.08" height="10" fill="#CE1124"/>
    <rect x="0" y="4.5" width="10.8" height="1" fill="#CE1124"/>
    <polygon points="16,14 17,13 17,14 18,14 18,15 17,15 17,16 16,15 15,15 15,14" fill="white"/>
    <polygon points="22,8 23,7 23,8 24,8 24,9 23,9 23,10 22,9 21,9 21,8" fill="white"/>
  </svg>
)

interface CountryModeToggleProps {
  value: 'us' | 'australian'
  onChange: (value: 'us' | 'australian') => void
  className?: string
}

export function CountryModeToggle({ value, onChange, className }: CountryModeToggleProps) {
  const [mode, setMode] = useState<'us' | 'australian'>(value)

  useEffect(() => {
    setMode(value)
  }, [value])

  const handleToggle = () => {
    const newMode = mode === 'us' ? 'australian' : 'us'
    setMode(newMode)
    onChange(newMode)
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Button
        variant={mode === 'us' ? 'default' : 'outline'}
        size="sm"
        onClick={() => {
          setMode('us')
          onChange('us')
        }}
        className="flex items-center gap-2"
      >
        <USFlag size={16} />
        <span>US</span>
      </Button>
      <Button
        variant={mode === 'australian' ? 'default' : 'outline'}
        size="sm"
        onClick={() => {
          setMode('australian')
          onChange('australian')
        }}
        className="flex items-center gap-2"
      >
        <AUSFlag size={16} />
        <span>AUS</span>
      </Button>
    </div>
  )
}