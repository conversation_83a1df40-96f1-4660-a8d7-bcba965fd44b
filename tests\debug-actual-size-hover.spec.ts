import { test, expect } from '@playwright/test';

// Debug actual size hover preview
test.describe('Debug Actual Size Hover', () => {
  test('should debug actual size hover preview DOM structure', async ({ page }) => {
    await page.goto('http://localhost:3001/ocr-testing');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Find first thumbnail and hover
    const firstThumbnail = page.locator('img[width="48"]').first();
    const groupContainer = firstThumbnail.locator('..').locator('..');
    
    console.log('=== BEFORE HOVER ===');
    
    // Check what images exist before hover
    const allImages = page.locator('img');
    const imageCount = await allImages.count();
    console.log(`Total images found: ${imageCount}`);
    
    // Check for any images with sizes attribute
    const sizesImages = page.locator('img[sizes]');
    const sizesCount = await sizesImages.count();
    console.log(`Images with sizes attribute: ${sizesCount}`);
    
    // Check current hover preview structure
    const hoverPreviewDivs = page.locator('div.absolute');
    const hoverDivCount = await hoverPreviewDivs.count();
    console.log(`Hover preview divs found: ${hoverDivCount}`);
    
    if (hoverDivCount > 0) {
      const firstHoverDiv = hoverPreviewDivs.first();
      const hoverDivClasses = await firstHoverDiv.getAttribute('class');
      console.log('First hover div classes:', hoverDivClasses);
      
      // Check what's inside the hover div
      const hoverDivImages = firstHoverDiv.locator('img');
      const hoverDivImageCount = await hoverDivImages.count();
      console.log(`Images inside first hover div: ${hoverDivImageCount}`);
      
      if (hoverDivImageCount > 0) {
        const hoverImage = hoverDivImages.first();
        const hoverImageSrc = await hoverImage.getAttribute('src');
        const hoverImageSizes = await hoverImage.getAttribute('sizes');
        const hoverImageWidth = await hoverImage.getAttribute('width');
        const hoverImageHeight = await hoverImage.getAttribute('height');
        
        console.log('Hover image src:', hoverImageSrc);
        console.log('Hover image sizes:', hoverImageSizes);
        console.log('Hover image width:', hoverImageWidth);
        console.log('Hover image height:', hoverImageHeight);
      }
    }
    
    console.log('=== HOVERING ===');
    
    // Hover over the group container
    await groupContainer.hover();
    await page.waitForTimeout(1000);
    
    // Check what changed after hover
    const afterHoverImages = page.locator('img');
    const afterHoverImageCount = await afterHoverImages.count();
    console.log(`Total images after hover: ${afterHoverImageCount}`);
    
    const afterHoverSizesImages = page.locator('img[sizes]');
    const afterHoverSizesCount = await afterHoverSizesImages.count();
    console.log(`Images with sizes attribute after hover: ${afterHoverSizesCount}`);
    
    // Check if any images became visible
    const visibleImages = page.locator('img:visible');
    const visibleImageCount = await visibleImages.count();
    console.log(`Visible images after hover: ${visibleImageCount}`);
    
    // Check the hover preview divs again
    const afterHoverPreviewDivs = page.locator('div.absolute');
    const afterHoverDivCount = await afterHoverPreviewDivs.count();
    console.log(`Hover preview divs after hover: ${afterHoverDivCount}`);
    
    // Check if any of the hover divs are visible
    for (let i = 0; i < Math.min(afterHoverDivCount, 3); i++) {
      const hoverDiv = afterHoverPreviewDivs.nth(i);
      const isVisible = await hoverDiv.isVisible();
      const computedStyle = await hoverDiv.evaluate((el) => {
        const style = window.getComputedStyle(el);
        return {
          display: style.display,
          visibility: style.visibility,
          opacity: style.opacity
        };
      });
      
      console.log(`Hover div ${i} visible: ${isVisible}, styles:`, computedStyle);
      
      if (isVisible) {
        const hoverDivImages = hoverDiv.locator('img');
        const hoverImageCount = await hoverDivImages.count();
        console.log(`Visible hover div ${i} has ${hoverImageCount} images`);
        
        if (hoverImageCount > 0) {
          const hoverImage = hoverDivImages.first();
          const hoverImageSizes = await hoverImage.getAttribute('sizes');
          const hoverImageClasses = await hoverImage.getAttribute('class');
          
          console.log(`Hover image ${i} sizes: ${hoverImageSizes}`);
          console.log(`Hover image ${i} classes: ${hoverImageClasses}`);
        }
      }
    }
    
    // Take screenshot for debugging
    await page.screenshot({ path: 'debug-actual-size-hover.png' });
    
    console.log('=== SEARCHING FOR SPECIFIC SELECTORS ===');
    
    // Try different selectors
    const selectors = [
      'img[sizes="100vw"]',
      'div.absolute img[sizes]',
      'div.absolute img',
      'img[sizes]',
      'img[width="0"]',
      'img[height="0"]'
    ];
    
    for (const selector of selectors) {
      const elements = page.locator(selector);
      const count = await elements.count();
      console.log(`Selector "${selector}": ${count} elements`);
      
      if (count > 0) {
        const firstElement = elements.first();
        const isVisible = await firstElement.isVisible();
        console.log(`  First element visible: ${isVisible}`);
        
        if (isVisible) {
          const src = await firstElement.getAttribute('src');
          console.log(`  First element src: ${src}`);
        }
      }
    }
  });
});