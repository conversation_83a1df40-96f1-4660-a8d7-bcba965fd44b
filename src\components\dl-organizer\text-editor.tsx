"use client"

import { useState, useEffect, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { FileText, Save, Plus, RefreshCw, AlertCircle, Eye, Edit3, Trash2, PencilLine } from 'lucide-react'
import { cn } from '@/lib/utils'

interface TextFile {
  name: string
  path: string
  content: string
  lastModified: Date
  size: number
  exists: boolean
}

interface TextEditorProps {
  folderPath: string
  className?: string
}

export default function TextEditor({ folderPath, className }: TextEditorProps) {
  const [textFiles, setTextFiles] = useState<TextFile[]>([])
  const [selectedFile, setSelectedFile] = useState<TextFile | null>(null)
  const [content, setContent] = useState('')
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setSaving] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [newFileName, setNewFileName] = useState('')
  const [expandedFile, setExpandedFile] = useState<string | null>(null)
  const [renamingFile, setRenamingFile] = useState<string | null>(null)
  const [renameValue, setRenameValue] = useState('')
  const [loadingFileId, setLoadingFileId] = useState<string | null>(null)

  const loadTextFiles = useCallback(async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('http://localhost:3003/api/filesystem/text-files', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ folderPath })
      })
      
      if (!response.ok) {
        throw new Error('Failed to load text files')
      }
      
      const data = await response.json()
      setTextFiles(data.files || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load text files')
      console.error('Error loading text files:', err)
    } finally {
      setIsLoading(false)
    }
  }, [folderPath])

  useEffect(() => {
    console.log('📁 TextEditor mounted/folderPath changed:', folderPath)
    
    // Clear previous state when folder changes
    setSelectedFile(null)
    setContent('')
    setIsEditing(false)
    setError(null)
    setLoadingFileId(null)
    
    if (folderPath) {
      loadTextFiles()
    }
  }, [folderPath, loadTextFiles])

  const loadFileContent = async (file: TextFile) => {
    // Guard against loading the same file multiple times or during loading
    if (loadingFileId === file.path) {
      console.log('🚫 Already loading this file, skipping request for:', file.name)
      return
    }
    
    if (selectedFile?.path === file.path && content) {
      console.log('🎯 File already loaded:', file.name)
      return
    }
    
    console.log('🔍 Loading file content for:', file.name, file.path)
    setLoadingFileId(file.path)
    setError(null)
    
    try {
      const url = `http://localhost:3003/api/filesystem/text-content?filePath=${encodeURIComponent(file.path)}`
      console.log('📡 Making API call to:', url)
      
      const response = await fetch(url, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      })
      
      console.log('📊 Response status:', response.status, response.statusText)
      
      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ API Error response:', errorText)
        throw new Error(`Failed to load file content: ${response.status} ${response.statusText}`)
      }
      
      const data = await response.json()
      console.log('✅ File content loaded:', { contentLength: data.content?.length || 0 })
      
      setContent(data.content || '')
      setSelectedFile(file)
      setIsEditing(false)
      
      console.log('🎯 State updated - selectedFile set to:', file.name)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load file content'
      console.error('❌ Error loading file content:', err)
      setError(errorMessage)
    } finally {
      setLoadingFileId(null)
    }
  }

  const saveFileContent = async () => {
    if (!selectedFile) return
    
    setSaving(true)
    setError(null)
    
    try {
      const response = await fetch('http://localhost:3003/api/filesystem/text-content', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          filePath: selectedFile.path,
          content: content
        })
      })
      
      if (!response.ok) {
        throw new Error('Failed to save file')
      }
      
      setIsEditing(false)
      await loadTextFiles() // Refresh the file list
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save file')
      console.error('Error saving file:', err)
    } finally {
      setSaving(false)
    }
  }

  const createNewFile = async () => {
    if (!newFileName.trim()) return
    
    const fileName = newFileName.trim()
    const fullPath = `${folderPath}\\${fileName}${fileName.endsWith('.txt') ? '' : '.txt'}`
    
    setSaving(true)
    setError(null)
    
    try {
      const response = await fetch('http://localhost:3003/api/filesystem/text-content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          filePath: fullPath,
          content: ''
        })
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create file')
      }
      
      setNewFileName('')
      setShowCreateDialog(false)
      await loadTextFiles()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create file')
      console.error('Error creating file:', err)
    } finally {
      setSaving(false)
    }
  }

  const deleteFile = async (file: TextFile) => {
    if (!confirm(`Are you sure you want to delete ${file.name}?`)) return
    
    setSaving(true)
    setError(null)
    
    try {
      const response = await fetch('http://localhost:3003/api/filesystem/text-content', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ filePath: file.path })
      })
      
      if (!response.ok) {
        throw new Error('Failed to delete file')
      }
      
      if (selectedFile?.path === file.path) {
        setSelectedFile(null)
        setContent('')
      }
      
      await loadTextFiles()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete file')
      console.error('Error deleting file:', err)
    } finally {
      setSaving(false)
    }
  }

  const startRename = (file: TextFile) => {
    setRenamingFile(file.path)
    setRenameValue(file.name)
  }

  const cancelRename = () => {
    setRenamingFile(null)
    setRenameValue('')
  }

  const renameFile = async (file: TextFile) => {
    if (!renameValue.trim() || renameValue === file.name) {
      cancelRename()
      return
    }

    setSaving(true)
    setError(null)
    
    try {
      // First, check if it's a JSON or TXT file that might have associated files
      const extension = file.name.substring(file.name.lastIndexOf('.'))
      const newPath = file.path.replace(file.name, renameValue)
      
      const response = await fetch('http://localhost:3003/api/filesystem/rename', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          oldPath: file.path,
          newPath: newPath,
          updateAssociatedFiles: true // This will update related JSON/TXT files
        })
      })
      
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to rename file')
      }
      
      // If this was the selected file, update its path
      if (selectedFile?.path === file.path) {
        setSelectedFile({
          ...selectedFile,
          path: newPath,
          name: renameValue
        })
      }
      
      cancelRename()
      await loadTextFiles()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to rename file')
      console.error('Error renaming file:', err)
    } finally {
      setSaving(false)
    }
  }

  const handleStartEditing = () => {
    setIsEditing(true)
  }

  const handleCancelEditing = () => {
    setIsEditing(false)
    // Reset content to original
    if (selectedFile) {
      loadFileContent(selectedFile)
    }
  }

  console.log('🔄 TextEditor render - selectedFile:', selectedFile?.name || 'none')
  
  return (
    <Card className={cn("h-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Text Files</CardTitle>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={loadTextFiles}
              disabled={isLoading}
            >
              <RefreshCw className={`w-4 h-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Plus className="w-4 h-4 mr-1" />
                  New File
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Text File</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">File Name</label>
                    <input
                      type="text"
                      value={newFileName}
                      onChange={(e) => setNewFileName(e.target.value)}
                      placeholder="document.txt"
                      className="w-full p-2 border rounded"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={createNewFile} disabled={!newFileName.trim() || isSaving}>
                      {isSaving ? 'Creating...' : 'Create'}
                    </Button>
                    <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {error && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* File List */}
        {textFiles.length > 0 && (
          <div>
            <h4 className="font-medium mb-2">Available Files</h4>
            <div className="space-y-2">
              {textFiles.map((file) => {
                const isExpanded = expandedFile === file.path
                return (
                  <div
                    key={file.path}
                    className={cn(
                      "flex items-center justify-between p-2 rounded border cursor-pointer hover:bg-muted/50 transition-all",
                      selectedFile?.path === file.path && "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800",
                      loadingFileId === file.path && "bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 animate-pulse"
                    )}
                    onClick={() => {
                      console.log('🖱️ File clicked:', file.name)
                      loadFileContent(file)
                    }}
                  >
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <FileText className="w-4 h-4 text-blue-500 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        {renamingFile === file.path ? (
                          <form
                            onSubmit={(e) => {
                              e.preventDefault()
                              renameFile(file)
                            }}
                            className="flex gap-2 items-center"
                          >
                            <Input
                              value={renameValue}
                              onChange={(e) => setRenameValue(e.target.value)}
                              className="h-8 text-sm"
                              autoFocus
                              onBlur={() => cancelRename()}
                              onClick={(e) => e.stopPropagation()}
                            />
                          </form>
                        ) : (
                          <>
                            <p 
                              className={cn(
                                "text-sm font-medium cursor-pointer",
                                !isExpanded && "truncate",
                                isExpanded && "break-all"
                              )}
                              onClick={(e) => {
                                e.stopPropagation()
                                setExpandedFile(isExpanded ? null : file.path)
                              }}
                              title="Click to expand/collapse filename"
                            >
                              {file.name}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {new Date(file.lastModified).toLocaleDateString()} • {file.size} bytes
                            </p>
                          </>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-1 flex-shrink-0 ml-2">
                      {renamingFile !== file.path && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              loadFileContent(file)
                            }}
                            disabled={loadingFileId === file.path}
                            title="View file"
                          >
                            {loadingFileId === file.path ? (
                              <RefreshCw className="w-4 h-4 animate-spin" />
                            ) : (
                              <Eye className="w-4 h-4" />
                            )}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              startRename(file)
                            }}
                            title="Rename file"
                          >
                            <PencilLine className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              deleteFile(file)
                            }}
                            title="Delete file"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {/* File Content Editor */}
        {selectedFile && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">{selectedFile.name}</h4>
                <p className="text-sm text-muted-foreground">{selectedFile.path}</p>
              </div>
              <div className="flex gap-2">
                {!isEditing ? (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleStartEditing}
                  >
                    <Edit3 className="w-4 h-4 mr-1" />
                    Edit
                  </Button>
                ) : (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCancelEditing}
                    >
                      Cancel
                    </Button>
                    <Button
                      size="sm"
                      onClick={saveFileContent}
                      disabled={isSaving}
                    >
                      <Save className="w-4 h-4 mr-1" />
                      {isSaving ? 'Saving...' : 'Save'}
                    </Button>
                  </>
                )}
              </div>
            </div>

            <div className="border rounded">
              <Textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder="File content..."
                className="min-h-[300px] resize-none border-none focus:ring-0"
                disabled={!isEditing}
              />
            </div>
          </div>
        )}

        {/* Empty State */}
        {textFiles.length === 0 && !isLoading && (
          <div className="text-center py-8 text-muted-foreground">
            <FileText className="h-8 w-8 mx-auto mb-2" />
            <p>No text files found in this folder</p>
            <p className="text-sm mt-1">Create a new file to get started</p>
          </div>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="text-center py-8">
            <RefreshCw className="h-8 w-8 mx-auto mb-2 animate-spin" />
            <p className="text-sm text-muted-foreground">Loading...</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}