# Test Script for Enhanced PowerShell Launcher
# Tests the intelligent port conflict detection and management features

param([switch]$Verbose)

# Set console preferences
$Host.UI.RawUI.WindowTitle = "Enhanced Launcher Test"
$Host.UI.RawUI.BackgroundColor = "Black"
$Host.UI.RawUI.ForegroundColor = "Green"
Clear-Host

Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host " ENHANCED LAUNCHER TEST SUITE" -ForegroundColor White
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host ""

# Test configuration
$script:projectRoot = $PSScriptRoot
$script:testResults = @()
$script:devFrontendPort = 3030
$script:backendPort = 3003
$script:requiredPorts = @{
    "Frontend" = $script:devFrontendPort
    "Backend" = $script:backendPort
    "Ngrok" = 4040
}

function Write-TestResult {
    param(
        [string]$TestName,
        [bool]$Passed,
        [string]$Details = ""
    )
    
    $result = @{
        TestName = $TestName
        Passed = $Passed
        Details = $Details
        Timestamp = Get-Date
    }
    
    $script:testResults += $result
    
    $status = if ($Passed) { "PASS" } else { "FAIL" }
    $color = if ($Passed) { "Green" } else { "Red" }
    
    Write-Host "  [$status] $TestName" -ForegroundColor $color
    if ($Details -and $Verbose) {
        Write-Host "        $Details" -ForegroundColor Gray
    }
}

function Test-PortAvailability {
    param([int]$Port)
    
    try {
        $connection = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
        return $connection -eq $null
    } catch {
        # If Get-NetTCPConnection fails, try netstat as fallback
        $netstat = netstat -ano | Select-String ":$Port\s"
        return $netstat -eq $null
    }
}

function Get-ProcessUsingPort {
    param([int]$Port)
    
    try {
        # Method 1: Use Get-NetTCPConnection (faster and more reliable)
        $connection = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
        if ($connection) {
            $processId = $connection.OwningProcess
            $process = Get-Process -Id $processId -ErrorAction SilentlyContinue
            if ($process) {
                return @{
                    ProcessId = $processId
                    ProcessName = $process.Name
                    Process = $process
                }
            }
        }
    } catch {
        # Method 2: Fallback to netstat
        try {
            $netstat = netstat -ano | Select-String ":$Port\s"
            if ($netstat) {
                $processId = ($netstat -split '\s+')[-1]
                if ($processId -match '^\d+$') {
                    $process = Get-Process -Id $processId -ErrorAction SilentlyContinue
                    if ($process) {
                        return @{
                            ProcessId = $processId
                            ProcessName = $process.Name
                            Process = $process
                        }
                    }
                }
            }
        } catch {
            # Silent fail for test
        }
    }
    
    return $null
}

# Test 1: Port Availability Detection
Write-Host "🔍 TEST 1: PORT AVAILABILITY DETECTION" -ForegroundColor Cyan
foreach ($portName in $script:requiredPorts.Keys) {
    $port = $script:requiredPorts[$portName]
    $isAvailable = Test-PortAvailability -Port $port
    
    Write-TestResult -TestName "Port $portName ($port) availability check" -Passed $true -Details "Available: $isAvailable"
}

# Test 2: Process Detection on Ports
Write-Host ""
Write-Host "🔍 TEST 2: PROCESS DETECTION ON PORTS" -ForegroundColor Cyan
foreach ($portName in $script:requiredPorts.Keys) {
    $port = $script:requiredPorts[$portName]
    $processInfo = Get-ProcessUsingPort -Port $port
    
    if ($processInfo) {
        Write-TestResult -TestName "Process detection on port $port" -Passed $true -Details "Found: $($processInfo.ProcessName) (PID: $($processInfo.ProcessId))"
    } else {
        Write-TestResult -TestName "Process detection on port $port" -Passed $true -Details "No process found (port available)"
    }
}

# Test 3: Configuration File Validation
Write-Host ""
Write-Host "🔍 TEST 3: CONFIGURATION VALIDATION" -ForegroundColor Cyan

# Check if launcher.ps1 exists
$launcherPath = "$script:projectRoot\launcher.ps1"
$launcherExists = Test-Path $launcherPath
Write-TestResult -TestName "Launcher script exists" -Passed $launcherExists -Details $launcherPath

if ($launcherExists) {
    # Check if enhanced functions are present
    $launcherContent = Get-Content $launcherPath -Raw
    
    $hasPortConflictResolution = $launcherContent -match "function Resolve-PortConflicts"
    Write-TestResult -TestName "Port conflict resolution function exists" -Passed $hasPortConflictResolution
    
    $hasEnhancedStartup = $launcherContent -match "Enhanced Development Server Startup"
    Write-TestResult -TestName "Enhanced startup function exists" -Passed $hasEnhancedStartup
    
    $hasCorrectBackendPort = $launcherContent -match '\$script:backendPort = 3003'
    Write-TestResult -TestName "Backend port correctly set to 3003" -Passed $hasCorrectBackendPort
    
    $hasRequiredPortsConfig = $launcherContent -match '\$script:requiredPorts'
    Write-TestResult -TestName "Required ports configuration exists" -Passed $hasRequiredPortsConfig
}

# Test 4: Environment Configuration
Write-Host ""
Write-Host "🔍 TEST 4: ENVIRONMENT CONFIGURATION" -ForegroundColor Cyan

# Check for .env.local file
$envLocalPath = "$script:projectRoot\.env.local"
$envLocalExists = Test-Path $envLocalPath
Write-TestResult -TestName ".env.local file exists" -Passed $envLocalExists -Details $envLocalPath

if ($envLocalExists) {
    $envContent = Get-Content $envLocalPath -Raw
    $hasBackendUrl = $envContent -match "BACKEND_URL=http://localhost:3003"
    Write-TestResult -TestName ".env.local has correct BACKEND_URL" -Passed $hasBackendUrl
    
    $hasBackendPort = $envContent -match "BACKEND_PORT=3003"
    Write-TestResult -TestName ".env.local has correct BACKEND_PORT" -Passed $hasBackendPort
}

# Test 5: Package.json Scripts
Write-Host ""
Write-Host "🔍 TEST 5: PACKAGE.JSON SCRIPTS" -ForegroundColor Cyan

$packageJsonPath = "$script:projectRoot\package.json"
$packageJsonExists = Test-Path $packageJsonPath
Write-TestResult -TestName "package.json exists" -Passed $packageJsonExists -Details $packageJsonPath

if ($packageJsonExists) {
    try {
        $packageJson = Get-Content $packageJsonPath -Raw | ConvertFrom-Json
        
        $hasDevFrontend = $packageJson.scripts."dev:frontend" -ne $null
        Write-TestResult -TestName "dev:frontend script exists" -Passed $hasDevFrontend
        
        $hasBackendScript = $packageJson.scripts | Get-Member -Name "*backend*" -MemberType NoteProperty
        Write-TestResult -TestName "Backend script exists" -Passed ($hasBackendScript -ne $null)
        
    } catch {
        Write-TestResult -TestName "package.json parsing" -Passed $false -Details $_.Exception.Message
    }
}

# Test Summary
Write-Host ""
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host " TEST SUMMARY" -ForegroundColor White
Write-Host "===============================================================================" -ForegroundColor Cyan

$totalTests = $script:testResults.Count
$passedTests = ($script:testResults | Where-Object { $_.Passed }).Count
$failedTests = $totalTests - $passedTests

Write-Host " Total Tests: $totalTests" -ForegroundColor White
Write-Host " Passed: " -NoNewline -ForegroundColor White
Write-Host $passedTests -ForegroundColor Green
Write-Host " Failed: " -NoNewline -ForegroundColor White
Write-Host $failedTests -ForegroundColor Red

if ($failedTests -eq 0) {
    Write-Host ""
    Write-Host "🎉 ALL TESTS PASSED!" -ForegroundColor Green
    Write-Host "The enhanced launcher is ready for use." -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "❌ SOME TESTS FAILED" -ForegroundColor Red
    Write-Host "Please review the failed tests above." -ForegroundColor Yellow
    
    if ($Verbose) {
        Write-Host ""
        Write-Host "Failed Tests Details:" -ForegroundColor Yellow
        $script:testResults | Where-Object { -not $_.Passed } | ForEach-Object {
            Write-Host "  • $($_.TestName): $($_.Details)" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host ""

# Pause for user to read results
Read-Host "Press Enter to exit"
