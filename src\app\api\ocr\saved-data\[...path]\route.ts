import { NextRequest, NextResponse } from 'next/server'
import { getAppConfig } from '../../../../../config/app-config'

export async function GET(request: NextRequest, { params }: { params: { path: string[] } }) {
  try {
    const config = getAppConfig()
    const BACKEND_URL = config.backendUrl
    
    // Join the path segments to get the full base64 encoded path
    const base64Path = params.path.join('/')
    
    if (!base64Path) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Base64 encoded path is required'
        }, 
        { status: 400 }
      )
    }

    console.log('🔄 OCR Saved Data API Route: Received request for base64 path:', base64Path.substring(0, 50) + '...')
    console.log('🎯 OCR Saved Data API Route: Forwarding to backend', `${BACKEND_URL}/api/ocr/saved-data/${base64Path}`)

    // Forward the request to the backend
    const response = await fetch(`${BACKEND_URL}/api/ocr/saved-data/${base64Path}`, {
      method: 'GET',
      headers: {
        'User-Agent': request.headers.get('User-Agent') || '',
      }
    })

    // Get response data
    const data = await response.text()
    
    console.log('✅ OCR Saved Data API Route: Response status:', response.status)
    
    // Return response with same status and headers
    return new NextResponse(data, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        'Content-Type': 'application/json',
      },
    })
    
  } catch (error) {
    console.error('API Proxy Error (ocr/saved-data):', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Proxy error: Failed to forward saved data request to backend',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest, { params }: { params: { path: string[] } }) {
  try {
    const config = getAppConfig()
    const BACKEND_URL = config.backendUrl
    
    // Join the path segments to get the full base64 encoded path
    const base64Path = params.path.join('/')
    
    if (!base64Path) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Base64 encoded path is required'
        }, 
        { status: 400 }
      )
    }

    // Get the request body
    const body = await request.text()
    
    console.log('🔄 OCR Saved Data PUT API Route: Received request for base64 path:', base64Path.substring(0, 50) + '...')
    console.log('🎯 OCR Saved Data PUT API Route: Forwarding to backend', `${BACKEND_URL}/api/ocr/saved-data/${base64Path}`)
    
    // Forward the request to the backend
    const response = await fetch(`${BACKEND_URL}/api/ocr/saved-data/${base64Path}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': request.headers.get('User-Agent') || '',
      },
      body: body,
    })

    // Get response data
    const data = await response.text()
    
    console.log('✅ OCR Saved Data PUT API Route: Response status:', response.status)
    
    // Return response with same status and headers
    return new NextResponse(data, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        'Content-Type': 'application/json',
      },
    })
    
  } catch (error) {
    console.error('API Proxy Error (ocr/saved-data PUT):', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Proxy error: Failed to forward saved data PUT request to backend',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    )
  }
}
