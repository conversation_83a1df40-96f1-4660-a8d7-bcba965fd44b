"use client"

import { useState, useRef, useEffect } from 'react'
import { RotateCw, RotateCcw, FlipVertical2, Image as ImageIcon, AlertCircle, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import Image from 'next/image'
import { cn } from '@/lib/utils'
import { ImageFile } from '@/types'

interface ImageGridProps {
  images: ImageFile[]
  onImageSelect: (image: ImageFile) => void
  onImageRotate: (imageId: string, degrees: number) => void
  selectedImageId?: string
  gridSize?: number
  className?: string
}

interface HoverPreviewProps {
  image: ImageFile
  anchorElement: HTMLElement
  onClose: () => void
}

function HoverPreview({ image, anchorElement, onClose }: HoverPreviewProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const previewRef = useRef<HTMLDivElement>(null)
  
  // State for dynamic sizing based on loaded image
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 })

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (previewRef.current && !previewRef.current.contains(e.target as Node)) {
        onClose()
      }
    }

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    document.addEventListener('keydown', handleEscape)

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [onClose])

  useEffect(() => {
    const positionPreview = () => {
      if (!previewRef.current || !anchorElement) return

      const rect = anchorElement.getBoundingClientRect()
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft
      const windowWidth = window.innerWidth
      const windowHeight = window.innerHeight

      // Use dynamic sizing with reasonable constraints
      const maxWidth = Math.min(window.innerWidth * 0.8, 800)
      const maxHeight = Math.min(window.innerHeight * 0.8, 600)
      
      // Use actual image size or fallback to max constraints
      const previewWidth = imageSize.width > 0 ? Math.min(imageSize.width, maxWidth) : maxWidth
      const previewHeight = imageSize.height > 0 ? Math.min(imageSize.height, maxHeight) : maxHeight

      let left, top

      // Try to position to the right first
      if (rect.right + previewWidth + 20 < windowWidth) {
        left = rect.right + scrollLeft + 15
      } 
      // If not enough space on right, try left
      else if (rect.left - previewWidth - 20 > 0) {
        left = rect.left + scrollLeft - previewWidth - 15
      }
      // If neither side works, center horizontally
      else {
        left = Math.max(10, (windowWidth - previewWidth) / 2 + scrollLeft)
      }

      // Vertical positioning - try to center on the card
      top = rect.top + scrollTop - (previewHeight - rect.height) / 2

      // Keep preview within viewport
      if (top < scrollTop + 10) {
        top = scrollTop + 10
      } else if (top + previewHeight > scrollTop + windowHeight - 10) {
        top = scrollTop + windowHeight - previewHeight - 10
      }

      previewRef.current.style.position = 'absolute'
      previewRef.current.style.left = left + 'px'
      previewRef.current.style.top = top + 'px'
      previewRef.current.style.zIndex = '10000'
    }

    positionPreview()
    window.addEventListener('resize', positionPreview)
    window.addEventListener('scroll', positionPreview)

    return () => {
      window.removeEventListener('resize', positionPreview)
      window.removeEventListener('scroll', positionPreview)
    }
  }, [anchorElement, imageSize.width, imageSize.height])

  return (
    <div
      ref={previewRef}
      className="fixed bg-card text-card-foreground border-2 border-border rounded-lg shadow-2xl p-3 max-w-screen-lg max-h-screen-lg overflow-hidden transition-opacity duration-200"
      style={{ 
        opacity: 1
      }}
    >
      <div className="text-sm font-medium text-foreground mb-2 truncate">
        {image.filename}
      </div>
      <div className="relative bg-card rounded-md p-2">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-muted rounded">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        )}
        {hasError && (
          <div className="absolute inset-0 flex items-center justify-center bg-muted rounded text-muted-foreground">
            Failed to load
          </div>
        )}
        <Image
          src={`${image.previewUrl}${image.previewUrl.includes('?') ? '&' : '?'}t=${Date.now()}&r=${Math.random()}`}
          alt={image.filename}
          width={0}
          height={0}
          sizes="100vw"
          className={cn(
            "object-contain rounded w-auto h-auto max-w-full max-h-[80vh]",
            isLoading && "opacity-0"
          )}
          style={{
            transform: 'scale(2.0)',
            transformOrigin: 'center',
            maxWidth: '80vw',
            maxHeight: '80vh'
          }}
          onLoad={(e) => {
            const img = e.target as HTMLImageElement
            setImageSize({
              width: img.naturalWidth * 2.0,
              height: img.naturalHeight * 2.0
            })
            setIsLoading(false)
          }}
          onError={() => {
            setIsLoading(false)
            setHasError(true)
          }}
        />
      </div>
    </div>
  )
}

interface ImageCardProps {
  image: ImageFile
  isSelected: boolean
  onSelect: (image: ImageFile) => void
  onRotate: (imageId: string, degrees: number) => void
  gridSize: number
}

function ImageCard({ image, isSelected, onSelect, onRotate, gridSize }: ImageCardProps) {
  const [showPreview, setShowPreview] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [hasError, setHasError] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const cardRef = useRef<HTMLDivElement>(null)
  const hoverTimeoutRef = useRef<NodeJS.Timeout>()

  const handleMouseEnter = () => {
    // Don't show preview if currently processing
    if (isProcessing) return
    
    hoverTimeoutRef.current = setTimeout(() => {
      // Double-check processing state before showing preview
      if (!isProcessing) {
        setShowPreview(true)
      }
    }, 2000) // 2 second delay
  }

  const handleMouseLeave = () => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current)
    }
    setShowPreview(false)
  }

  const handleRotate = async (degrees: number) => {
    if (isProcessing) return
    
    setIsProcessing(true)
    try {
      await onRotate(image.id, degrees)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleImageClick = () => {
    onSelect(image)
  }

  return (
    <>
      <Card
        ref={cardRef}
        className={cn(
          "cursor-pointer transition-all duration-300 hover:shadow-lg",
          isSelected && "ring-2 ring-purple-500 shadow-xl shadow-purple-500/60 dark:shadow-purple-400/50 gray:shadow-purple-400/50 ring-offset-1 ring-offset-background",
          isProcessing && "opacity-50"
        )}
        style={{ width: gridSize, height: gridSize + 60 }}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <CardContent className="p-2">
          <div 
            className="relative mb-2 bg-muted rounded overflow-hidden group"
            style={{ height: gridSize - 20 }}
            onClick={handleImageClick}
          >
            {!hasError ? (
              <Image
                src={image.thumbnailUrl}
                alt={image.filename}
                width={gridSize - 20}
                height={gridSize - 20}
                className={cn(
                  "w-full h-full object-cover transition-opacity duration-200",
                  isLoading && "opacity-0"
                )}
                loading="lazy"
                onLoad={() => setIsLoading(false)}
                onError={() => {
                  setIsLoading(false)
                  setHasError(true)
                }}
              />
            ) : (
              <div className="w-full h-full flex flex-col items-center justify-center text-muted-foreground">
                <ImageIcon className="w-8 h-8 mb-1" />
                <span className="text-xs">No Preview</span>
              </div>
            )}
            
            {isLoading && !hasError && (
              <div className="absolute inset-0 flex items-center justify-center bg-muted">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              </div>
            )}
            
            {isProcessing && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/50">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
              </div>
            )}
            
            {!hasError && !isLoading && (
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200 flex items-center justify-center">
                <RotateCw className="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
              </div>
            )}
          </div>
          
          <div className="text-xs text-muted-foreground truncate mb-1" title={image.filename}>
            {image.filename}
          </div>
          
          <div className="flex justify-between gap-1">
            <Button
              variant="info-ghost"
              size="sm"
              className="flex-1 p-1 h-8 text-xs"
              onClick={() => handleRotate(-90)}
              disabled={isProcessing}
              title="Rotate 90° Counter-Clockwise"
            >
              <RotateCcw className="w-5 h-5" />
            </Button>
            <Button
              variant="premium-ghost"
              size="sm"
              className="flex-1 p-1 h-8 text-xs"
              onClick={() => handleRotate(180)}
              disabled={isProcessing}
              title="Flip 180°"
            >
              <FlipVertical2 className="w-5 h-5" />
            </Button>
            <Button
              variant="success-ghost"
              size="sm"
              className="flex-1 p-1 h-8 text-xs"
              onClick={() => handleRotate(90)}
              disabled={isProcessing}
              title="Rotate 90° Clockwise"
            >
              <RotateCw className="w-5 h-5" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {showPreview && cardRef.current && (
        <HoverPreview
          image={image}
          anchorElement={cardRef.current}
          onClose={() => setShowPreview(false)}
        />
      )}
    </>
  )
}

export default function ImageGrid({ 
  images, 
  onImageSelect, 
  onImageRotate, 
  selectedImageId, 
  gridSize = 200,
  className 
}: ImageGridProps) {
  if (images.length === 0) {
    return (
      <div className={cn("flex items-center justify-center h-64 text-muted-foreground", className)}>
        No images found in this folder
      </div>
    )
  }

  return (
    <div className={cn("grid gap-6 p-2", className)} style={{ 
      gridTemplateColumns: `repeat(auto-fill, minmax(${gridSize}px, 1fr))` 
    }}>
      {images.map((image) => (
        <ImageCard
          key={image.id}
          image={image}
          isSelected={selectedImageId === image.id}
          onSelect={onImageSelect}
          onRotate={onImageRotate}
          gridSize={gridSize}
        />
      ))}
    </div>
  )
}