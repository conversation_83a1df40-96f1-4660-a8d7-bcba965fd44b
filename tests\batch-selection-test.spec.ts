import { test, expect } from '@playwright/test';

test.describe('Batch Selection Functionality', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
  });

  test('should enable batch selection mode and show checkboxes', async ({ page }) => {
    console.log('Testing batch selection mode activation...');
    
    // Navigate to project view
    const projectCards = page.locator('[data-testid="project-card"]');
    const cardCount = await projectCards.count();
    
    if (cardCount > 0) {
      await projectCards.first().locator('button:has-text("Open")').click();
      await page.waitForTimeout(3000);
    }
    
    // Look for batch selection controls
    const batchModeButton = page.locator('button:has-text("Batch Mode")');
    if (await batchModeButton.isVisible()) {
      await batchModeButton.click();
      await page.waitForTimeout(500);
      
      // Check if checkboxes appear in folder tree
      const folderCheckboxes = page.locator('input[type="checkbox"]');
      const checkboxCount = await folderCheckboxes.count();
      
      console.log(`Found ${checkboxCount} checkboxes in batch mode`);
      expect(checkboxCount).toBeGreaterThan(0);
    } else {
      console.log('Batch mode button not found - may need to be implemented');
    }
  });

  test('should allow selecting multiple folders with checkboxes', async ({ page }) => {
    console.log('Testing multiple folder selection...');
    
    // Navigate to project view
    const projectCards = page.locator('[data-testid="project-card"]');
    const cardCount = await projectCards.count();
    
    if (cardCount > 0) {
      await projectCards.first().locator('button:has-text("Open")').click();
      await page.waitForTimeout(3000);
    }
    
    // Look for folder checkboxes
    const folderCheckboxes = page.locator('input[type="checkbox"]');
    const checkboxCount = await folderCheckboxes.count();
    
    if (checkboxCount > 0) {
      // Select first few folders
      const maxSelections = Math.min(3, checkboxCount);
      for (let i = 0; i < maxSelections; i++) {
        const checkbox = folderCheckboxes.nth(i);
        await checkbox.check();
        await page.waitForTimeout(200);
      }
      
      // Check if selection count is displayed
      const selectionCounter = page.locator('text=/\\(\\d+ selected\\)/');
      if (await selectionCounter.isVisible()) {
        const counterText = await selectionCounter.textContent();
        console.log(`Selection counter: ${counterText}`);
        expect(counterText).toMatch(/\(\d+ selected\)/);
      }
      
      console.log(`Successfully selected ${maxSelections} folders`);
    } else {
      console.log('No folder checkboxes found - batch mode may not be active');
    }
  });

  test('should show Select All / Clear All functionality', async ({ page }) => {
    console.log('Testing Select All / Clear All functionality...');
    
    // Navigate to project view
    const projectCards = page.locator('[data-testid="project-card"]');
    const cardCount = await projectCards.count();
    
    if (cardCount > 0) {
      await projectCards.first().locator('button:has-text("Open")').click();
      await page.waitForTimeout(3000);
    }
    
    // Look for Select All button
    const selectAllButton = page.locator('button:has-text("Select All")');
    if (await selectAllButton.isVisible()) {
      await selectAllButton.click();
      await page.waitForTimeout(1000);
      
      // Check if button text changed to "Clear All"
      const clearAllButton = page.locator('button:has-text("Clear All")');
      await expect(clearAllButton).toBeVisible();
      
      // Check if checkboxes are selected
      const checkedBoxes = page.locator('input[type="checkbox"]:checked');
      const checkedCount = await checkedBoxes.count();
      console.log(`Found ${checkedCount} checked boxes after Select All`);
      
      // Test Clear All
      await clearAllButton.click();
      await page.waitForTimeout(500);
      
      // Check if all checkboxes are unchecked
      const uncheckedBoxes = page.locator('input[type="checkbox"]:not(:checked)');
      const uncheckedCount = await uncheckedBoxes.count();
      console.log(`Found ${uncheckedCount} unchecked boxes after Clear All`);
      
      console.log('Select All / Clear All functionality working correctly');
    } else {
      console.log('Select All button not found - may need batch mode activation');
    }
  });

  test('should display batch processing options when folders are selected', async ({ page }) => {
    console.log('Testing batch processing options...');
    
    // Navigate to project view
    const projectCards = page.locator('[data-testid="project-card"]');
    const cardCount = await projectCards.count();
    
    if (cardCount > 0) {
      await projectCards.first().locator('button:has-text("Open")').click();
      await page.waitForTimeout(3000);
    }
    
    // Select some folders
    const folderCheckboxes = page.locator('input[type="checkbox"]');
    const checkboxCount = await folderCheckboxes.count();
    
    if (checkboxCount > 0) {
      // Select first checkbox
      await folderCheckboxes.first().check();
      await page.waitForTimeout(500);
      
      // Look for batch processing options
      const batchProcessButton = page.locator('button:has-text("Process Selected")');
      const batchOCRButton = page.locator('button:has-text("OCR Selected")');
      const batchExportButton = page.locator('button:has-text("Export Selected")');
      
      if (await batchProcessButton.isVisible()) {
        console.log('Batch processing button found');
      } else if (await batchOCRButton.isVisible()) {
        console.log('Batch OCR button found');
      } else if (await batchExportButton.isVisible()) {
        console.log('Batch export button found');
      } else {
        console.log('Batch processing options not found - may need implementation');
      }
    } else {
      console.log('No folder checkboxes found for batch processing test');
    }
  });

  test('should maintain selection state during folder expansion', async ({ page }) => {
    console.log('Testing selection state during folder expansion...');
    
    // Navigate to project view
    const projectCards = page.locator('[data-testid="project-card"]');
    const cardCount = await projectCards.count();
    
    if (cardCount > 0) {
      await projectCards.first().locator('button:has-text("Open")').click();
      await page.waitForTimeout(3000);
    }
    
    // Find a folder with children (expandable)
    const expandableFolder = page.locator('button:has(svg)').first();
    if (await expandableFolder.isVisible()) {
      // Find the checkbox for this folder
      const folderRow = expandableFolder.locator('..').locator('..');
      const folderCheckbox = folderRow.locator('input[type="checkbox"]');
      
      if (await folderCheckbox.isVisible()) {
        // Select the folder
        await folderCheckbox.check();
        await page.waitForTimeout(500);
        
        // Expand the folder
        await expandableFolder.click();
        await page.waitForTimeout(1000);
        
        // Check if selection is maintained
        await expect(folderCheckbox).toBeChecked();
        
        console.log('Selection state maintained during folder expansion');
      } else {
        console.log('Folder checkbox not found');
      }
    } else {
      console.log('No expandable folders found');
    }
  });

  test('should handle nested folder selection correctly', async ({ page }) => {
    console.log('Testing nested folder selection...');
    
    // Navigate to project view
    const projectCards = page.locator('[data-testid="project-card"]');
    const cardCount = await projectCards.count();
    
    if (cardCount > 0) {
      await projectCards.first().locator('button:has-text("Open")').click();
      await page.waitForTimeout(3000);
    }
    
    // Find and expand a folder to reveal nested structure
    const expandButtons = page.locator('button:has(svg)');
    const expandCount = await expandButtons.count();
    
    if (expandCount > 0) {
      // Expand first folder
      await expandButtons.first().click();
      await page.waitForTimeout(1000);
      
      // Look for nested checkboxes
      const nestedCheckboxes = page.locator('input[type="checkbox"]');
      const nestedCount = await nestedCheckboxes.count();
      
      console.log(`Found ${nestedCount} total checkboxes including nested ones`);
      
      if (nestedCount > 1) {
        // Select a nested folder
        const nestedCheckbox = nestedCheckboxes.nth(1);
        await nestedCheckbox.check();
        await page.waitForTimeout(500);
        
        // Verify selection
        await expect(nestedCheckbox).toBeChecked();
        
        console.log('Nested folder selection working correctly');
      }
    } else {
      console.log('No expandable folders found for nested selection test');
    }
  });
});