@echo off
echo Starting DL Organizer Development Environment (Smart Mode)...

REM Add Bun to PATH
set PATH=C:\Users\<USER>\.bun\bin;%PATH%

REM Run port manager
echo Resolving port conflicts...
bun run scripts/port-manager.js resolve
if %errorlevel% neq 0 (
    echo Port manager failed, continuing with defaults...
)

REM Update frontend port
bun run scripts/update-frontend-port.js
if %errorlevel% neq 0 (
    echo Frontend port update failed, continuing...
)

REM Start development environment
echo Starting development servers...
call start-dev.bat