import { NextRequest, NextResponse } from "next/server";
import { getAppConfig } from "../../../../../config/app-config";

export async function POST(request: NextRequest) {
  try {
    const config = getAppConfig();
    const BACKEND_URL = config.backendUrl;

    // Get the request body
    const body = await request.text();

    console.log("🔄 OCR Cache Check API Route: Received request");
    console.log(
      "🎯 OCR Cache Check API Route: Forwarding to backend",
      `${BACKEND_URL}/api/ocr/cache/check`
    );

    // Forward the request to the backend
    const response = await fetch(`${BACKEND_URL}/api/ocr/cache/check`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "User-Agent": request.headers.get("User-Agent") || "",
      },
      body: body,
    });

    // Get response data
    const data = await response.text();

    console.log(
      "✅ OCR Cache Check API Route: Response status:",
      response.status
    );

    // Return response with same status and headers
    return new NextResponse(data, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("API Proxy Error (ocr/cache/check):", error);
    return NextResponse.json(
      {
        success: false,
        error: "Proxy error: Failed to forward cache check request to backend",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
