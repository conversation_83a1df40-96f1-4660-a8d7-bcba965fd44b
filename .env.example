# DL Organizer Environment Configuration
# Copy this file to .env and configure your settings

# Application Environment
NODE_ENV=development
PORT=3001
FRONTEND_PORT=3000

# Database Configuration
DATABASE_PATH=./data/dl-organizer.db

# File Processing Settings
MAX_FILE_SIZE=50MB
THUMBNAIL_SIZE=200
PREVIEW_MAX_WIDTH=800
PREVIEW_MAX_HEIGHT=600
IMAGE_QUALITY=85

# OCR Configuration
OCR_TIMEOUT=30000
OCR_MAX_RETRIES=3
OCR_BATCH_SIZE=5

# OpenAI API Configuration (Optional - can be set in UI)
# OPENAI_API_KEY=your_openai_api_key_here

# OpenRouter API Configuration (Optional - can be set in UI)
# OPENROUTER_API_KEY=your_openrouter_api_key_here

# Security Settings
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100
OCR_RATE_LIMIT_MAX=10

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./data/logs/app.log
LOG_MAX_SIZE=10MB
LOG_MAX_FILES=5

# Cache Settings
CACHE_MAX_AGE=604800000
CLEANUP_INTERVAL=86400000

# Windows Service Settings (for production)
SERVICE_NAME=DLOrganizer
SERVICE_DISPLAY_NAME=DL Organizer Service
SERVICE_DESCRIPTION=Driver License Organizer with OCR capabilities