'use client'

import { useEffect } from 'react'

/**
 * Minimal Browser Compatibility Component
 * Basic SSR safety and hydration handling
 */

export function BrowserCompatibility() {
  useEffect(() => {
    // Basic SSR safety check
    if (typeof window === 'undefined') return

    // Simple hydration completion logging
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 Browser compatibility initialized')
    }

    // Enhanced extension conflict handling
    try {
      // Strategy 1: Handle crypto extension ethereum property conflicts
      const handleEthereumProperty = () => {
        try {
          const ethereumDescriptor = Object.getOwnPropertyDescriptor(window, 'ethereum')
          
          if (ethereumDescriptor && ethereumDescriptor.get && !ethereumDescriptor.set) {
            const currentValue = (window as any).ethereum
            
            Object.defineProperty(window, 'ethereum', {
              get: () => currentValue,
              set: (value) => {
                try {
                  Object.defineProperty(window, 'ethereum', {
                    value: value,
                    writable: true,
                    configurable: true,
                    enumerable: true
                  })
                } catch {
                  // Silently ignore set failures
                }
              },
              configurable: true,
              enumerable: true
            })
          }
        } catch {
          // Silently handle failures
        }
      }

      // Strategy 2: Suppress extension error noise in console
      const originalError = console.error
      console.error = (...args) => {
        const message = args[0]?.toString() || ''
        
        // Filter out known extension errors that don't affect functionality
        const extensionErrorPatterns = [
          'A listener indicated an asynchronous response',
          'message channel closed before a response',
          'Cannot set property ethereum',
          'Nano Defender',
          'Unstoppable Domains',
          'contentProxy.bundle.js',
          'pageProvider.js'
        ]
        
        const isExtensionError = extensionErrorPatterns.some(pattern => 
          message.includes(pattern)
        )
        
        if (!isExtensionError) {
          originalError.apply(console, args)
        } else if (process.env.NODE_ENV === 'development') {
          // Log extension errors as debug info only in development
          console.debug('🔧 Extension error filtered:', message)
        }
      }

      // Strategy 3: Handle form submission conflicts
      document.addEventListener('submit', (e) => {
        const form = e.target as HTMLFormElement
        if (form && !form.action && !e.defaultPrevented) {
          e.preventDefault()
        }
      }, true)

      // Strategy 4: Handle extension message channel errors
      window.addEventListener('error', (e) => {
        const message = e.message || ''
        const extensionErrorPatterns = [
          'message channel closed',
          'listener indicated an asynchronous response',
          'ethereum'
        ]
        
        if (extensionErrorPatterns.some(pattern => message.includes(pattern))) {
          e.preventDefault()
          return false
        }
      })

      // Strategy 5: Handle unhandled promise rejections from extensions
      window.addEventListener('unhandledrejection', (e) => {
        const reason = e.reason?.toString() || ''
        const extensionErrorPatterns = [
          'message channel closed',
          'listener indicated an asynchronous response',
          'ethereum'
        ]
        
        if (extensionErrorPatterns.some(pattern => reason.includes(pattern))) {
          e.preventDefault()
          return false
        }
      })
      
      // Run ethereum property fix immediately and with retries
      handleEthereumProperty()
      setTimeout(handleEthereumProperty, 100)
      setTimeout(handleEthereumProperty, 500)
      
    } catch (error) {
      // Silently handle any extension compatibility issues
      if (process.env.NODE_ENV === 'development') {
        console.debug('🔧 Extension compatibility handled')
      }
    }

    // Basic localStorage safety wrapper
    const safeStorage = {
      getItem: (key: string) => {
        try {
          return localStorage.getItem(key)
        } catch {
          return null
        }
      },
      setItem: (key: string, value: string) => {
        try {
          localStorage.setItem(key, value)
        } catch {
          // Fail silently
        }
      }
    }

    // Make safe storage available globally
    ;(window as any).safeStorage = safeStorage
  }, [])

  return null
}

// Export helper functions for use in other components
export const isBrowser = () => typeof window !== 'undefined'

export const safeLocalStorage = {
  getItem: (key: string): string | null => {
    if (!isBrowser()) return null
    try {
      return localStorage.getItem(key)
    } catch {
      return null
    }
  },
  setItem: (key: string, value: string): void => {
    if (!isBrowser()) return
    try {
      localStorage.setItem(key, value)
    } catch {
      // Fail silently
    }
  },
  removeItem: (key: string): void => {
    if (!isBrowser()) return
    try {
      localStorage.removeItem(key)
    } catch {
      // Fail silently
    }
  }
}