import { test, expect } from '@playwright/test';

test.describe('Folder Tree Depth and Traversal Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
  });

  test('should test folder tree traversal up to 4 levels deep', async ({ page }) => {
    console.log('Testing folder tree depth traversal...');
    
    // First, we need to create a project or open an existing one
    const projectCards = page.locator('[data-testid="project-card"]');
    const cardCount = await projectCards.count();
    
    if (cardCount > 0) {
      // Open existing project
      console.log('Opening existing project...');
      await projectCards.first().locator('button:has-text("Open")').click();
      await page.waitForTimeout(3000);
    } else {
      // Create new project for testing
      console.log('Creating new project for testing...');
      const newProjectButton = page.locator('button:has-text("New Project")');
      if (await newProjectButton.isVisible()) {
        await newProjectButton.click();
        await page.waitForTimeout(1000);
        
        // Fill project details
        const nameInput = page.locator('input[placeholder*="Project Name"]');
        if (await nameInput.isVisible()) {
          await nameInput.fill('Test Project - Folder Depth');
        }
        
        const pathInput = page.locator('input[placeholder*="folder"]');
        if (await pathInput.isVisible()) {
          await pathInput.fill('C:/Users/<USER>/Downloads/Telegram Desktop/100x us dl w selfie/100x us dl w selfie/combined_to_do');
        }
        
        const createButton = page.locator('button:has-text("Create Project")');
        if (await createButton.isVisible()) {
          await createButton.click();
          await page.waitForTimeout(5000);
        }
      }
    }
    
    // Now test folder tree depth
    const folderTree = page.locator('[data-testid="folder-tree"]');
    if (await folderTree.isVisible()) {
      console.log('Found folder tree');
    } else {
      // Look for folder tree by class or other selectors
      const folderTreeAlt = page.locator('.folder-tree, [class*="folder"], [class*="tree"]').first();
      if (await folderTreeAlt.isVisible()) {
        console.log('Found folder tree (alternative selector)');
      }
    }
    
    // Test expanding folders at different levels
    await testFolderLevel(page, 0, 'Level 0 (Root)');
    await testFolderLevel(page, 1, 'Level 1');
    await testFolderLevel(page, 2, 'Level 2');
    await testFolderLevel(page, 3, 'Level 3');
    await testFolderLevel(page, 4, 'Level 4');
    
    console.log('Folder tree depth test completed');
  });

  test('should test folder expansion and collapse functionality', async ({ page }) => {
    console.log('Testing folder expansion/collapse...');
    
    // Navigate to project view if not already there
    const projectCards = page.locator('[data-testid="project-card"]');
    const cardCount = await projectCards.count();
    
    if (cardCount > 0) {
      await projectCards.first().locator('button:has-text("Open")').click();
      await page.waitForTimeout(3000);
    }
    
    // Find expandable folders
    const expandButtons = page.locator('button[title*="expand"], button:has(svg[class*="chevron"]), [data-testid="expand-button"]');
    const buttonCount = await expandButtons.count();
    
    console.log(`Found ${buttonCount} expandable folder buttons`);
    
    for (let i = 0; i < Math.min(buttonCount, 5); i++) {
      const button = expandButtons.nth(i);
      
      if (await button.isVisible()) {
        console.log(`Testing expand button ${i + 1}`);
        
        // Click to expand
        await button.click();
        await page.waitForTimeout(500);
        
        // Verify expansion worked (look for child elements)
        const parentFolder = button.locator('..');
        const childFolders = parentFolder.locator('[data-testid="folder-item"], .folder-item, [class*="folder"]');
        const childCount = await childFolders.count();
        
        if (childCount > 0) {
          console.log(`✓ Folder expanded, showing ${childCount} children`);
          
          // Click again to collapse
          await button.click();
          await page.waitForTimeout(500);
          
          console.log('✓ Folder collapsed');
        }
      }
    }
    
    console.log('Folder expansion/collapse test completed');
  });

  test('should test folder tree performance with deep nesting', async ({ page }) => {
    console.log('Testing folder tree performance...');
    
    const startTime = Date.now();
    
    // Navigate to project
    const projectCards = page.locator('[data-testid="project-card"]');
    const cardCount = await projectCards.count();
    
    if (cardCount > 0) {
      await projectCards.first().locator('button:has-text("Open")').click();
      await page.waitForTimeout(3000);
    }
    
    // Test API endpoint for deep scanning
    const response = await page.request.post('http://localhost:3003/api/filesystem/scan', {
      data: {
        rootPath: 'C:/Users/<USER>/Downloads/Telegram Desktop/100x us dl w selfie/100x us dl w selfie/combined_to_do',
        maxDepth: 4,
        includeStats: true,
        deepImageScan: true
      }
    });
    
    if (response.ok()) {
      const scanResult = await response.json();
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`Folder scan completed in ${duration}ms`);
      console.log(`Total folders: ${scanResult.stats?.totalFolders || 0}`);
      console.log(`Total images: ${scanResult.stats?.totalImages || 0}`);
      
      // Performance should be reasonable (under 10 seconds for typical structures)
      expect(duration).toBeLessThan(10000);
      
      if (scanResult.folders && scanResult.folders.length > 0) {
        console.log('✓ Deep folder scanning working correctly');
        
        // Test nested structure
        const hasNestedFolders = checkForNestedStructure(scanResult.folders, 0, 4);
        if (hasNestedFolders) {
          console.log('✓ Found nested folder structure up to 4 levels');
        }
      }
    } else {
      console.log(`Folder scan failed with status: ${response.status()}`);
    }
    
    console.log('Folder tree performance test completed');
  });
});

async function testFolderLevel(page: any, level: number, levelName: string) {
  console.log(`Testing ${levelName}...`);
  
  // Look for folders at this nesting level
  const levelSelector = `[data-level="${level}"], [style*="padding-left: ${level * 20}px"], [style*="margin-left: ${level * 20}px"]`;
  const foldersAtLevel = page.locator(levelSelector);
  const folderCount = await foldersAtLevel.count();
  
  if (folderCount > 0) {
    console.log(`Found ${folderCount} folders at ${levelName}`);
    
    // Test first folder at this level
    const firstFolder = foldersAtLevel.first();
    if (await firstFolder.isVisible()) {
      // Check if it has an expand button
      const expandButton = firstFolder.locator('button[title*="expand"], button:has(svg[class*="chevron"])');
      if (await expandButton.isVisible()) {
        await expandButton.click();
        await page.waitForTimeout(500);
        console.log(`✓ Expanded folder at ${levelName}`);
      }
    }
  } else {
    console.log(`No folders found at ${levelName}`);
  }
}

function checkForNestedStructure(folders: any[], currentLevel: number, maxLevel: number): boolean {
  if (currentLevel >= maxLevel) return true;
  
  for (const folder of folders) {
    if (folder.children && folder.children.length > 0) {
      if (checkForNestedStructure(folder.children, currentLevel + 1, maxLevel)) {
        return true;
      }
    }
  }
  
  return currentLevel > 0; // Return true if we've gone beyond root level
}