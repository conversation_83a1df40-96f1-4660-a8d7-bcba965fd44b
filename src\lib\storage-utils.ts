// DL Organizer Storage utility - adapted from persona-manager backup system

interface StorageInfo {
  totalProjects: number;
  totalFolders: number;
  totalImages: number;
  totalSize: number;
  totalStorageUsed: number;
  lastBackup: string;
  backupCount: number;
  isAvailable: boolean;
}

interface BackupData {
  projects: unknown[];
  folders: unknown[];
  images: unknown[];
  ocrResults: unknown[];
  settings: unknown;
  metadata: {
    exportDate: string;
    version: string;
    totalProjects: number;
    totalFolders: number;
  };
}

export const DLOrganizerStorageUtils = {
  // Check if we're in browser environment
  isBrowser(): boolean {
    return typeof window !== 'undefined' && typeof localStorage !== 'undefined'
  },

  // Test if localStorage is available and working
  testLocalStorage(): boolean {
    if (!this.isBrowser()) {
      return false
    }

    try {
      const testKey = 'dl-organizer-test'
      const testValue = 'test-data'
      localStorage.setItem(testKey, testValue)
      const retrieved = localStorage.getItem(testKey)
      localStorage.removeItem(testKey)
      return retrieved === testValue
    } catch (error) {
      console.error('localStorage test failed:', error)
      return false
    }
  },

  // Get storage info for DL Organizer
  async getStorageInfo(): Promise<StorageInfo> {
    const defaultInfo: StorageInfo = {
      totalProjects: 0,
      totalFolders: 0,
      totalImages: 0,
      totalSize: 0,
      totalStorageUsed: 0,
      lastBackup: 'Never',
      backupCount: 0,
      isAvailable: false
    }

    if (typeof window === 'undefined') {
      return defaultInfo
    }

    try {
      // Get project data
      const projectsData = localStorage.getItem('dl-organizer-projects-v1')
      const projects = projectsData ? JSON.parse(projectsData) : []
      
      // Get folder data
      const foldersData = localStorage.getItem('dl-organizer-folders-v1')
      const folders = foldersData ? JSON.parse(foldersData) : []
      
      // Get image data
      const imagesData = localStorage.getItem('dl-organizer-images-v1')
      const images = imagesData ? JSON.parse(imagesData) : []
      
      // Calculate sizes
      const totalSize = new Blob([
        JSON.stringify(projects),
        JSON.stringify(folders),
        JSON.stringify(images)
      ]).size
      
      // Get backup info
      const backupCount = parseInt(localStorage.getItem('dl-organizer-backup-count') || '0', 10)
      const lastBackup = localStorage.getItem('dl-organizer-last-backup') || 'Never'
      
      const isAvailable = this.testLocalStorage()
      const totalStorageUsed = this.getStorageUsage()
      
      return {
        totalProjects: projects.length,
        totalFolders: folders.length,
        totalImages: images.length,
        totalSize,
        totalStorageUsed,
        lastBackup,
        backupCount,
        isAvailable
      }
    } catch {
      return defaultInfo
    }
  },

  // Calculate total localStorage usage for DL Organizer
  getStorageUsage(): number {
    if (!this.isBrowser()) {
      return 0
    }

    let total = 0
    try {
      for (const key in localStorage) {
        if (localStorage.hasOwnProperty(key) && key.startsWith('dl-organizer-')) {
          total += localStorage[key].length + key.length
        }
      }
    } catch (error) {
      console.warn('Could not calculate storage usage:', error)
    }
    return total
  },

  // Create backup with automatic backup limit management
  createBackup(data: BackupData): boolean {
    if (!this.isBrowser()) {
      return false
    }

    try {
      const timestamp = new Date().toISOString()
      const backupId = `dl-organizer-backup-${timestamp}`
      
      // Store backup
      localStorage.setItem(backupId, JSON.stringify({
        ...data,
        metadata: {
          ...data.metadata,
          backupDate: timestamp
        }
      }))
      
      // Update backup tracking
      const currentCount = parseInt(localStorage.getItem('dl-organizer-backup-count') || '0', 10)
      localStorage.setItem('dl-organizer-backup-count', String(currentCount + 1))
      localStorage.setItem('dl-organizer-last-backup', timestamp)
      
      // Clean up old backups (keep only last 5)
      this.cleanupOldBackups()
      
      return true
    } catch (error) {
      console.error('Failed to create backup:', error)
      return false
    }
  },

  // Clean up old backups, keeping only the 5 most recent
  cleanupOldBackups(): void {
    if (!this.isBrowser()) return

    try {
      const backups = this.listBackups()
      if (backups.length > 5) {
        // Remove oldest backups
        const toRemove = backups.slice(5)
        toRemove.forEach(backupId => {
          localStorage.removeItem(`dl-organizer-backup-${backupId}`)
        })
        
        // Update backup count
        localStorage.setItem('dl-organizer-backup-count', String(Math.min(backups.length, 5)))
      }
    } catch (error) {
      console.error('Failed to cleanup old backups:', error)
    }
  },

  // List available backups
  listBackups(): string[] {
    if (typeof window === 'undefined') return []
    
    const backups: string[] = []
    for (const key of Object.keys(localStorage)) {
      if (key.startsWith('dl-organizer-backup-')) {
        const backupId = key.replace('dl-organizer-backup-', '')
        backups.push(backupId)
      }
    }
    
    return backups.sort().reverse()
  },

  // Restore from backup
  restoreBackup(backupId: string): boolean {
    if (!this.isBrowser()) return false

    try {
      const backupData = localStorage.getItem(`dl-organizer-backup-${backupId}`)
      if (!backupData) return false

      const backup: BackupData = JSON.parse(backupData)
      
      // Restore data
      if (backup.projects) {
        localStorage.setItem('dl-organizer-projects-v1', JSON.stringify(backup.projects))
      }
      if (backup.folders) {
        localStorage.setItem('dl-organizer-folders-v1', JSON.stringify(backup.folders))
      }
      if (backup.images) {
        localStorage.setItem('dl-organizer-images-v1', JSON.stringify(backup.images))
      }
      if (backup.ocrResults) {
        localStorage.setItem('dl-organizer-ocr-results-v1', JSON.stringify(backup.ocrResults))
      }
      if (backup.settings) {
        localStorage.setItem('dl-organizer-settings-v1', JSON.stringify(backup.settings))
      }
      
      return true
    } catch (error) {
      console.error('Failed to restore backup:', error)
      return false
    }
  },

  // Export all data for backup
  exportData(): string | null {
    if (!this.isBrowser()) {
      return null
    }

    try {
      const data: BackupData = {
        projects: JSON.parse(localStorage.getItem('dl-organizer-projects-v1') || '[]'),
        folders: JSON.parse(localStorage.getItem('dl-organizer-folders-v1') || '[]'),
        images: JSON.parse(localStorage.getItem('dl-organizer-images-v1') || '[]'),
        ocrResults: JSON.parse(localStorage.getItem('dl-organizer-ocr-results-v1') || '[]'),
        settings: JSON.parse(localStorage.getItem('dl-organizer-settings-v1') || '{}'),
        metadata: {
          exportDate: new Date().toISOString(),
          version: '1.0',
          totalProjects: JSON.parse(localStorage.getItem('dl-organizer-projects-v1') || '[]').length,
          totalFolders: JSON.parse(localStorage.getItem('dl-organizer-folders-v1') || '[]').length,
        }
      }
      
      return JSON.stringify(data, null, 2)
    } catch (error) {
      console.error('Export failed:', error)
      return null
    }
  },

  // Import data from backup
  importData(jsonString: string): boolean {
    if (!this.isBrowser()) {
      return false
    }

    try {
      const data: BackupData = JSON.parse(jsonString)
      
      // Validate data structure
      if (!data.metadata || !data.metadata.version) {
        throw new Error('Invalid backup format')
      }
      
      // Create backup before import
      const currentData = this.exportData()
      if (currentData) {
        this.createBackup(JSON.parse(currentData))
      }
      
      // Import data
      if (data.projects) {
        localStorage.setItem('dl-organizer-projects-v1', JSON.stringify(data.projects))
      }
      if (data.folders) {
        localStorage.setItem('dl-organizer-folders-v1', JSON.stringify(data.folders))
      }
      if (data.images) {
        localStorage.setItem('dl-organizer-images-v1', JSON.stringify(data.images))
      }
      if (data.ocrResults) {
        localStorage.setItem('dl-organizer-ocr-results-v1', JSON.stringify(data.ocrResults))
      }
      if (data.settings) {
        localStorage.setItem('dl-organizer-settings-v1', JSON.stringify(data.settings))
      }
      
      return true
    } catch (error) {
      console.error('Import failed:', error)
      return false
    }
  },

  // Clear all DL Organizer data
  clearAllData(): boolean {
    if (!this.isBrowser()) {
      return false
    }

    try {
      // Create backup before clearing
      const currentData = this.exportData()
      if (currentData) {
        this.createBackup(JSON.parse(currentData))
      }
      
      // Clear all DL Organizer data
      const keysToRemove = []
      for (const key in localStorage) {
        if (key.startsWith('dl-organizer-')) {
          keysToRemove.push(key)
        }
      }
      
      keysToRemove.forEach(key => {
        localStorage.removeItem(key)
      })
      
      console.log('✅ All DL Organizer data cleared')
      return true
    } catch (error) {
      console.error('❌ Failed to clear data:', error)
      return false
    }
  }
}

// Make it available globally for debugging in browser console (client-side only)
if (typeof window !== 'undefined') {
  (window as typeof window & { DLOrganizerStorageUtils: typeof DLOrganizerStorageUtils }).DLOrganizerStorageUtils = DLOrganizerStorageUtils
}