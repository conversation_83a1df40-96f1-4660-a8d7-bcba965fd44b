{"compilerOptions": {"target": "es2015", "lib": ["dom", "dom.iterable", "es2015", "es2017"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/**/*"], "exclude": ["node_modules", ".next", "backend/**/*", "ReadySearch/**/*", "archives/**/*", "cleanup-backup/**/*", "export-for-batch-feature-improvement/**/*"]}