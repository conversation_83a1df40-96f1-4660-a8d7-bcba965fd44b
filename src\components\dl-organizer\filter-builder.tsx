"use client"

import { useState, useEffect } from 'react';
import { useDebouncedValue } from '@/hooks/use-debounced-value';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { X, Filter as FilterIcon, Database, Clock, Loader2 } from 'lucide-react';
import { Filter, FilterStack, renderFilterLabel } from '@/types/filters';
import { cn } from '@/lib/utils';

interface FilterBuilderProps {
  filters: FilterStack;
  onAddFilter: (filter: Filter) => void;
  onRemoveFilter: (index: number) => void;
  onClearAll: () => void;
  className?: string;
  smartAnalyzer?: {
    start: () => void;
    running: boolean;
    canStart: boolean;
    progressPercentage: number;
  };
}

export default function FilterBuilder({
  filters,
  onAddFilter,
  onRemoveFilter,
  onClearAll,
  className,
  smartAnalyzer
}: FilterBuilderProps) {
  const [sizeValue, setSizeValue] = useState('');
  const [resolutionWidth, setResolutionWidth] = useState('');
  const [resolutionHeight, setResolutionHeight] = useState('');
  const [filenameValue, setFilenameValue] = useState('');
  
  // Debounce filename input to avoid excessive filter updates while typing
  const debouncedFilenameValue = useDebouncedValue(filenameValue, 400);
  
  // Auto-add filename filter when debounced value changes (with smart deduplication)
  useEffect(() => {
    const trimmedValue = debouncedFilenameValue.trim();
    if (trimmedValue.length > 2) { // Only filter when at least 3 characters
      const existingFilenameFilter = filters.find(f => f.kind === 'filename-contains');
      if (!existingFilenameFilter || existingFilenameFilter.text !== trimmedValue) {
        // Remove existing filename filter first if it exists
        if (existingFilenameFilter) {
          const filterIndex = filters.indexOf(existingFilenameFilter);
          onRemoveFilter(filterIndex);
        }
        // Add new filter
        onAddFilter({ kind: 'filename-contains', text: trimmedValue });
      }
    } else if (trimmedValue.length === 0) {
      // Remove filename filter when input is cleared
      const existingFilenameFilter = filters.find(f => f.kind === 'filename-contains');
      if (existingFilenameFilter) {
        const filterIndex = filters.indexOf(existingFilenameFilter);
        onRemoveFilter(filterIndex);
      }
    }
  }, [debouncedFilenameValue, filters, onAddFilter, onRemoveFilter]);

  const handleAddSizeFilter = (type: 'min' | 'max') => {
    const kb = parseInt(sizeValue);
    if (isNaN(kb) || kb <= 0) return;
    
    onAddFilter({ kind: `size-${type}` as 'size-min' | 'size-max', kb });
    setSizeValue('');
  };

  const handleAddResolutionFilter = (type: 'min' | 'max' | 'exact') => {
    const w = parseInt(resolutionWidth);
    const h = parseInt(resolutionHeight);
    if (isNaN(w) || isNaN(h) || w <= 0 || h <= 0) return;
    
    onAddFilter({ 
      kind: `resolution-${type}` as 'resolution-min' | 'resolution-max' | 'resolution-exact', 
      w, 
      h 
    });
    setResolutionWidth('');
    setResolutionHeight('');
  };

  const handleAddFilenameFilter = () => {
    const text = filenameValue.trim();
    if (!text) return;
    
    onAddFilter({ kind: 'filename-contains', text });
    setFilenameValue('');
  };

  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>, 
    action: () => void
  ) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      action();
    }
  };

  return (
    <div className={cn("space-y-1", className)}>
      {/* Responsive Filter Controls - Two rows to prevent overlap */}
      <div className="p-1.5 bg-muted/30 rounded-md space-y-2">
        {/* First Row: Main filter controls */}
        <div className="flex items-center gap-2 w-full">
          {/* Filter Icon and Label */}
          <div className="flex items-center gap-1 flex-shrink-0">
            <FilterIcon className="h-3 w-3 text-muted-foreground" />
            <span className="text-xs font-medium text-muted-foreground">Filters:</span>
          </div>
          
          {/* Quick Status Filters */}
          <div className="flex items-center gap-1.5">
            <Button 
              size="sm" 
              variant="outline"
              onClick={() => onAddFilter({ kind: 'cached', value: false })}
              className="h-6 px-2 text-xs"
            >
              <Clock className="h-3 w-3 mr-1" />
              Unprocessed
            </Button>
            <Button 
              size="sm" 
              variant="outline"
              onClick={() => onAddFilter({ kind: 'cached', value: true })}
              className="h-6 px-2 text-xs"
            >
              <Database className="h-3 w-3 mr-1" />
              Processed
            </Button>
          </div>
          
          <div className="h-4 w-px bg-border flex-shrink-0" />
          
          {/* Size Filter - Fixed truncation */}
          <div className="flex items-center gap-1">
            <span className="text-xs text-muted-foreground flex-shrink-0">Size KB:</span>
            <Input
              type="number"
              placeholder="KB"
              value={sizeValue}
              onChange={(e) => setSizeValue(e.target.value)}
              onKeyDown={(e) => handleKeyDown(e, () => handleAddSizeFilter('min'))}
              className="h-6 w-16 text-xs"
              min="1"
            />
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleAddSizeFilter('min')}
              disabled={!sizeValue || parseInt(sizeValue) <= 0}
              className="h-6 w-6 p-0 text-xs"
              title="Minimum size"
            >
              ≥
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleAddSizeFilter('max')}
              disabled={!sizeValue || parseInt(sizeValue) <= 0}
              className="h-6 w-6 p-0 text-xs"
              title="Maximum size"
            >
              ≤
            </Button>
          </div>
          
          <div className="h-4 w-px bg-border flex-shrink-0" />
          
          {/* Resolution Filter - Updated label */}
          <div className="flex items-center gap-1">
            <span className="text-xs text-muted-foreground flex-shrink-0">Resolution:</span>
            <Input
              type="number"
              placeholder="W"
              value={resolutionWidth}
              onChange={(e) => setResolutionWidth(e.target.value)}
              className="h-6 w-16 text-xs"
              min="1"
            />
            <span className="text-xs text-muted-foreground">×</span>
            <Input
              type="number"
              placeholder="H"
              value={resolutionHeight}
              onChange={(e) => setResolutionHeight(e.target.value)}
              className="h-6 w-16 text-xs"
              min="1"
            />
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleAddResolutionFilter('exact')}
              disabled={!resolutionWidth || !resolutionHeight}
              className="h-6 w-6 p-0 text-xs"
              title="Exact resolution"
            >
              =
            </Button>
          </div>
        </div>

        {/* Second Row: Keywords and Action buttons */}
        <div className="flex items-center gap-2 w-full">
          {/* Keywords Filter - Full width on second row */}
          <div className="flex items-center gap-1 flex-1">
            <span className="text-xs text-muted-foreground flex-shrink-0">Keywords:</span>
            <Input
              type="text"
              placeholder="contains..."
              value={filenameValue}
              onChange={(e) => setFilenameValue(e.target.value)}
              className="h-6 flex-1 text-xs"
              title="Auto-filters after 3+ characters"
            />
          </div>
          
          {/* Action buttons group - Right side of second row */}
          <div className="flex items-center gap-2 flex-shrink-0">
            {/* Clear All Button */}
            {filters.length > 0 && (
              <Button
                size="sm"
                variant="ghost"
                onClick={onClearAll}
                className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground flex-shrink-0 whitespace-nowrap"
              >
                Clear All
              </Button>
            )}
            
            {/* Analyze Button */}
            {smartAnalyzer && (
              <Button
                size="sm"
                variant="default"
                onClick={() => {
                  // Safe call without passing React refs
                  smartAnalyzer.start();
                }}
                disabled={smartAnalyzer.running || !smartAnalyzer.canStart}
                className="h-6 px-3 text-xs bg-blue-600 hover:bg-blue-700 text-white disabled:bg-blue-400 flex-shrink-0 whitespace-nowrap"
                title="Analyze folder contents"
              >
                {smartAnalyzer.running ? (
                  <>
                    <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                    {Math.round(smartAnalyzer.progressPercentage)}%
                  </>
                ) : (
                  <>
                    <Database className="h-3 w-3 mr-1" />
                    Analyze
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Active Filters Display */}
      {filters.length > 0 && (
        <div className="flex flex-wrap items-center gap-1 px-1">
          <span className="text-xs text-muted-foreground">Active:</span>
          {filters.map((filter, index) => (
            <Badge
              key={index}
              variant="secondary"
              className="h-5 px-1.5 py-0 flex items-center gap-1 text-xs"
            >
              <span className="truncate max-w-[120px]" title={renderFilterLabel(filter)}>
                {renderFilterLabel(filter)}
              </span>
              <button
                onClick={() => onRemoveFilter(index)}
                className="h-3 w-3 hover:bg-destructive/20 rounded-full flex items-center justify-center ml-1"
                title="Remove filter"
              >
                <X className="h-2 w-2" />
              </button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
}