const express = require('express');
const fs = require('fs').promises;
const path = require('path');
const logger = require('../utils/logger');
const SecurityMiddleware = require('../middleware/security');
const ErrorHandler = require('../middleware/error-handler');
const FileManager = require('../services/file-manager');

const router = express.Router();

/**
 * Save OCR results to TXT format with ReadySearch integration
 */
router.post('/save-txt', SecurityMiddleware.validateRequestBody(['imageId']), ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { imageId } = req.body;
    
    // Decode image path from base64 ID
    const imagePath = Buffer.from(imageId, 'base64').toString();
    const validatedPath = SecurityMiddleware.validatePath(imagePath);
    
    // Get existing OCR results JSON file
    const jsonPath = FileManager.generateResultFilename(validatedPath, 'json', 'ocr_results');
    
    try {
      const jsonData = await fs.readFile(jsonPath, 'utf8');
      const ocrData = JSON.parse(jsonData);
      
      // Save TXT file using FileManager with integrated ReadySearch results
      const txtResult = await FileManager.saveTXTResult(validatedPath, ocrData);
      
      res.json({
        success: true,
        message: 'TXT file saved successfully',
        file: txtResult,
        hasReadySearch: !!(ocrData.readySearchResults),
        readySearchMatches: ocrData.readySearchResults?.matches?.length || 0
      });
      
    } catch (error) {
      if (error.code === 'ENOENT') {
        res.status(404).json({
          success: false,
          error: 'OCR results not found. Please run OCR analysis first.'
        });
      } else {
        throw error;
      }
    }
    
  } catch (error) {
    logger.error('Error saving TXT file', { error: error.message });
    res.status(500).json({
      success: false,
      error: 'Failed to save TXT file',
      details: error.message
    });
  }
}));

/**
 * Batch save TXT files for multiple images
 */
router.post('/batch-save-txt', SecurityMiddleware.validateRequestBody(['imageIds']), ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { imageIds } = req.body;
    
    if (!Array.isArray(imageIds) || imageIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'imageIds must be a non-empty array'
      });
    }
    
    const results = [];
    let successCount = 0;
    let errorCount = 0;
    
    for (const imageId of imageIds) {
      try {
        const imagePath = Buffer.from(imageId, 'base64').toString();
        const validatedPath = SecurityMiddleware.validatePath(imagePath);
        
        // Get OCR results
        const jsonPath = FileManager.generateResultFilename(validatedPath, 'json', 'ocr_results');
        const jsonData = await fs.readFile(jsonPath, 'utf8');
        const ocrData = JSON.parse(jsonData);
        
        // Save TXT file
        const txtResult = await FileManager.saveTXTResult(validatedPath, ocrData);
        
        results.push({
          imageId,
          imagePath: validatedPath.substring(validatedPath.length - 50),
          success: true,
          file: txtResult,
          hasReadySearch: !!(ocrData.readySearchResults),
          readySearchMatches: ocrData.readySearchResults?.matches?.length || 0
        });
        
        successCount++;
        
      } catch (error) {
        results.push({
          imageId,
          success: false,
          error: error.message
        });
        errorCount++;
      }
    }
    
    res.json({
      success: errorCount === 0,
      processedCount: imageIds.length,
      successCount,
      errorCount,
      results
    });
    
  } catch (error) {
    logger.error('Error in batch TXT saving', { error: error.message });
    res.status(500).json({
      success: false,
      error: 'Batch TXT saving failed',
      details: error.message
    });
  }
}));

/**
 * Rename single image with ReadySearch indicators
 */
router.post('/rename-with-rs-indicator', SecurityMiddleware.validateRequestBody(['imageId', 'newBaseName']), ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { imageId, newBaseName, includeReadySearchIndicator = true } = req.body;
    
    // Decode image path from base64 ID
    const originalPath = Buffer.from(imageId, 'base64').toString();
    const validatedPath = SecurityMiddleware.validatePath(originalPath);
    
    // Check if original file exists
    await fs.access(validatedPath);
    
    // Get OCR results to determine ReadySearch status
    let ocrData = null;
    try {
      const jsonPath = FileManager.generateResultFilename(validatedPath, 'json', 'ocr_results');
      const jsonContent = await fs.readFile(jsonPath, 'utf8');
      ocrData = JSON.parse(jsonContent);
    } catch (error) {
      logger.warn('No OCR results found for rename operation', { 
        imagePath: validatedPath.substring(validatedPath.length - 50)
      });
    }
    
    // Generate new filename with ReadySearch status
    const directory = path.dirname(validatedPath);
    const extension = path.extname(validatedPath);
    
    let finalBaseName = SecurityMiddleware.sanitizeText(newBaseName);
    
    // Add ReadySearch indicator if requested and data available
    if (includeReadySearchIndicator && ocrData) {
      const rsStatus = FileManager.getReadySearchStatus(ocrData);
      if (rsStatus !== 'no-rs') {
        // Remove any existing RS indicators first
        finalBaseName = finalBaseName.replace(/-(rs-m|rs-nm|no-rs)$/i, '');
        finalBaseName = `${finalBaseName}-${rsStatus}`;
      }
    }
    
    const newPath = path.join(directory, `${finalBaseName}${extension}`);
    const secureNewPath = SecurityMiddleware.validatePath(newPath);
    
    // Check if target file already exists
    try {
      await fs.access(secureNewPath);
      return res.status(409).json({
        success: false,
        error: 'Target filename already exists',
        suggestedName: `${finalBaseName}_copy${extension}`
      });
    } catch (error) {
      // File doesn't exist, good to proceed
    }
    
    // Rename the image file
    await fs.rename(validatedPath, secureNewPath);
    
    // Update associated OCR result files if they exist
    const updateResults = [];
    
    // Update JSON file
    try {
      const oldJsonPath = FileManager.generateResultFilename(validatedPath, 'json', 'ocr_results');
      const newJsonPath = FileManager.generateResultFilename(secureNewPath, 'json', 'ocr_results');
      
      await fs.access(oldJsonPath);
      await fs.rename(oldJsonPath, newJsonPath);
      
      // Update the imagePath inside the JSON file
      if (ocrData) {
        ocrData.imagePath = secureNewPath;
        ocrData.lastModified = new Date().toISOString();
        ocrData.renameHistory = ocrData.renameHistory || [];
        ocrData.renameHistory.push({
          from: validatedPath,
          to: secureNewPath,
          timestamp: new Date().toISOString(),
          includeReadySearchIndicator
        });
        
        await fs.writeFile(newJsonPath, JSON.stringify(ocrData, null, 2));
      }
      
      updateResults.push({ type: 'json', from: oldJsonPath, to: newJsonPath, success: true });
    } catch (error) {
      updateResults.push({ type: 'json', error: error.message, success: false });
    }
    
    // Update TXT file if it exists
    try {
      const oldTxtPath = FileManager.generateResultFilename(validatedPath, 'txt', 'ocr_results');
      const newTxtPath = FileManager.generateResultFilename(secureNewPath, 'txt', 'ocr_results');
      
      await fs.access(oldTxtPath);
      await fs.rename(oldTxtPath, newTxtPath);
      updateResults.push({ type: 'txt', from: oldTxtPath, to: newTxtPath, success: true });
    } catch (error) {
      updateResults.push({ type: 'txt', error: error.message, success: false });
    }
    
    logger.info('Image renamed with ReadySearch indicator', {
      from: validatedPath.substring(validatedPath.length - 50),
      to: secureNewPath.substring(secureNewPath.length - 50),
      includeReadySearchIndicator,
      readySearchStatus: ocrData ? FileManager.getReadySearchStatus(ocrData) : 'no-data'
    });
    
    res.json({
      success: true,
      message: 'Image renamed successfully',
      originalPath: validatedPath,
      newPath: secureNewPath,
      newBaseName: finalBaseName,
      readySearchStatus: ocrData ? FileManager.getReadySearchStatus(ocrData) : 'no-data',
      hasReadySearchData: !!(ocrData?.readySearchResults),
      associatedFiles: updateResults
    });
    
  } catch (error) {
    logger.error('Error renaming image with ReadySearch indicator', { error: error.message });
    res.status(500).json({
      success: false,
      error: 'Failed to rename image',
      details: error.message
    });
  }
}));

/**
 * Batch rename images with ReadySearch indicators
 */
router.post('/batch-rename-with-rs-indicator', SecurityMiddleware.validateRequestBody(['renameData']), ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { renameData, includeReadySearchIndicator = true } = req.body;
    
    if (!Array.isArray(renameData) || renameData.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'renameData must be a non-empty array'
      });
    }
    
    const results = [];
    let successCount = 0;
    let errorCount = 0;
    
    for (const item of renameData) {
      try {
        const { imageId, newBaseName } = item;
        
        if (!imageId || !newBaseName) {
          throw new Error('Missing imageId or newBaseName');
        }
        
        // Process single rename
        const originalPath = Buffer.from(imageId, 'base64').toString();
        const validatedPath = SecurityMiddleware.validatePath(originalPath);
        
        // Check if original file exists
        await fs.access(validatedPath);
        
        // Get OCR results
        let ocrData = null;
        try {
          const jsonPath = FileManager.generateResultFilename(validatedPath, 'json', 'ocr_results');
          const jsonContent = await fs.readFile(jsonPath, 'utf8');
          ocrData = JSON.parse(jsonContent);
        } catch (error) {
          // No OCR data available
        }
        
        // Generate new filename
        const directory = path.dirname(validatedPath);
        const extension = path.extname(validatedPath);
        
        let finalBaseName = SecurityMiddleware.sanitizeText(newBaseName);
        
        if (includeReadySearchIndicator && ocrData) {
          const rsStatus = FileManager.getReadySearchStatus(ocrData);
          if (rsStatus !== 'no-rs') {
            finalBaseName = finalBaseName.replace(/-(rs-m|rs-nm|no-rs)$/i, '');
            finalBaseName = `${finalBaseName}-${rsStatus}`;
          }
        }
        
        const newPath = path.join(directory, `${finalBaseName}${extension}`);
        const secureNewPath = SecurityMiddleware.validatePath(newPath);
        
        // Check if target exists
        try {
          await fs.access(secureNewPath);
          throw new Error(`Target filename already exists: ${finalBaseName}${extension}`);
        } catch (error) {
          if (error.code !== 'ENOENT') throw error;
        }
        
        // Perform rename
        await fs.rename(validatedPath, secureNewPath);
        
        // Update associated files
        const fileUpdates = [];
        
        // JSON file
        try {
          const oldJsonPath = FileManager.generateResultFilename(validatedPath, 'json', 'ocr_results');
          const newJsonPath = FileManager.generateResultFilename(secureNewPath, 'json', 'ocr_results');
          await fs.access(oldJsonPath);
          await fs.rename(oldJsonPath, newJsonPath);
          
          if (ocrData) {
            ocrData.imagePath = secureNewPath;
            ocrData.lastModified = new Date().toISOString();
            await fs.writeFile(newJsonPath, JSON.stringify(ocrData, null, 2));
          }
          
          fileUpdates.push({ type: 'json', success: true });
        } catch (error) {
          fileUpdates.push({ type: 'json', success: false, error: error.message });
        }
        
        // TXT file
        try {
          const oldTxtPath = FileManager.generateResultFilename(validatedPath, 'txt', 'ocr_results');
          const newTxtPath = FileManager.generateResultFilename(secureNewPath, 'txt', 'ocr_results');
          await fs.access(oldTxtPath);
          await fs.rename(oldTxtPath, newTxtPath);
          fileUpdates.push({ type: 'txt', success: true });
        } catch (error) {
          fileUpdates.push({ type: 'txt', success: false, error: error.message });
        }
        
        results.push({
          imageId,
          originalPath: validatedPath.substring(validatedPath.length - 50),
          newPath: secureNewPath.substring(secureNewPath.length - 50),
          newBaseName: finalBaseName,
          readySearchStatus: ocrData ? FileManager.getReadySearchStatus(ocrData) : 'no-data',
          success: true,
          associatedFiles: fileUpdates
        });
        
        successCount++;
        
      } catch (error) {
        results.push({
          imageId: item.imageId,
          newBaseName: item.newBaseName,
          success: false,
          error: error.message
        });
        errorCount++;
      }
    }
    
    res.json({
      success: errorCount === 0,
      processedCount: renameData.length,
      successCount,
      errorCount,
      includeReadySearchIndicator,
      results
    });
    
  } catch (error) {
    logger.error('Error in batch rename with ReadySearch indicators', { error: error.message });
    res.status(500).json({
      success: false,
      error: 'Batch rename failed',
      details: error.message
    });
  }
}));

/**
 * Get file save options and ReadySearch status for image
 */
router.get('/save-options/:imageId', ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { imageId } = req.params;
    
    const imagePath = Buffer.from(imageId, 'base64').toString();
    const validatedPath = SecurityMiddleware.validatePath(imagePath);
    
    // Check if image exists
    await fs.access(validatedPath);
    
    // Check for OCR results
    let ocrData = null;
    let hasOCRResults = false;
    let hasReadySearch = false;
    let readySearchStatus = 'no-rs';
    
    try {
      const jsonPath = FileManager.generateResultFilename(validatedPath, 'json', 'ocr_results');
      const jsonContent = await fs.readFile(jsonPath, 'utf8');
      ocrData = JSON.parse(jsonContent);
      hasOCRResults = true;
      hasReadySearch = !!(ocrData.readySearchResults);
      readySearchStatus = FileManager.getReadySearchStatus(ocrData);
    } catch (error) {
      // No OCR results found
    }
    
    // Check for existing TXT file
    let hasTXTFile = false;
    try {
      const txtPath = FileManager.generateResultFilename(validatedPath, 'txt', 'ocr_results');
      await fs.access(txtPath);
      hasTXTFile = true;
    } catch (error) {
      // No TXT file found
    }
    
    res.json({
      success: true,
      imagePath: validatedPath.substring(validatedPath.length - 50),
      hasOCRResults,
      hasReadySearch,
      readySearchStatus,
      readySearchMatches: ocrData?.readySearchResults?.matches?.length || 0,
      hasTXTFile,
      saveOptions: {
        canSaveTXT: hasOCRResults,
        canRenameWithRS: hasOCRResults,
        suggestedFilename: hasOCRResults ? FileManager.generateFilenameWithReadySearchStatus(validatedPath, ocrData) : null
      }
    });
    
  } catch (error) {
    logger.error('Error getting save options', { error: error.message });
    res.status(500).json({
      success: false,
      error: 'Failed to get save options',
      details: error.message
    });
  }
}));

module.exports = router;