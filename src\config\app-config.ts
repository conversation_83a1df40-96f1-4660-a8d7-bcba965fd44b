/**
 * Centralized Application Configuration
 * 
 * This file provides a single source of truth for all application URLs, ports, and endpoints.
 * It dynamically loads configuration from environment variables and port-config.json,
 * ensuring consistent routing across all components.
 * 
 * Usage:
 * - Import { getAppConfig } from '@/config/app-config'
 * - Use getAppConfig().backendUrl, getAppConfig().frontendUrl, etc.
 * 
 * The configuration is automatically updated when:
 * - Environment variables change
 * - port-config.json is updated by the launcher
 * - Server ports are dynamically assigned
 */

import fs from 'fs'
import path from 'path'

export interface AppConfig {
  // Server URLs
  backendUrl: string
  frontendUrl: string
  ngrokUrl?: string
  
  // Port numbers
  backendPort: number
  frontendPort: number
  ngrokPort?: number
  
  // API endpoints
  apiBaseUrl: string
  thumbnailsBaseUrl: string
  previewsBaseUrl: string
  
  // Environment info
  environment: 'development' | 'production' | 'test'
  isProduction: boolean
  isDevelopment: boolean
  
  // Feature flags
  enableThumbnails: boolean
  enablePreviews: boolean
  enableCORS: boolean
}

interface PortConfig {
  ports?: {
    frontend?: number
    backend?: number
    ngrok?: number
  }
  environment?: {
    FRONTEND_PORT?: string
    BACKEND_PORT?: string
    BACKEND_URL?: string
    NGROK_PORT?: string
  }
  urls?: {
    frontend?: string
    backend?: string
    ngrok?: string
  }
}

let cachedConfig: AppConfig | null = null
let configLastModified: number = 0

/**
 * Load port configuration from port-config.json if it exists
 */
function loadPortConfig(): PortConfig {
  try {
    const configPath = path.join(process.cwd(), 'data', 'port-config.json')
    
    // Check if file exists and get modification time
    if (fs.existsSync(configPath)) {
      const stats = fs.statSync(configPath)
      const lastModified = stats.mtime.getTime()
      
      // Only reload if file has been modified
      if (lastModified > configLastModified) {
        configLastModified = lastModified
        const configData = fs.readFileSync(configPath, 'utf8')
        return JSON.parse(configData)
      }
    }
  } catch (error) {
    console.warn('⚠️  Could not load port configuration:', error)
  }
  
  return {}
}

/**
 * Get backend port from various sources in order of priority:
 * 1. port-config.json environment.BACKEND_PORT
 * 2. port-config.json ports.backend
 * 3. Environment variable BACKEND_PORT
 * 4. Default 3003
 */
function getBackendPort(): number {
  const portConfig = loadPortConfig()
  
  // Try port-config.json environment first
  if (portConfig.environment?.BACKEND_PORT) {
    return parseInt(portConfig.environment.BACKEND_PORT)
  }
  
  // Try port-config.json ports
  if (portConfig.ports?.backend) {
    return portConfig.ports.backend
  }
  
  // Try environment variable
  if (process.env.BACKEND_PORT) {
    return parseInt(process.env.BACKEND_PORT)
  }
  
  // Default
  return 3003
}

/**
 * Get frontend port from various sources in order of priority:
 * 1. port-config.json environment.FRONTEND_PORT
 * 2. port-config.json ports.frontend
 * 3. Environment variable FRONTEND_PORT
 * 4. Environment variable PORT
 * 5. Default 3030
 */
function getFrontendPort(): number {
  const portConfig = loadPortConfig()
  
  // Try port-config.json environment first
  if (portConfig.environment?.FRONTEND_PORT) {
    return parseInt(portConfig.environment.FRONTEND_PORT)
  }
  
  // Try port-config.json ports
  if (portConfig.ports?.frontend) {
    return portConfig.ports.frontend
  }
  
  // Try environment variables
  if (process.env.FRONTEND_PORT) {
    return parseInt(process.env.FRONTEND_PORT)
  }
  
  if (process.env.PORT) {
    return parseInt(process.env.PORT)
  }
  
  // Default
  return 3030
}

/**
 * Get backend URL from various sources in order of priority:
 * 1. port-config.json urls.backend
 * 2. port-config.json environment.BACKEND_URL
 * 3. Environment variable BACKEND_URL
 * 4. Constructed from backend port
 */
function getBackendUrl(): string {
  const portConfig = loadPortConfig()
  
  // Try port-config.json URLs first
  if (portConfig.urls?.backend) {
    return portConfig.urls.backend
  }
  
  // Try port-config.json environment
  if (portConfig.environment?.BACKEND_URL) {
    return portConfig.environment.BACKEND_URL
  }
  
  // Try environment variable
  if (process.env.BACKEND_URL) {
    return process.env.BACKEND_URL
  }
  
  // Construct from port
  const backendPort = getBackendPort()
  return `http://localhost:${backendPort}`
}

/**
 * Get the current application configuration
 * This function caches the configuration and only reloads when port-config.json changes
 */
export function getAppConfig(): AppConfig {
  // Check if we need to reload config
  const portConfig = loadPortConfig()
  
  if (!cachedConfig) {
    const backendPort = getBackendPort()
    const frontendPort = getFrontendPort()
    const backendUrl = getBackendUrl()
    const environment = (process.env.NODE_ENV as any) || 'development'
    
    cachedConfig = {
      // Server URLs
      backendUrl,
      frontendUrl: `http://localhost:${frontendPort}`,
      ngrokUrl: portConfig.urls?.ngrok,
      
      // Port numbers
      backendPort,
      frontendPort,
      ngrokPort: portConfig.ports?.ngrok,
      
      // API endpoints (relative paths that go through Next.js API routes)
      apiBaseUrl: '/api',
      thumbnailsBaseUrl: '/api/thumbnails',
      previewsBaseUrl: '/api/previews',
      
      // Environment info
      environment,
      isProduction: environment === 'production',
      isDevelopment: environment === 'development',
      
      // Feature flags
      enableThumbnails: true,
      enablePreviews: true,
      enableCORS: true,
    }
  }
  
  return cachedConfig
}

/**
 * Force reload of configuration (useful for testing or when config changes)
 */
export function reloadAppConfig(): AppConfig {
  cachedConfig = null
  configLastModified = 0
  return getAppConfig()
}

/**
 * Get a complete thumbnail URL for an image
 * @param imageId - Base64 encoded image path or thumbnail filename
 * @param cacheBust - Whether to add cache busting parameters
 */
export function getThumbnailUrl(imageId: string, cacheBust: boolean = true): string {
  const config = getAppConfig()
  const baseUrl = `${config.thumbnailsBaseUrl}/${imageId}`
  
  if (cacheBust) {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substr(2, 9)
    return `${baseUrl}?t=${timestamp}&cb=${random}`
  }
  
  return baseUrl
}

/**
 * Get a complete preview URL for an image
 * @param imageId - Base64 encoded image path or preview filename
 * @param cacheBust - Whether to add cache busting parameters
 */
export function getPreviewUrl(imageId: string, cacheBust: boolean = true): string {
  const config = getAppConfig()
  const baseUrl = `${config.previewsBaseUrl}/${imageId}`
  
  if (cacheBust) {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substr(2, 9)
    return `${baseUrl}?t=${timestamp}&cb=${random}`
  }
  
  return baseUrl
}

/**
 * Get API endpoint URL
 * @param endpoint - API endpoint path (without leading slash)
 */
export function getApiUrl(endpoint: string): string {
  const config = getAppConfig()
  return `${config.apiBaseUrl}/${endpoint.replace(/^\//, '')}`
}

/**
 * Validate and normalize a URL to ensure it uses the correct configuration
 * @param url - URL to validate
 * @param type - Type of URL (api, thumbnail, preview)
 */
export function normalizeUrl(url: string, type: 'api' | 'thumbnail' | 'preview' = 'api'): string {
  const config = getAppConfig()
  
  // Remove any hardcoded localhost:3003 references
  url = url.replace(/http:\/\/localhost:3003/g, '')
  url = url.replace(/http:\/\/127\.0\.0\.1:3003/g, '')
  
  // Ensure URL starts with correct base
  if (type === 'thumbnail' && !url.startsWith(config.thumbnailsBaseUrl)) {
    return `${config.thumbnailsBaseUrl}/${url.replace(/^\//, '')}`
  }
  
  if (type === 'preview' && !url.startsWith(config.previewsBaseUrl)) {
    return `${config.previewsBaseUrl}/${url.replace(/^\//, '')}`
  }
  
  if (type === 'api' && !url.startsWith(config.apiBaseUrl)) {
    return `${config.apiBaseUrl}/${url.replace(/^\//, '')}`
  }
  
  return url
}

export default getAppConfig
