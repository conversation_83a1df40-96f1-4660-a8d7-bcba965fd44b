const fs = require('fs');
const path = require('path');
const http = require('http');

// Helper function to make HTTP requests
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlParts = new URL(url);
    const reqOptions = {
      hostname: urlParts.hostname,
      port: urlParts.port,
      path: urlParts.pathname + urlParts.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };
    
    const req = http.request(reqOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          statusText: res.statusMessage,
          headers: res.headers,
          json: () => Promise.resolve(JSON.parse(data)),
          text: () => Promise.resolve(data)
        });
      });
    });
    
    req.on('error', reject);
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

// Test configuration
const API_BASE = 'http://localhost:3003';
const API_KEY = '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';

// Test results
const results = {
  passed: 0,
  failed: 0,
  tests: []
};

// Helper function to run a test
async function runTest(name, testFn) {
  console.log(`\n🧪 Testing: ${name}...`);
  try {
    await testFn();
    results.passed++;
    results.tests.push({ name, status: 'PASSED', error: null });
    console.log(`✅ PASSED: ${name}`);
  } catch (error) {
    results.failed++;
    results.tests.push({ name, status: 'FAILED', error: error.message });
    console.log(`❌ FAILED: ${name}`);
    console.log(`   Error: ${error.message}`);
  }
}

// Test 1: CORS for ngrok access
async function testNgrokCORS() {
  const response = await makeRequest(`${API_BASE}/api/health`, {
    headers: {
      'Origin': 'https://test.ngrok-free.app'
    }
  });
  
  const corsHeader = response.headers['access-control-allow-origin'];
  if (!corsHeader || !corsHeader.includes('ngrok')) {
    throw new Error(`CORS header not set for ngrok: ${corsHeader}`);
  }
  
  const data = await response.json();
  if (data.status !== 'healthy') {
    throw new Error('API not healthy');
  }
}

// Test 2: API key persistence and pre-population
async function testAPIKeyPersistence() {
  const response = await makeRequest(`${API_BASE}/api/settings/openrouter`);
  const data = await response.json();
  
  if (data.apiKey !== API_KEY) {
    throw new Error(`API key mismatch. Expected: ${API_KEY}, Got: ${data.apiKey}`);
  }
  
  if (!data.isEnabled) {
    throw new Error('OpenRouter is not enabled by default');
  }
}

// Test 3: No mock data in OCR
async function testNoMockData() {
  // This test verifies that mock data has been removed
  // We'll check by attempting an OCR request and ensuring it doesn't return "John Smith"
  try {
    const response = await makeRequest(`${API_BASE}/api/ocr/analyze`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        testMode: true,
        validateNoMockData: true
      })
    });
    
    // If we get a response, check it's not mock data
    if (response.status === 200) {
      const data = await response.json();
      if (data.firstName === 'John' && data.lastName === 'Smith') {
        throw new Error('Mock data "John Smith" detected in OCR response');
      }
    }
    // Any error is fine as long as it's not returning mock data
  } catch (error) {
    if (error.message && error.message.includes('John Smith')) {
      throw error;
    }
  }
}

// Test 4: Model list availability
async function testModelList() {
  const response = await makeRequest(`${API_BASE}/api/ocr/models`);
  const data = await response.json();
  
  // Check for API models
  if (!data.api || data.api.length === 0) {
    throw new Error('No API models available');
  }
  
  // Check for total count
  if (!data.total || data.total === 0) {
    throw new Error('No models available');
  }
  
  console.log(`   Found ${data.total} models (${data.api.length} API, ${data.local ? data.local.length : 0} local)`);
}

// Test 5: Image rotation endpoint
async function testImageRotation() {
  const testImageId = 'test-image-123';
  
  try {
    const response = await makeRequest(`${API_BASE}/api/images/${testImageId}/rotate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ degrees: 90 })
    });
    
    // We expect this to fail for non-existent image, but endpoint should exist
    if (response.status === 404 && response.statusText && response.statusText.includes('Cannot POST')) {
      throw new Error('Image rotation endpoint not found');
    }
  } catch (error) {
    if (error.message && error.message.includes('endpoint not found')) {
      throw error;
    }
    // Other errors are expected for test image
  }
}

// Test 6: Default configuration
async function testDefaultConfiguration() {
  const response = await makeRequest(`${API_BASE}/api/settings/openrouter`);
  const data = await response.json();
  
  // Check all expected fields
  const requiredFields = ['apiKey', 'selectedModel', 'baseUrl', 'isEnabled'];
  for (const field of requiredFields) {
    if (!(field in data)) {
      throw new Error(`Missing required field: ${field}`);
    }
  }
  
  // Check default values
  if (data.baseUrl !== 'https://openrouter.ai/api/v1') {
    throw new Error(`Incorrect base URL: ${data.baseUrl}`);
  }
}

// Test 7: Health check comprehensive
async function testHealthCheck() {
  const response = await makeRequest(`${API_BASE}/api/health`);
  const data = await response.json();
  
  if (data.status !== 'healthy') {
    throw new Error('API not healthy');
  }
  
  // Check all subsystems
  const subsystems = ['memory', 'filesystem', 'sharp', 'database'];
  for (const system of subsystems) {
    if (!data.checks[system] || data.checks[system].status !== 'healthy') {
      throw new Error(`${system} subsystem not healthy`);
    }
  }
}

// Run all tests
async function runAllTests() {
  console.log('🎯 DL Organizer Production Validation Tests');
  console.log('==========================================');
  
  await runTest('CORS for ngrok access', testNgrokCORS);
  await runTest('API key persistence', testAPIKeyPersistence);
  await runTest('No mock data in OCR', testNoMockData);
  await runTest('Model list availability', testModelList);
  await runTest('Image rotation endpoint', testImageRotation);
  await runTest('Default configuration', testDefaultConfiguration);
  await runTest('Health check comprehensive', testHealthCheck);
  
  // Print summary
  console.log('\n==========================================');
  console.log('📊 Test Summary:');
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`📝 Total: ${results.tests.length}`);
  
  if (results.failed === 0) {
    console.log('\n🎉 All tests passed! Application is production-ready.');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the errors above.');
  }
  
  // Save detailed results
  const timestamp = new Date().toISOString().replace(/:/g, '-');
  const resultFile = path.join(__dirname, `validation-results-${timestamp}.json`);
  fs.writeFileSync(resultFile, JSON.stringify(results, null, 2));
  console.log(`\n📄 Detailed results saved to: ${resultFile}`);
}

// Run the tests
runAllTests().catch(console.error);