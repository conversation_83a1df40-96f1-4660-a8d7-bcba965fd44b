# Enhanced PowerShell Launcher - Intelligent Port Management

## Overview

The PowerShell launcher (`launcher.ps1`) has been significantly enhanced with intelligent port conflict detection and management capabilities. This eliminates the manual process killing/restarting cycle and prevents route configuration issues that occur when ports change.

## Key Enhancements

### 🔍 Intelligent Port Conflict Detection

- **Automatic Detection**: Scans required ports (3030 for frontend, 3003 for backend, 4040 for ngrok) before startup
- **Process Identification**: Identifies exactly which processes are using conflicting ports
- **Smart Filtering**: Distinguishes between project-related and system processes

### 🔧 Selective Process Management

- **Targeted Termination**: Kills only conflicting processes, not all Node.js processes
- **Process Safety**: Provides detailed information about processes before termination
- **User Control**: Interactive prompts for conflict resolution with detailed process information

### ⚙️ Route Configuration Preservation

- **Consistent Ports**: Maintains fixed port assignments (3030 frontend, 3003 backend)
- **No Dynamic Assignment**: Avoids port changes that break pre-configured API routes
- **Environment Sync**: Updates `.env.local` and configuration files automatically

### 🚀 Enhanced Startup Reliability

- **Step-by-Step Process**: Clear 5-step startup process with progress indicators
- **Health Verification**: Confirms successful server startup before completion
- **Error Handling**: Comprehensive error detection and troubleshooting guidance

## New Features

### Enhanced Menu Options

- **Option [D]**: Enhanced Development Environment with smart port management
- **Option [6]**: Development Server with enhanced port management
- **Option [R]**: Force restart with intelligent conflict resolution
- **Option [18]**: Manual port conflict resolution tool

### New Functions

1. **`Test-PortAvailability`**: Checks if a specific port is available
2. **`Get-ProcessUsingPort`**: Identifies processes using specific ports
3. **`Resolve-PortConflicts`**: Interactive port conflict resolution
4. **Enhanced `Start-DevelopmentServer`**: 5-step startup process with conflict resolution
5. **Enhanced `Force-RestartDevelopmentServers`**: Force restart with port management

## Usage

### Quick Start (Recommended)

```powershell
# Run the enhanced launcher
.\launcher.ps1

# Select option [D] for enhanced development environment
# The launcher will automatically:
# 1. Detect port conflicts
# 2. Resolve conflicts (with user confirmation)
# 3. Update configuration
# 4. Start servers in correct order
# 5. Verify successful startup
```

### Manual Port Conflict Resolution

```powershell
# Run the launcher
.\launcher.ps1

# Select option [18] for manual port conflict resolution
# This will show detailed information about conflicting processes
# and allow you to resolve conflicts without starting servers
```

### Testing the Enhanced Launcher

```powershell
# Run the test suite to verify all enhancements work correctly
.\test-enhanced-launcher.ps1 -Verbose
```

## Configuration

### Required Ports

- **Frontend**: 3030 (development)
- **Backend**: 3003 (matches API route configuration)
- **Ngrok**: 4040 (tunnel management)

### Environment Variables

The launcher automatically maintains these in `.env.local`:

```env
BACKEND_URL=http://localhost:3003
BACKEND_PORT=3003
FRONTEND_PORT=3030
NODE_ENV=development
```

## Startup Process

### Enhanced Development Server Startup

1. **🔍 Port Conflict Detection**
   - Scans all required ports
   - Identifies conflicting processes
   - Provides detailed process information

2. **⚙️ Configuration Update**
   - Updates port configuration files
   - Syncs environment variables
   - Ensures route consistency

3. **📦 Dependency Check**
   - Verifies Node.js and Bun installation
   - Checks package dependencies
   - Updates if necessary

4. **🚀 Server Startup**
   - Starts backend first (port 3003)
   - Waits for backend health confirmation
   - Starts frontend (port 3030)
   - Waits for frontend initialization

5. **✅ Startup Verification**
   - Health checks both servers
   - Confirms API connectivity
   - Displays final status and URLs

## Troubleshooting

### Common Issues

1. **Port Still in Use After Conflict Resolution**
   - Use Option [18] for manual resolution
   - Check for hidden processes with Task Manager
   - Try Option [X] for nuclear kill if needed

2. **Backend Health Check Fails**
   - Verify backend server logs
   - Check for API key configuration issues
   - Use Option [14] for detailed health check

3. **Frontend Won't Start**
   - Check for TypeScript compilation errors
   - Verify package dependencies are installed
   - Use Option [15] for system diagnostics

### Debug Mode

Run with verbose output to see detailed process information:

```powershell
.\test-enhanced-launcher.ps1 -Verbose
```

## Benefits

### Before Enhancement

- Manual process identification and killing required
- Port conflicts caused startup failures
- Dynamic port assignment broke API routes
- No verification of successful startup
- Time-consuming troubleshooting

### After Enhancement

- ✅ Automatic conflict detection and resolution
- ✅ Intelligent process management
- ✅ Consistent port configuration
- ✅ Verified startup process
- ✅ Clear error messages and guidance
- ✅ Reduced manual intervention
- ✅ Reliable development environment

## Technical Details

### Port Detection Methods

1. **Primary**: `Get-NetTCPConnection` (fast, reliable)
2. **Fallback**: `netstat -ano` (compatibility)

### Process Management

- Uses PowerShell jobs for server processes
- Implements graceful shutdown before force kill
- Maintains process references for cleanup

### Configuration Management

- JSON-based port configuration
- Environment variable synchronization
- Automatic backup and restore

## Future Enhancements

- [ ] Docker container conflict detection
- [ ] Multiple environment support (dev/staging/prod)
- [ ] Automatic dependency updates
- [ ] Performance monitoring integration
- [ ] Cloud deployment integration

---

**Note**: This enhanced launcher eliminates the need for manual port management and ensures a reliable, consistent development environment startup process.
