# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

DL Organizer is a comprehensive AI-powered driver's license OCR processing system built as a hybrid web application. It combines a Next.js frontend with Express.js backend to provide folder management, image processing, and multi-provider OCR capabilities for extracting structured data from driver's license images.

**ReadySearch Integration**: ReadySearch v3.0 is now fully integrated as part of the main repository (previously a git submodule). All ReadySearch functionality including Australian DL database search, enhanced CLI tools, and browser automation is directly accessible within the `ReadySearch/` directory.

## Core Commands

### Development
```bash
# Start development environment (frontend + backend)
npm run dev

# Individual servers
npm run dev:frontend     # Next.js on port 3030
npm run dev:backend      # Express on port 3050

# Smart port management (auto-resolve conflicts)
npm run dev:smart
```

### Testing
```bash
npm run test                  # Jest unit tests
npm run test:watch           # Jest in watch mode
npm run test:e2e             # Playwright E2E tests
npm run test:integration     # Integration tests only
npm run lint                 # ESLint
npm run typecheck            # TypeScript checking

# Run specific E2E test
npm run test:e2e -- --grep "image processing"
```

### Production & Maintenance
```bash
npm run build               # Build for production
npm run start               # Start production servers
npm run setup               # Initial production setup
npm run health              # System health check
npm run backup              # Create database backup
npm run backup:restore      # Restore from backup
npm run maintenance         # Run maintenance tasks
npm run service:install     # Install as Windows service
```

### Utility Commands
```bash
# Port management
npm run ports:check         # Check port availability
npm run ports:resolve       # Resolve port conflicts
npm run ports:force         # Force port resolution

# Advanced launcher
.\launcher.ps1              # PowerShell launcher
launcher.bat                # Batch launcher
```

## Architecture Overview

### Technology Stack
- **Frontend**: Next.js 14.2+ (App Router) with TypeScript 5.8+
- **Styling**: Tailwind CSS + Radix UI components
- **State Management**: React state with custom hooks
- **Backend**: Express.js with middleware stack
- **Database**: SQLite with synchronous API
- **Image Processing**: Sharp for thumbnails and transformations
- **AI Integration**: Multi-provider OCR (OpenAI GPT-4o, OpenRouter models)
- **Testing**: Jest (unit), Playwright (E2E)
- **Windows Integration**: Native filesystem APIs, drive detection

### Core Components

#### Backend Architecture (`backend/`)

**Main Server**
- `server.js` - Express server with comprehensive middleware stack, error handling, and graceful shutdown

**Services Layer** (`backend/services/`)
- `ocr-service.js` - Multi-provider OCR orchestration with fallback strategies
- `model-validator.js` - Real-time model availability checking with caching
- `batch-processor.js` - Queue-based bulk processing with progress tracking
- `cost-tracker.js` - API usage monitoring with spending limits
- `file-manager.js` - File organization and naming strategies
- `local-model-service.js` - Local model integration support

**Utilities** (`backend/utils/`)
- `windows-fs.js` - Windows-specific filesystem operations
- `image-processor.js` - Sharp-based processing with format conversion
- `logger.js` - Winston logger with rotation
- `database.js` - SQLite connection management

**Routes** (`backend/routes/`)
- `ocr.js` - OCR endpoints with validation
- `batch-ocr.js` - Batch processing endpoints
- `filesystem.js` - File system operations
- `folders.js` - Folder management
- `images.js` - Image operations
- `projects.js` - Project CRUD
- `model-validation.js` - Model checking endpoints

#### Frontend Architecture (`src/`)

**Components** (`src/components/dl-organizer/`)
- `project-overview.tsx` - Main dashboard with statistics
- `folder-tree.tsx` - Recursive folder navigation
- `enhanced-image-grid.tsx` - Virtualized image grid with hover previews
- `ocr-panel.tsx` - Multi-provider OCR interface
- `batch-mode-panel.tsx` - Bulk processing UI
- `ocr-testing-playground.tsx` - Model testing and validation
- `text-editor.tsx` - In-app file editing
- `settings-panel.tsx` - Configuration management
- `model-validation-panel.tsx` - Real-time model checking

**API Integration** (`src/app/api/`)
- Next.js API routes for frontend-backend communication
- Proxy endpoints for OCR providers
- Image serving endpoints

**Utilities** (`src/lib/`)
- `utils.ts` - Common utilities and helpers
- `storage-utils.ts` - LocalStorage abstraction
- `australian-ocr-merger.ts` - AU-specific OCR logic

**Type Definitions** (`src/types/`)
- Comprehensive TypeScript interfaces for all data models

### Key APIs

#### OCR Operations
```typescript
POST /api/ocr/analyze               // Single image OCR
POST /api/ocr/analyze-by-path       // Path-based OCR
GET /api/ocr/prompts                // Available prompts
POST /api/ocr/save-results          // Persist results
GET /api/ocr/saved-data/:imageId    // Retrieve saved data
```

#### Model Validation
```typescript
GET /api/model-validation/available     // List available models
POST /api/model-validation/validate     // Validate model list
POST /api/model-validation/auto-fix     // Auto-fix invalid models
DELETE /api/model-validation/cache      // Clear validation cache
```

#### Batch Processing
```typescript
POST /api/batch-ocr/process            // Start batch job
GET /api/batch-ocr/status/:jobId       // Job status
POST /api/batch-ocr/cancel/:jobId      // Cancel job
```

#### File System
```typescript
GET /api/filesystem/drives             // Windows drives
POST /api/filesystem/scan              // Scan directory
GET /api/folders/:id/images            // List images
POST /api/folders/:id/text             // Text file ops
```

#### Image Operations
```typescript
GET /thumbnails/:filename              // Cached thumbnail
GET /previews/:filename                // Full preview
POST /api/images/:id/rotate            // Rotate image
```

## Data Models

### Core Types
```typescript
interface OCRResult {
  firstName: string;
  lastName: string;
  dateOfBirth: string;        // YYYY-MM-DD
  address: string;
  licenseNumber: string;
  expirationDate: string;     // YYYY-MM-DD
  state: string;              // Two-letter code
  confidence: number;         // 0.0-1.0
  rawText: string;
  mode: 'us' | 'australian';
  // Optional fields
  middleName?: string;
  issueDate?: string;         // US mode
  cardNumber?: string;        // AUS mode
}

interface Project {
  id: string;
  name: string;
  rootPath: string;
  createdAt: string;
  updatedAt: string;
  settings?: ProjectSettings;
}

interface FolderNode {
  id: string;
  name: string;
  path: string;
  imageCount: number;
  hasTextFile: boolean;
  children: FolderNode[];
  depth: number;
  isExpanded?: boolean;
}

interface BatchJob {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  totalFolders: number;
  processedFolders: number;
  results: BatchResult[];
  startedAt: string;
  completedAt?: string;
}
```

### Database Schema
- `projects` - Project configurations and settings
- `folders` - Hierarchical folder structure
- `images` - Image metadata, rotation state, OCR results
- `ocr_results` - Extracted data with provider info
- `batch_jobs` - Batch processing state
- `api_usage` - Cost tracking data

## Development Workflow

### Adding New Features

1. **Backend Implementation**:
   - Create service in `backend/services/`
   - Add routes in `backend/routes/`
   - Update database schema if needed
   - Add error handling and logging
   - Write unit tests

2. **Frontend Implementation**:
   - Create components in `src/components/dl-organizer/`
   - Add types in `src/types/`
   - Implement API calls
   - Handle loading/error states
   - Add user feedback (toasts, progress)

3. **Testing**:
   - Unit tests for business logic
   - Integration tests for API endpoints
   - E2E tests for user workflows
   - Visual regression tests for UI

### OCR Provider Integration

1. **Backend Setup**:
   - Add provider config in `ocr-service.js`
   - Implement API client with error handling
   - Add cost tracking metrics
   - Set up rate limiting

2. **Model Configuration**:
   - Define model capabilities
   - Set pricing information
   - Configure retry strategies
   - Add validation logic

3. **Frontend Integration**:
   - Update model selector UI
   - Add provider-specific settings
   - Implement connection testing
   - Show provider status

### Testing Strategy

**Unit Tests** (Jest)
- Business logic isolation
- Service layer testing
- Utility function coverage
- Mock external dependencies

**E2E Tests** (Playwright)
- User workflow validation
- Cross-browser testing
- Visual regression checks
- Performance monitoring

**Test Organization**
```
tests/
├── ocr-testing/          # OCR model tests
├── batch-processing/     # Batch operation tests
├── integration/          # API integration tests
└── *.spec.ts            # Feature-specific tests
```

## Common Patterns

### Error Handling
```javascript
// Consistent error structure
class APIError extends Error {
  constructor(message, statusCode, details) {
    super(message);
    this.statusCode = statusCode;
    this.details = details;
  }
}

// Middleware error handler
app.use((err, req, res, next) => {
  logger.error(err);
  res.status(err.statusCode || 500).json({
    error: err.message,
    details: err.details
  });
});
```

### Progress Tracking
```javascript
// Emit progress events
eventEmitter.emit('progress', {
  jobId,
  current: processedCount,
  total: totalCount,
  percentage: Math.round((processedCount / totalCount) * 100)
});
```

### Caching Strategy
- Thumbnails: File-based with Sharp
- OCR Results: Database with TTL
- Model Validation: In-memory with 5min expiry
- API Responses: Memory cache for static data

## Windows-Specific Considerations

### File System
- Handle UNC paths and network drives
- Support paths >260 characters
- Implement retry logic for locked files
- Normalize path separators

### Security
- Validate all file paths
- Prevent directory traversal
- Handle permission errors gracefully
- Use Windows Credential Manager for secrets

### Performance
- Batch file operations
- Use streaming for large files
- Implement connection pooling
- Cache directory listings

## Production Deployment

### Health Monitoring
- Endpoint: `/api/health`
- Database connectivity check
- Disk space monitoring
- API provider status
- Memory usage tracking

### Logging
- Location: `data/logs/`
- Rotation: Daily with 7-day retention
- Levels: error, warn, info, debug
- Format: JSON for parsing

### Backup Strategy
- Database: SQLite backup API
- Scheduled via Windows Task Scheduler
- Retention: 30 days
- Location: `data/backups/`

## Memory Notes

### Development Warnings
- NEVER RUN "taskkill //F //IM node.exe:" - kills claude code environment
- Check port availability before starting servers
- Validate API keys through settings panel
- Monitor rate limits during batch operations

### Performance Tips
- Enable thumbnail caching for large folders
- Use batch processing for >10 images
- Implement pagination for folder trees >1000 items
- Consider worker threads for CPU-intensive tasks

This documentation provides the essential information needed to work effectively with the DL Organizer codebase while maintaining its robust architecture and Windows-optimized features.