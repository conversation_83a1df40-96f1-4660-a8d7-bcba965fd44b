import { NextRequest, NextResponse } from 'next/server'

const BACKEND_URL = process.env.BACKEND_PORT 
  ? `http://localhost:${process.env.BACKEND_PORT}` 
  : 'http://localhost:3003'

export async function GET(request: NextRequest) {
  try {
    // Forward the request to the backend
    const response = await fetch(`${BACKEND_URL}/api/health`, {
      method: 'GET',
      headers: {
        // Forward relevant headers
        'User-Agent': request.headers.get('User-Agent') || '',
      },
    })

    // Get response data
    const data = await response.text()
    
    // Return response with same status and headers
    return new NextResponse(data, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        'Content-Type': 'application/json',
      },
    })
    
  } catch (error) {
    console.error('API Proxy Error (health):', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Proxy error: Failed to forward request to backend',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    )
  }
}