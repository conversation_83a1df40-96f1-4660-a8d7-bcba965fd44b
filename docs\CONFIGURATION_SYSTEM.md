# Centralized Configuration System

## Overview

This document describes the centralized configuration system that ensures consistent port and URL management across all components of the DL Organizer application. This system was designed to solve recurring port configuration issues and provide a single source of truth for all application endpoints.

## Problem Solved

Previously, the application had multiple hardcoded port references scattered throughout the codebase:
- Hardcoded `localhost:3003` in API routes
- Inconsistent backend URL construction patterns
- Launcher script with fixed port assignments
- Manual environment variable management
- Recurring port conflicts when switching between development environments

## Architecture

### Core Components

1. **`src/config/app-config.ts`** - Centralized configuration module
2. **`data/port-config.json`** - Dynamic port configuration file (created by launcher)
3. **`launcher.ps1`** - Updated to create/manage port configuration
4. **Updated API routes** - All routes now use centralized configuration

### Configuration Hierarchy

The system loads configuration in the following priority order:

1. **port-config.json environment variables** (highest priority)
2. **port-config.json port assignments**
3. **System environment variables**
4. **Default values** (lowest priority)

## Files and Structure

### 1. Centralized Configuration (`src/config/app-config.ts`)

```typescript
import { getAppConfig } from '@/config/app-config'

// Get current configuration
const config = getAppConfig()

// Use configuration
const backendUrl = config.backendUrl
const thumbnailUrl = getThumbnailUrl(imageId)
const apiUrl = getApiUrl('projects')
```

**Key Functions:**
- `getAppConfig()` - Returns current application configuration
- `getThumbnailUrl(imageId, cacheBust?)` - Generates thumbnail URLs
- `getPreviewUrl(imageId, cacheBust?)` - Generates preview URLs
- `getApiUrl(endpoint)` - Generates API endpoint URLs
- `normalizeUrl(url, type)` - Fixes hardcoded URLs
- `reloadAppConfig()` - Forces configuration reload

### 2. Port Configuration File (`data/port-config.json`)

Automatically created/updated by the launcher:

```json
{
  "ports": {
    "frontend": 3030,
    "backend": 3014,
    "ngrok": 4040
  },
  "environment": {
    "FRONTEND_PORT": "3030",
    "BACKEND_PORT": "3014",
    "BACKEND_URL": "http://localhost:3014",
    "NODE_ENV": "development"
  },
  "urls": {
    "frontend": "http://localhost:3030",
    "backend": "http://localhost:3014"
  },
  "lastUpdated": "2024-01-15T10:30:00.000Z",
  "updatedBy": "launcher.ps1"
}
```

### 3. Updated Launcher (`launcher.ps1`)

The launcher now includes:
- `Update-PortConfiguration` function
- Automatic port-config.json creation
- Dynamic port assignment (changed from hardcoded 3003 to 3014)

### 4. Updated API Routes

All API routes now use the centralized configuration:

**Before:**
```typescript
const BACKEND_URL = process.env.BACKEND_PORT 
  ? `http://localhost:${process.env.BACKEND_PORT}` 
  : 'http://localhost:3003'
```

**After:**
```typescript
import { getAppConfig } from '@/config/app-config'
const config = getAppConfig()
const backendUrl = config.backendUrl
```

## Usage Guide

### For Developers

1. **Import the configuration:**
   ```typescript
   import { getAppConfig, getThumbnailUrl, getApiUrl } from '@/config/app-config'
   ```

2. **Use configuration instead of hardcoded values:**
   ```typescript
   // ❌ Don't do this
   const url = 'http://localhost:3003/api/projects'
   
   // ✅ Do this
   const url = getApiUrl('projects')
   ```

3. **For image URLs:**
   ```typescript
   // ❌ Don't do this
   const thumbnailUrl = `http://localhost:3003/thumbnails/${imageId}.jpg`
   
   // ✅ Do this
   const thumbnailUrl = getThumbnailUrl(imageId)
   ```

### For System Administration

1. **Starting servers:**
   - Use `launcher.ps1` or `launcher.bat`
   - Port configuration is automatically created/updated
   - No manual environment variable setup required

2. **Changing ports:**
   - Edit the port variables in `launcher.ps1` (lines 42-44)
   - Or manually edit `data/port-config.json`
   - Restart servers to apply changes

3. **Debugging configuration:**
   ```typescript
   import { getAppConfig } from '@/config/app-config'
   console.log('Current config:', getAppConfig())
   ```

## Migration Guide

### Updating Existing Code

1. **Replace hardcoded URLs:**
   ```typescript
   // Find and replace patterns like:
   'http://localhost:3003' → getAppConfig().backendUrl
   '/thumbnails/' → getThumbnailUrl()
   '/previews/' → getPreviewUrl()
   ```

2. **Update API route patterns:**
   ```typescript
   // Old pattern
   const BACKEND_URL = process.env.BACKEND_PORT 
     ? `http://localhost:${process.env.BACKEND_PORT}` 
     : 'http://localhost:3003'
   
   // New pattern
   import { getAppConfig } from '@/config/app-config'
   const config = getAppConfig()
   ```

3. **Update component imports:**
   ```typescript
   // Add to components that need URLs
   import { getThumbnailUrl, getPreviewUrl } from '@/config/app-config'
   ```

## Troubleshooting

### Common Issues

1. **"Failed to fetch" errors:**
   - Check that `data/port-config.json` exists
   - Verify ports in configuration match running servers
   - Restart servers to reload configuration

2. **Port conflicts:**
   - The launcher automatically resolves port conflicts
   - Check launcher output for actual assigned ports
   - Configuration file reflects resolved ports

3. **Thumbnail/Preview 404 errors:**
   - Verify base64 path encoding is working
   - Check API route logs for decoding issues
   - Ensure backend thumbnail generation is working

### Debug Commands

```bash
# Check current configuration
node -e "console.log(require('./src/config/app-config').getAppConfig())"

# Verify port-config.json
cat data/port-config.json

# Check running processes
netstat -ano | findstr :3014
netstat -ano | findstr :3030
```

## Benefits

1. **Single Source of Truth:** All port and URL configuration in one place
2. **Dynamic Configuration:** Ports can be changed without code modifications
3. **Automatic Resolution:** Launcher handles port conflicts automatically
4. **Consistent Patterns:** All components use the same configuration methods
5. **Easy Debugging:** Clear logging and configuration visibility
6. **Future-Proof:** Easy to add new configuration options

## Future Enhancements

- Environment-specific configurations (dev/staging/prod)
- Configuration validation and error handling
- Hot-reload of configuration changes
- Integration with Docker and deployment systems
- Configuration UI for non-technical users

## Related Files

- `src/config/app-config.ts` - Main configuration module
- `data/port-config.json` - Dynamic configuration file
- `launcher.ps1` - Launcher with configuration management
- `src/app/api/*/route.ts` - Updated API routes
- `src/app/page.tsx` - Updated main page component
- `.env.local` - Environment variables (still supported)

---

**Last Updated:** January 2024  
**Version:** 1.0  
**Maintainer:** DL Organizer Development Team
