"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, SelectGroup, SelectLabel, SelectSeparator } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Settings, Save, RefreshCw, Eye, EyeOff, AlertCircle, CheckCircle, Key, Bot, ExternalLink, Search, Edit3, Plus } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useSettingsStandalone, type VisionModel, type OpenRouterConfig } from '@/hooks/use-settings-sync'
import PromptEditor from './prompt-editor'

interface SettingsPanelProps {
  className?: string
}

export default function SettingsPanel({ className }: SettingsPanelProps) {
  // Use shared settings synchronization
  const {
    openRouterConfig,
    isLoading,
    error,
    connectionStatus,
    saveOpenRouterConfig,
    testConnection,
    clearError
  } = useSettingsStandalone()

  const [localConfig, setLocalConfig] = useState<OpenRouterConfig>(openRouterConfig)
  const [showCustomModel, setShowCustomModel] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [showApiKey, setShowApiKey] = useState(false)
  const [showDialog, setShowDialog] = useState(false)
  const [promptEditorOpen, setPromptEditorOpen] = useState<string | null>(null)

  // Sync with global settings when they change
  useEffect(() => {
    setLocalConfig(openRouterConfig)
  }, [openRouterConfig])

  // Top vision models available on OpenRouter (expanded with more options)
  const visionModels: VisionModel[] = [
    // Free models (top tier)
    {
      id: 'google/gemini-flash-1.5',
      name: 'Gemini Flash 1.5',
      provider: 'Google',
      cost: 'Free',
      description: 'Fast and efficient vision model for document analysis',
      tier: 'free',
      contextWindow: 1000000,
      maxOutputTokens: 8192
    },
    {
      id: 'qwen/qwen-2-vl-7b-instruct',
      name: 'Qwen2-VL 7B',
      provider: 'Qwen',
      cost: 'Free',
      description: 'Open-source vision-language model with good OCR performance',
      tier: 'free',
      contextWindow: 32768,
      maxOutputTokens: 8192
    },
    {
      id: 'meta-llama/llama-3.2-11b-vision-instruct',
      name: 'Llama 3.2 11B Vision',
      provider: 'Meta',
      cost: 'Free',
      description: 'Meta\'s vision-capable language model',
      tier: 'free',
      contextWindow: 131072,
      maxOutputTokens: 2048
    },
    {
      id: 'microsoft/phi-3.5-vision-instruct',
      name: 'Phi-3.5 Vision',
      provider: 'Microsoft',
      cost: 'Free',
      description: 'Compact but powerful vision model',
      tier: 'free',
      contextWindow: 128000,
      maxOutputTokens: 4096
    },
    // Paid models (top tier)
    {
      id: 'openai/gpt-4o',
      name: 'GPT-4o',
      provider: 'OpenAI',
      cost: '$15/1M tokens',
      description: 'Most capable multimodal model for complex document analysis',
      tier: 'paid',
      contextWindow: 128000,
      maxOutputTokens: 4096
    },
    {
      id: 'openai/gpt-4o-mini',
      name: 'GPT-4o Mini',
      provider: 'OpenAI',
      cost: '$0.15/1M tokens',
      description: 'Affordable multimodal model with excellent performance',
      tier: 'paid',
      contextWindow: 128000,
      maxOutputTokens: 16384
    },
    {
      id: 'anthropic/claude-3.5-sonnet',
      name: 'Claude 3.5 Sonnet',
      provider: 'Anthropic',
      cost: '$3/1M tokens',
      description: 'Excellent vision and reasoning capabilities',
      tier: 'paid',
      contextWindow: 200000,
      maxOutputTokens: 4096
    },
    {
      id: 'google/gemini-pro-1.5',
      name: 'Gemini Pro 1.5',
      provider: 'Google',
      cost: '$7/1M tokens',
      description: 'Large context window for complex documents',
      tier: 'paid',
      contextWindow: 2000000,
      maxOutputTokens: 8192
    },
    {
      id: 'anthropic/claude-3-5-haiku',
      name: 'Claude 3.5 Haiku',
      provider: 'Anthropic',
      cost: '$0.25/1M tokens',
      description: 'Fast and cost-effective vision model',
      tier: 'paid',
      contextWindow: 200000,
      maxOutputTokens: 4096
    }
  ]

  const saveConfig = async () => {
    clearError()

    try {
      await saveOpenRouterConfig(localConfig)

      // Test connection after saving if API key and model are configured
      if (localConfig.apiKey && localConfig.selectedModel) {
        await testConnection()
      }
    } catch (error) {
      // Error is already handled by the shared hook
      console.error('Failed to save settings:', error)
    }
  }

  const getModelsByTier = (tier: 'free' | 'paid') => {
    const models = visionModels.filter(model => model.tier === tier)
    if (searchQuery) {
      return models.filter(model => 
        model.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        model.provider.toLowerCase().includes(searchQuery.toLowerCase()) ||
        model.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }
    return models
  }

  const getSelectedModel = () => {
    return visionModels.find(model => model.id === localConfig.selectedModel)
  }

  const formatContextWindow = (tokens: number) => {
    if (tokens >= 1000000) {
      return `${(tokens / 1000000).toFixed(1)}M`
    } else if (tokens >= 1000) {
      return `${(tokens / 1000).toFixed(0)}K`
    }
    return tokens.toString()
  }

  return (
    <Dialog open={showDialog} onOpenChange={setShowDialog}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Settings className="h-4 w-4 mr-2" />
          Settings
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Application Settings</DialogTitle>
        </DialogHeader>
        
        <Tabs defaultValue="openrouter" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="openrouter">OpenRouter AI</TabsTrigger>
            <TabsTrigger value="prompts">AI Prompts</TabsTrigger>
            <TabsTrigger value="general">General</TabsTrigger>
          </TabsList>

          <TabsContent value="openrouter" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bot className="h-5 w-5" />
                  OpenRouter Configuration
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {error && (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                {/* API Key */}
                <div className="space-y-2">
                  <Label htmlFor="apiKey">API Key</Label>
                  <div className="relative">
                    <Input
                      id="apiKey"
                      type={showApiKey ? "text" : "password"}
                      placeholder="Enter your OpenRouter API key"
                      value={localConfig.apiKey}
                      onChange={(e) => setLocalConfig({ ...localConfig, apiKey: e.target.value })}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowApiKey(!showApiKey)}
                    >
                      {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Get your API key from{' '}
                    <a href="https://openrouter.ai/keys" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                      OpenRouter.ai
                    </a>
                  </p>
                </div>

                {/* Base URL */}
                <div className="space-y-2">
                  <Label htmlFor="baseUrl">Base URL</Label>
                  <Input
                    id="baseUrl"
                    placeholder="https://openrouter.ai/api/v1"
                    value={localConfig.baseUrl}
                    onChange={(e) => setLocalConfig({ ...localConfig, baseUrl: e.target.value })}
                  />
                </div>

                {/* Model Selection */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="model">Vision Model</Label>
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setShowCustomModel(!showCustomModel)}
                      >
                        {showCustomModel ? 'Use Preset' : 'Custom Model'}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        asChild
                      >
                        <a
                          href="https://openrouter.ai/models"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <ExternalLink className="h-3 w-3 mr-1" />
                          Browse All Models
                        </a>
                      </Button>
                    </div>
                  </div>
                  
                  {showCustomModel ? (
                    <div className="space-y-2">
                      <Input
                        placeholder="Enter custom model ID (e.g., openai/gpt-4o)"
                        value={localConfig.customModel || ''}
                        onChange={(e) => setLocalConfig({ ...localConfig, customModel: e.target.value, selectedModel: e.target.value })}
                      />
                      <p className="text-sm text-muted-foreground">
                        Find available models at{' '}
                        <a href="https://openrouter.ai/models" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                          OpenRouter.ai/models
                        </a>
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {/* Search functionality */}
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search models..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                      
                      <Select value={localConfig.selectedModel} onValueChange={(value) => setLocalConfig({ ...localConfig, selectedModel: value, customModel: '' })}>
                        <SelectTrigger className="h-12">
                          <SelectValue placeholder="Select a vision model" />
                        </SelectTrigger>
                        <SelectContent className="max-h-[500px]" position="popper" sideOffset={5}>
                          <SelectGroup>
                            <SelectLabel className="text-green-600 font-semibold">🆓 Free Models</SelectLabel>
                            {getModelsByTier('free').map((model) => (
                              <SelectItem key={model.id} value={model.id} className="py-2">
                                <div className="flex flex-col">
                                  <div className="flex items-center gap-2">
                                    <span className="font-medium">{model.name}</span>
                                    <span className="text-xs text-muted-foreground">({model.provider})</span>
                                  </div>
                                  <span className="text-xs text-muted-foreground">{model.cost}</span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectGroup>
                          
                          <SelectSeparator />
                          
                          <SelectGroup>
                            <SelectLabel className="text-blue-600 font-semibold">💰 Paid Models</SelectLabel>
                            {getModelsByTier('paid').map((model) => (
                              <SelectItem key={model.id} value={model.id} className="py-2">
                                <div className="flex flex-col">
                                  <div className="flex items-center gap-2">
                                    <span className="font-medium">{model.name}</span>
                                    <span className="text-xs text-muted-foreground">({model.provider})</span>
                                  </div>
                                  <span className="text-xs text-muted-foreground">{model.cost}</span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>

                {/* Selected Model Info */}
                {localConfig.selectedModel && (
                  <Card className="bg-muted/50">
                    <CardContent className="pt-4">
                      {(() => {
                        const model = getSelectedModel()
                        if (!model) return null
                        
                        return (
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium">{model.name}</h4>
                              <Badge variant="secondary">{model.provider}</Badge>
                              <Badge variant={model.tier === 'free' ? 'default' : 'outline'}>
                                {model.tier === 'free' ? 'FREE' : model.cost}
                              </Badge>
                            </div>
                            <p className="text-sm text-muted-foreground">{model.description}</p>
                            <div className="flex gap-4 text-xs text-muted-foreground">
                              <span>Context: {formatContextWindow(model.contextWindow)} tokens</span>
                              <span>Max Output: {formatContextWindow(model.maxOutputTokens)} tokens</span>
                            </div>
                          </div>
                        )
                      })()}
                    </CardContent>
                  </Card>
                )}

                {/* OCR Mode Selection */}
                <div className="space-y-3">
                  <Label>OCR Processing Mode</Label>
                  <Select value={localConfig.ocrMode} onValueChange={(value: 'us' | 'australian' | 'auto-detect') => setLocalConfig({ ...localConfig, ocrMode: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select OCR mode" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="auto-detect">
                        <div className="flex flex-col">
                          <span className="font-medium">Auto-Detect Mode</span>
                          <span className="text-xs text-muted-foreground">
                            Automatically detects document type: Driver License, Passport, ID, or Selfie
                          </span>
                        </div>
                      </SelectItem>
                      <SelectItem value="us">
                        <div className="flex flex-col">
                          <span className="font-medium">US Driver License / ID</span>
                          <span className="text-xs text-muted-foreground">
                            Standard US format with first, middle, last name
                          </span>
                        </div>
                      </SelectItem>
                      <SelectItem value="australian">
                        <div className="flex flex-col">
                          <span className="font-medium">Australian Driver License</span>
                          <span className="text-xs text-muted-foreground">
                            Australian format with surname, given names, front/back detection
                          </span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  
                  {localConfig.ocrMode === 'auto-detect' && (
                    <div className="p-3 bg-purple-50 dark:bg-purple-950/20 rounded-md">
                      <h4 className="font-medium text-sm mb-2">Auto-Detect Mode Features:</h4>
                      <ul className="text-xs text-muted-foreground space-y-1">
                        <li>• Automatically identifies document type (Driver License, Passport, ID, Selfie)</li>
                        <li>• Extracts appropriate fields based on detected document type</li>
                        <li>• Supports mixed folder processing with different document types</li>
                        <li>• Provides document type confidence scoring</li>
                        <li>• Generates selfie descriptions for person identification</li>
                        <li>• Handles US and Australian formats automatically</li>
                      </ul>
                    </div>
                  )}

                  {localConfig.ocrMode === 'australian' && (
                    <div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded-md">
                      <h4 className="font-medium text-sm mb-2">Australian Mode Features:</h4>
                      <ul className="text-xs text-muted-foreground space-y-1">
                        <li>• Surname and Given Names extraction</li>
                        <li>• Address, DOB, License #, Card # (state-dependent)</li>
                        <li>• Expiration Date and State detection</li>
                        <li>• Automatic front/back card detection</li>
                        <li>• Intelligent field merging for multi-side cards</li>
                      </ul>
                    </div>
                  )}
                  
                  {localConfig.ocrMode === 'us' && (
                    <div className="p-3 bg-green-50 dark:bg-green-950/20 rounded-md">
                      <h4 className="font-medium text-sm mb-2">US Mode Features:</h4>
                      <ul className="text-xs text-muted-foreground space-y-1">
                        <li>• First, Middle, and Last Name extraction</li>
                        <li>• Date of Birth, Address, License Number</li>
                        <li>• Issue Date, Expiration Date, State</li>
                        <li>• Standard US driver&apos;s license format</li>
                      </ul>
                    </div>
                  )}
                </div>

                {/* Connection Status */}
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-2">
                    <div className={cn(
                      "h-2 w-2 rounded-full",
                      connectionStatus === 'connected' ? 'bg-green-500' : 
                      connectionStatus === 'failed' ? 'bg-red-500' : 'bg-gray-300'
                    )} />
                    <span className="text-sm text-muted-foreground">
                      {connectionStatus === 'connected' ? 'Connected' : 
                       connectionStatus === 'failed' ? 'Connection Failed' : 'Not Tested'}
                    </span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={testConnection}
                    disabled={!localConfig.apiKey || !localConfig.selectedModel || isLoading}
                  >
                    {isLoading ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <CheckCircle className="h-4 w-4 mr-2" />
                    )}
                    Test Connection
                  </Button>
                </div>

                {/* Actions */}
                <div className="flex gap-2 pt-4">
                  <Button onClick={saveConfig} disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Save Configuration
                      </>
                    )}
                  </Button>
                  <Button variant="outline" onClick={() => setShowDialog(false)}>
                    Close
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* AI Prompts Settings */}
          <TabsContent value="prompts" className="space-y-6">
            <div>
              <h3 className="font-semibold text-lg mb-4">AI Prompt Templates</h3>
              <p className="text-sm text-muted-foreground mb-6">
                Configure AI prompts for different operations. These prompts control how the AI processes and extracts information from documents and images.
              </p>
              
              <div className="space-y-4">
                {/* OCR Document Prompts */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base">OCR Document Templates</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-3 border rounded-lg space-y-2">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-sm">US Driver License</h4>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => setPromptEditorOpen('us-driver')}
                          >
                            <Edit3 className="h-3 w-3 mr-1" />
                            Edit
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Extracts personal information and license details from US driver licenses
                        </p>
                      </div>
                      
                      <div className="p-3 border rounded-lg space-y-2">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-sm">Australian Driver License</h4>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => setPromptEditorOpen('aus-driver')}
                          >
                            <Edit3 className="h-3 w-3 mr-1" />
                            Edit
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Extracts personal information and license details from Australian driver licenses
                        </p>
                      </div>
                      
                      <div className="p-3 border rounded-lg space-y-2">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-sm">Passport</h4>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => setPromptEditorOpen('passport')}
                          >
                            <Edit3 className="h-3 w-3 mr-1" />
                            Edit
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Extracts information from passport documents
                        </p>
                      </div>
                      
                      <div className="p-3 border rounded-lg space-y-2">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-sm">Auto-Detect</h4>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => setPromptEditorOpen('auto-detect')}
                          >
                            <Edit3 className="h-3 w-3 mr-1" />
                            Edit
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Automatically detects document type and extracts relevant information
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                {/* Folder Analysis Prompt */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base">Smart Folder Analysis</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="p-3 border rounded-lg space-y-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-sm">Folder Analysis Prompt</h4>
                          <p className="text-xs text-muted-foreground mt-1">
                            AI prompt used to analyze image folders and provide smart filtering suggestions
                          </p>
                        </div>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => setPromptEditorOpen('folder-analysis')}
                        >
                          <Edit3 className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                      </div>
                      <div className="mt-3 p-2 bg-muted/50 rounded text-xs">
                        <strong>Usage:</strong> This prompt is used when you click &ldquo;Analyze Folder&rdquo; to categorize images and suggest smart selection filters.
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="general" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Field Extraction Templates</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-3">Document Type Configuration</h4>
                    <p className="text-sm text-muted-foreground mb-4">
                      Configure which fields are extracted for different document types and countries.
                    </p>
                    
                    <Tabs defaultValue="us-driver" className="w-full">
                      <TabsList className="grid grid-cols-4 mb-4">
                        <TabsTrigger value="us-driver">US Driver License</TabsTrigger>
                        <TabsTrigger value="aus-driver">Australian Driver License</TabsTrigger>
                        <TabsTrigger value="passport">Passport</TabsTrigger>
                        <TabsTrigger value="auto-detect">Auto-Detect</TabsTrigger>
                      </TabsList>

                      {/* US Driver License Fields */}
                      <TabsContent value="us-driver" className="space-y-4">
                        <div className="p-4 bg-green-50 dark:bg-green-950/20 rounded-lg">
                          <h5 className="font-medium text-sm mb-3">US Driver License Fields</h5>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <h6 className="font-medium mb-2">Personal Information</h6>
                              <ul className="space-y-1 text-muted-foreground">
                                <li>• First Name *</li>
                                <li>• Middle Name</li>
                                <li>• Last Name *</li>
                                <li>• Date of Birth *</li>
                                <li>• Address</li>
                              </ul>
                            </div>
                            <div>
                              <h6 className="font-medium mb-2">License Information</h6>
                              <ul className="space-y-1 text-muted-foreground">
                                <li>• License Number *</li>
                                <li>• State *</li>
                                <li>• Issue Date</li>
                                <li>• Expiration Date</li>
                                <li>• Card Side Detection</li>
                              </ul>
                            </div>
                          </div>
                          <div className="mt-4 pt-3 border-t">
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-muted-foreground">
                                AI Prompt: US Driver License Template
                              </span>
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => setPromptEditorOpen('us-driver')}
                              >
                                <Edit3 className="h-3 w-3 mr-1" />
                                Edit Prompt
                              </Button>
                            </div>
                          </div>
                        </div>
                      </TabsContent>

                      {/* Australian Driver License Fields */}
                      <TabsContent value="aus-driver" className="space-y-4">
                        <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                          <h5 className="font-medium text-sm mb-3">Australian Driver License Fields</h5>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <h6 className="font-medium mb-2">Personal Information</h6>
                              <ul className="space-y-1 text-muted-foreground">
                                <li>• Surname *</li>
                                <li>• Given Names *</li>
                                <li>• Date of Birth *</li>
                                <li>• Address</li>
                              </ul>
                            </div>
                            <div>
                              <h6 className="font-medium mb-2">License Information</h6>
                              <ul className="space-y-1 text-muted-foreground">
                                <li>• Driver License Number *</li>
                                <li>• Card Number</li>
                                <li>• State/Territory *</li>
                                <li>• Expiration Date</li>
                                <li>• Card Side Detection</li>
                              </ul>
                            </div>
                          </div>
                          <div className="mt-4">
                            <div className="p-3 bg-yellow-50 dark:bg-yellow-950/20 rounded border border-yellow-200 dark:border-yellow-800">
                              <h6 className="font-medium text-sm text-yellow-800 dark:text-yellow-200 mb-2">
                                ✨ Recently Added: Card Number Field
                              </h6>
                              <p className="text-xs text-yellow-700 dark:text-yellow-300">
                                Australian driver licenses now extract card numbers when present (state-dependent feature).
                              </p>
                            </div>
                          </div>
                          <div className="mt-4 pt-3 border-t">
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-muted-foreground">
                                AI Prompt: Australian Driver License Template
                              </span>
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => setPromptEditorOpen('aus-driver')}
                              >
                                <Edit3 className="h-3 w-3 mr-1" />
                                Edit Prompt
                              </Button>
                            </div>
                          </div>
                        </div>
                      </TabsContent>

                      {/* Passport Fields */}
                      <TabsContent value="passport" className="space-y-4">
                        <div className="p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg">
                          <h5 className="font-medium text-sm mb-3">Passport Fields</h5>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <h6 className="font-medium mb-2">Personal Information</h6>
                              <ul className="space-y-1 text-muted-foreground">
                                <li>• First Name *</li>
                                <li>• Last Name *</li>
                                <li>• Date of Birth *</li>
                                <li>• Place of Birth</li>
                                <li>• Nationality</li>
                                <li>• Gender</li>
                              </ul>
                            </div>
                            <div>
                              <h6 className="font-medium mb-2">Passport Information</h6>
                              <ul className="space-y-1 text-muted-foreground">
                                <li>• Passport Number *</li>
                                <li>• Issuing Country *</li>
                                <li>• Issue Date</li>
                                <li>• Expiration Date</li>
                                <li>• Document Type</li>
                              </ul>
                            </div>
                          </div>
                          <div className="mt-4 pt-3 border-t">
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-muted-foreground">
                                AI Prompt: Passport Template
                              </span>
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => setPromptEditorOpen('passport')}
                              >
                                <Edit3 className="h-3 w-3 mr-1" />
                                Edit Prompt
                              </Button>
                            </div>
                          </div>
                        </div>
                      </TabsContent>

                      {/* Auto-Detect Mode */}
                      <TabsContent value="auto-detect" className="space-y-4">
                        <div className="p-4 bg-gray-50 dark:bg-gray-950/20 rounded-lg">
                          <h5 className="font-medium text-sm mb-3">Auto-Detect Mode</h5>
                          <p className="text-sm text-muted-foreground mb-4">
                            Automatically detects document type and extracts appropriate fields.
                          </p>
                          <div className="grid grid-cols-1 gap-4 text-sm">
                            <div>
                              <h6 className="font-medium mb-2">Supported Document Types</h6>
                              <ul className="space-y-1 text-muted-foreground">
                                <li>• Driver&apos;s License (US and Australian formats)</li>
                                <li>• Passport (International standard)</li>
                                <li>• ID Card (General identification)</li>
                                <li>• Selfie (Person description and identification)</li>
                              </ul>
                            </div>
                            <div>
                              <h6 className="font-medium mb-2">Auto-Detection Features</h6>
                              <ul className="space-y-1 text-muted-foreground">
                                <li>• Document type confidence scoring</li>
                                <li>• Field extraction based on detected type</li>
                                <li>• Mixed folder processing support</li>
                                <li>• Selfie description generation</li>
                              </ul>
                            </div>
                          </div>
                          <div className="mt-4 pt-3 border-t">
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-muted-foreground">
                                AI Prompt: Auto-Detection Template
                              </span>
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => setPromptEditorOpen('auto-detect')}
                              >
                                <Edit3 className="h-3 w-3 mr-1" />
                                Edit Prompt
                              </Button>
                            </div>
                          </div>
                        </div>
                      </TabsContent>
                    </Tabs>
                  </div>

                  <div className="pt-4 border-t">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium">Custom Field Configuration</h4>
                      <Button variant="outline" size="sm">
                        <Plus className="h-4 w-4 mr-2" />
                        Add Custom Field
                      </Button>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Add custom fields to extract additional information from your documents.
                      Custom fields will be included in the AI prompts for all relevant document types.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Prompt Editors */}
        <PromptEditor
          templateType="us-driver"
          isOpen={promptEditorOpen === 'us-driver'}
          onOpenChange={(open) => setPromptEditorOpen(open ? 'us-driver' : null)}
        />
        <PromptEditor
          templateType="aus-driver"
          isOpen={promptEditorOpen === 'aus-driver'}
          onOpenChange={(open) => setPromptEditorOpen(open ? 'aus-driver' : null)}
        />
        <PromptEditor
          templateType="passport"
          isOpen={promptEditorOpen === 'passport'}
          onOpenChange={(open) => setPromptEditorOpen(open ? 'passport' : null)}
        />
        <PromptEditor
          templateType="auto-detect"
          isOpen={promptEditorOpen === 'auto-detect'}
          onOpenChange={(open) => setPromptEditorOpen(open ? 'auto-detect' : null)}
        />
        <PromptEditor
          templateType={"folder-analysis" as any}
          isOpen={promptEditorOpen === 'folder-analysis'}
          onOpenChange={(open) => setPromptEditorOpen(open ? 'folder-analysis' : null)}
        />
      </DialogContent>
    </Dialog>
  )
}