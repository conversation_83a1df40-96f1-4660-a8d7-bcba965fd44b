const OpenAI = require("openai");
const fs = require("fs").promises;
const path = require("path");
const sharp = require("sharp");
const CostTracker = require("./cost-tracker");
const FileManager = require("./file-manager");

class OCRService {
  constructor() {
    this.providers = new Map();
    this.costTracker = new CostTracker();
    this.rateLimits = new Map();
    this.config = this.loadConfig();
    this.initializeProviders();
    // Initialize providers asynchronously
    this.initializeAsync();
  }

  async initializeAsync() {
    try {
      await this.autoConfigureOpenRouter();
      this.autoConfigureOpenAI();
      // Initialize cost tracker
      await this.costTracker.initialize();
    } catch (err) {
      console.error("OCR Service initialization failed:", err);
    }
  }

  loadConfig() {
    const defaultConfig = {
      providers: [],
      defaultModel: "google/gemini-flash-1.5",
      fallbackModels: [
        "openai/gpt-4o-mini",
        "meta-llama/llama-3.2-11b-vision-instruct",
        "anthropic/claude-3-5-haiku",
        "openai/gpt-4o",
      ],
      maxRetries: 3,
      retryDelay: 1000,
      timeout: 30000,
      costLimit: 10.0, // $10/month
      enableCostTracking: true,
      enableRateLimit: true,
      batchSize: 5,
      enableFallback: true,
      preferences: {
        prioritizeAccuracy: true,
        prioritizeSpeed: false,
        prioritizeCost: true,
      },
    };

    try {
      const configPath = path.join(process.cwd(), "config", "ocr-models.json");
      const configData = require(configPath);
      return { ...defaultConfig, ...configData };
    } catch (error) {
      console.log("Using default OCR config");
      return defaultConfig;
    }
  }

  initializeProviders() {
    // OpenRouter Provider
    this.providers.set("openrouter", {
      id: "openrouter",
      name: "OpenRouter",
      type: "api",
      baseUrl: "https://openrouter.ai/api/v1",
      models: [
        // Free models
        {
          id: "google/gemini-flash-1.5",
          name: "Gemini Flash 1.5",
          provider: "openrouter",
          type: "free",
          modelName: "google/gemini-flash-1.5",
          description: "Fast and efficient vision model for document analysis",
          accuracy: 0.86,
          avgProcessingTime: 3000,
          costPer1kTokens: 0,
          maxImageSize: 15 * 1024 * 1024,
          supportedFormats: ["jpg", "jpeg", "png", "webp"],
          rateLimit: { requests: 400, period: 3600 },
          isEnabled: true,
          priority: 9,
        },
        {
          id: "meta-llama/llama-3.2-11b-vision-instruct",
          name: "Llama 3.2 11B Vision",
          provider: "openrouter",
          type: "free",
          modelName: "meta-llama/llama-3.2-11b-vision-instruct",
          description: "Meta's vision-capable language model",
          accuracy: 0.8,
          avgProcessingTime: 5000,
          costPer1kTokens: 0,
          maxImageSize: 15 * 1024 * 1024,
          supportedFormats: ["jpg", "jpeg", "png", "webp"],
          rateLimit: { requests: 200, period: 3600 },
          isEnabled: true,
          priority: 7,
        },
        // Paid models
        {
          id: "openai/gpt-4o",
          name: "GPT-4o",
          provider: "openrouter",
          type: "paid",
          modelName: "openai/gpt-4o",
          description: "Advanced vision model with excellent OCR accuracy",
          accuracy: 0.95,
          avgProcessingTime: 8000,
          costPer1kTokens: 0.005,
          maxImageSize: 20 * 1024 * 1024,
          supportedFormats: ["jpg", "jpeg", "png", "webp"],
          rateLimit: { requests: 500, period: 3600 },
          isEnabled: true,
          priority: 10,
        },
        {
          id: "openai/gpt-4o-mini",
          name: "GPT-4o Mini",
          provider: "openrouter",
          type: "paid",
          modelName: "openai/gpt-4o-mini",
          description: "Cost-effective vision model with good OCR performance",
          accuracy: 0.88,
          avgProcessingTime: 5000,
          costPer1kTokens: 0.00015,
          maxImageSize: 20 * 1024 * 1024,
          supportedFormats: ["jpg", "jpeg", "png", "webp"],
          rateLimit: { requests: 500, period: 3600 },
          isEnabled: true,
          priority: 9,
        },
        {
          id: "anthropic/claude-3.5-sonnet",
          name: "Claude 3.5 Sonnet",
          provider: "openrouter",
          type: "paid",
          modelName: "anthropic/claude-3.5-sonnet",
          description:
            "High-quality vision model with strong reasoning capabilities",
          accuracy: 0.92,
          avgProcessingTime: 7000,
          costPer1kTokens: 0.003,
          maxImageSize: 20 * 1024 * 1024,
          supportedFormats: ["jpg", "jpeg", "png", "webp"],
          rateLimit: { requests: 200, period: 3600 },
          isEnabled: true,
          priority: 9,
        },
        {
          id: "google/gemini-pro-1.5",
          name: "Gemini Pro 1.5",
          provider: "openrouter",
          type: "paid",
          modelName: "google/gemini-pro-1.5",
          description:
            "High-performance vision model with advanced capabilities",
          accuracy: 0.9,
          avgProcessingTime: 6000,
          costPer1kTokens: 0.00125,
          maxImageSize: 20 * 1024 * 1024,
          supportedFormats: ["jpg", "jpeg", "png", "webp"],
          rateLimit: { requests: 300, period: 3600 },
          isEnabled: true,
          priority: 8,
        },
        {
          id: "anthropic/claude-3-5-haiku",
          name: "Claude 3.5 Haiku",
          provider: "openrouter",
          type: "paid",
          modelName: "anthropic/claude-3-5-haiku",
          description: "Fast and efficient vision model for OCR tasks",
          accuracy: 0.85,
          avgProcessingTime: 4000,
          costPer1kTokens: 0.00025,
          maxImageSize: 15 * 1024 * 1024,
          supportedFormats: ["jpg", "jpeg", "png", "webp"],
          rateLimit: { requests: 300, period: 3600 },
          isEnabled: true,
          priority: 8,
        },
      ],
      isConfigured: false,
    });

    // OpenAI Provider
    this.providers.set("openai", {
      id: "openai",
      name: "OpenAI",
      type: "api",
      baseUrl: "https://api.openai.com/v1",
      models: [
        {
          id: "gpt-4o-mini",
          name: "GPT-4o Mini",
          provider: "openai",
          type: "paid",
          modelName: "gpt-4o-mini",
          description: "Cost-effective vision model with good OCR performance",
          accuracy: 0.88,
          avgProcessingTime: 3000,
          costPer1kTokens: 0.00015,
          maxImageSize: 20 * 1024 * 1024,
          supportedFormats: ["jpg", "jpeg", "png", "webp"],
          rateLimit: { requests: 500, period: 60 },
          isEnabled: true,
          priority: 7,
        },
        {
          id: "gpt-4o",
          name: "GPT-4o",
          provider: "openai",
          type: "paid",
          modelName: "gpt-4o",
          description: "High-accuracy vision model for complex OCR tasks",
          accuracy: 0.94,
          avgProcessingTime: 5000,
          costPer1kTokens: 0.005,
          maxImageSize: 50 * 1024 * 1024,
          supportedFormats: ["jpg", "jpeg", "png", "webp", "tiff"],
          rateLimit: { requests: 500, period: 60 },
          isEnabled: true,
          priority: 9,
        },
      ],
      isConfigured: false,
    });

    // Local Provider
    this.providers.set("local", {
      id: "local",
      name: "Local Models",
      type: "local",
      models: [
        {
          id: "llava-1.5-7b",
          name: "LLaVA 1.5 7B",
          provider: "local",
          type: "local",
          modelName: "llava-1.5-7b",
          description: "Local multimodal model via Ollama",
          accuracy: 0.82,
          avgProcessingTime: 15000,
          maxImageSize: 10 * 1024 * 1024,
          supportedFormats: ["jpg", "jpeg", "png"],
          requirements: ["ollama", "cuda"],
          isEnabled: false,
          priority: 5,
        },
        {
          id: "qwen-vl-chat",
          name: "Qwen-VL Chat",
          provider: "local",
          type: "local",
          modelName: "qwen-vl-chat",
          description: "Local vision-language model with multilingual OCR",
          accuracy: 0.85,
          avgProcessingTime: 12000,
          maxImageSize: 15 * 1024 * 1024,
          supportedFormats: ["jpg", "jpeg", "png", "webp"],
          requirements: ["ollama", "cuda"],
          isEnabled: false,
          priority: 6,
        },
      ],
      isConfigured: false,
    });
  }

  async loadSettingsApiKey() {
    try {
      const fs = require("fs").promises;
      const path = require("path");
      const settingsPath = path.join(process.cwd(), "data", "settings.json");
      const data = await fs.readFile(settingsPath, "utf8");
      const settings = JSON.parse(data);
      return settings.openrouter?.apiKey;
    } catch (error) {
      // Settings file doesn't exist or is invalid
      return null;
    }
  }

  async autoConfigureOpenRouter() {
    // Try environment variable first, then user settings, then default config
    let openRouterApiKey = process.env.OPENROUTER_API_KEY;
    let keySource = "environment variable";

    if (!openRouterApiKey) {
      openRouterApiKey = await this.loadSettingsApiKey();
      keySource = "user settings";
    }

    if (!openRouterApiKey) {
      // Fallback to default config for testing
      openRouterApiKey = this.config.openRouter?.apiKey;
      keySource = "default configuration";
    }

    if (!openRouterApiKey) {
      console.log(
        "🔧 OCR Service: No OPENROUTER_API_KEY found in environment variables, user settings, or default configuration"
      );
      console.log(
        "🔧 OCR Service: Please set OPENROUTER_API_KEY environment variable or configure via Settings panel"
      );
      return;
    }

    console.log(
      `🔧 OCR Service: Configuring OpenRouter with API key from ${keySource}`
    );

    try {
      this.configureProvider("openrouter", { apiKey: openRouterApiKey });
      console.log(`✅ OCR Service: OpenRouter configured successfully!`);
      console.log(
        `🔑 OCR Service: API key: ${openRouterApiKey.substring(
          0,
          20
        )}...${openRouterApiKey.substring(openRouterApiKey.length - 4)}`
      );
      console.log(`🌐 OCR Service: Base URL: https://openrouter.ai/api/v1`);
      console.log(
        `📋 OCR Service: Available OpenRouter models: ${this.providers
          .get("openrouter")
          .models.map((m) => m.id)
          .join(", ")}`
      );
    } catch (error) {
      console.error(
        "❌ OCR Service: Failed to auto-configure OpenRouter:",
        error.message
      );
      console.error(
        "❌ OCR Service: This will prevent OpenRouter models from working"
      );
    }
  }

  autoConfigureOpenAI() {
    // Check for OpenAI API key
    const openAIApiKey = process.env.OPENAI_API_KEY;

    if (openAIApiKey) {
      console.log(
        `🔧 OCR Service: Configuring OpenAI with API key from environment variable`
      );

      try {
        this.configureProvider("openai", { apiKey: openAIApiKey });
        console.log(`✅ OCR Service: OpenAI configured successfully!`);
        console.log(
          `🔑 OCR Service: API key: ${openAIApiKey.substring(
            0,
            20
          )}...${openAIApiKey.substring(openAIApiKey.length - 4)}`
        );
        console.log(
          `📋 OCR Service: Available OpenAI models: ${this.providers
            .get("openai")
            .models.map((m) => m.id)
            .join(", ")}`
        );
      } catch (error) {
        console.error(
          "❌ OCR Service: Failed to auto-configure OpenAI:",
          error.message
        );
      }
    } else {
      console.log(
        `⚠️ OCR Service: OPENAI_API_KEY not found in environment, OpenAI models will not be available`
      );
    }
  }

  async configureProvider(providerId, config) {
    const provider = this.providers.get(providerId);
    if (!provider) {
      throw new Error(`Provider ${providerId} not found`);
    }

    if (providerId === "openrouter") {
      console.log(`🔧 OCR Service: Configuring OpenRouter provider...`);
      provider.apiKey = config.apiKey;
      provider.client = new OpenAI({
        baseURL: "https://openrouter.ai/api/v1",
        apiKey: config.apiKey,
        defaultHeaders: {
          "HTTP-Referer": "https://dl-organizer.local",
          "X-Title": "DL Organizer OCR Processing",
        },
      });
      console.log(
        `✅ OCR Service: OpenRouter client created with baseURL: https://openrouter.ai/api/v1`
      );
    } else if (providerId === "openai") {
      provider.apiKey = config.apiKey;
      provider.client = new OpenAI({
        apiKey: config.apiKey,
      });
    } else if (providerId === "local") {
      provider.endpoint = config.endpoint || "http://localhost:11434";
      provider.modelPath = config.modelPath;
    }

    provider.isConfigured = true;
    return provider;
  }

  async processImage(imageData, options = {}) {
    const startTime = Date.now();
    let lastError = null;

    // Select model based on preferences and availability
    const modelId = options.modelOverride || this.selectOptimalModel();
    const model = this.findModel(modelId);

    if (!model) {
      console.error(`❌ OCR Service: Model "${modelId}" not found`);

      // If user specified a model and it's not found, provide helpful error
      if (options.modelOverride) {
        const availableModels = this.getAvailableModels();
        const modelList = availableModels
          .map((m) => `${m.id} (${m.name})`)
          .join(", ");
        throw new Error(
          `Selected model "${modelId}" is not available. Available models: ${modelList}`
        );
      }

      throw new Error(`Model ${modelId} not found`);
    }

    console.log(
      `🔍 Processing image with model: ${model.id} (${
        model.name
      }) - User requested: ${options.modelOverride || "auto"}`
    );

    // Check if the provider is configured
    const provider = this.providers.get(model.provider);
    if (!provider || !provider.isConfigured) {
      console.error(
        `❌ OCR Service: Provider ${model.provider} not configured for model ${model.id}`
      );

      // If user specified this model, give clear error
      if (options.modelOverride) {
        throw new Error(
          `The selected model "${
            model.name
          }" requires ${model.provider.toUpperCase()} API configuration. Please configure the API key in settings.`
        );
      }

      throw new Error(`Provider ${model.provider} not configured`);
    }

    // Check rate limits
    if (this.config.enableRateLimit && this.isRateLimited(model)) {
      throw new Error("Rate limit exceeded. Please try again later.");
    }

    // Check cost limits
    if (this.config.enableCostTracking && this.isCostLimitExceeded()) {
      throw new Error("Monthly cost limit exceeded");
    }

    // Process with retries
    for (let attempt = 0; attempt < this.config.maxRetries; attempt++) {
      try {
        console.log(
          `🔄 Attempt ${attempt + 1}/${this.config.maxRetries} for model ${
            model.id
          }`
        );
        const result = await this.processWithModel(imageData, model, options);

        // Update cost tracking
        if (result.cost && this.config.enableCostTracking) {
          this.updateCostTracking(model.id, result.cost, true);
        }

        console.log(`✅ OCR processing successful with model ${model.id}`);
        const modelUsed = model && model.id ? model.id : "unknown-model";
        console.log(`🔍 OCR Service: Setting modelUsed to: "${modelUsed}"`);
        return {
          success: true,
          result: result.ocrResult,
          modelUsed: modelUsed,
          processingTime: Date.now() - startTime,
          cost: result.cost || 0,
          confidence: result.confidence || 0,
          retryCount: attempt,
        };
      } catch (error) {
        lastError = error;
        console.error(
          `❌ Attempt ${attempt + 1} failed with model ${model.id}:`,
          error.message
        );

        // Try fallback model if available (only if user didn't specify a model)
        if (
          attempt === this.config.maxRetries - 1 &&
          this.config.enableFallback &&
          !options.modelOverride
        ) {
          const fallbackModel = this.selectFallbackModel(model.id);
          if (fallbackModel) {
            try {
              console.log(`🔄 Trying fallback model: ${fallbackModel.id}`);
              const result = await this.processWithModel(
                imageData,
                fallbackModel,
                options
              );
              const modelUsed =
                fallbackModel && fallbackModel.id
                  ? fallbackModel.id
                  : "unknown-fallback";
              console.log(
                `🔍 OCR Service: Fallback modelUsed set to: "${modelUsed}"`
              );
              return {
                success: true,
                result: result.ocrResult,
                modelUsed: modelUsed,
                processingTime: Date.now() - startTime,
                cost: result.cost || 0,
                confidence: result.confidence || 0,
                retryCount: attempt + 1,
                note: `Original model ${
                  model?.id || "unknown"
                } failed, used fallback ${fallbackModel.id || "unknown"}`,
              };
            } catch (fallbackError) {
              lastError = fallbackError;
              console.error(
                `❌ Fallback model ${fallbackModel.id} also failed:`,
                fallbackError.message
              );
            }
          }
        } else if (
          attempt === this.config.maxRetries - 1 &&
          options.modelOverride
        ) {
          console.log(
            `⚠️ User-specified model ${model.id} failed, not trying fallback`
          );
        }

        // Wait before retry
        if (attempt < this.config.maxRetries - 1) {
          const delay = this.config.retryDelay * (attempt + 1);
          console.log(`⏳ Waiting ${delay}ms before retry...`);
          await this.sleep(delay);
        }
      }
    }

    console.error(
      `💥 All OCR attempts failed after ${this.config.maxRetries} retries`
    );

    // Provide helpful error message based on the context
    let errorMessage = lastError?.message || "Unknown error occurred";
    if (options.modelOverride && errorMessage.includes("not configured")) {
      errorMessage = `Selected model "${
        model?.name || "Unknown"
      }" failed: ${errorMessage}. Please check your API configuration.`;
    }

    const modelUsed =
      model && model.id ? model.id : modelId || "unknown-error-model";
    console.log(`🔍 OCR Service: Error case modelUsed set to: "${modelUsed}"`);

    return {
      success: false,
      error: errorMessage,
      modelUsed: modelUsed,
      modelRequested: options.modelOverride || "auto",
      processingTime: Date.now() - startTime,
      cost: 0,
      confidence: 0,
      retryCount: this.config.maxRetries,
    };
  }

  async processWithModel(imageData, model, options = {}) {
    const provider = this.providers.get(model.provider);

    if (!provider || !provider.isConfigured) {
      throw new Error(`Provider ${model.provider} not configured`);
    }

    console.log(
      `📤 Sending request to ${model.provider} with model ${model.modelName}`
    );

    // Prepare image
    const processedImage = await this.preprocessImage(imageData, model);

    if (model.provider === "local") {
      return await this.processWithLocalModel(processedImage, model, options);
    } else {
      return await this.processWithAPIModel(
        processedImage,
        model,
        provider,
        options
      );
    }
  }

  async processWithAPIModel(imageData, model, provider, options = {}) {
    const base64Image = imageData.toString("base64");
    const imageUrl = `data:image/jpeg;base64,${base64Image}`;

    // Use custom prompt if provided, otherwise build default prompt
    const prompt =
      options.customPrompt ||
      this.buildOCRPrompt(
        options.extractionType || "driver_license",
        options.mode || "us",
        options.cardSide || null
      );

    const promptType = options.customPrompt ? "custom" : "built-in";
    console.log(
      `📝 OCR API: Using ${promptType} prompt for ${
        options.extractionType || "driver_license"
      } in ${options.mode || "us"} mode`
    );
    console.log(`📋 OCR API: Full prompt being sent to ${model.modelName}:`);
    console.log("=".repeat(80));
    console.log(prompt);
    console.log("=".repeat(80));

    const requestPayload = {
      model: model.modelName,
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: prompt,
            },
            {
              type: "image_url",
              image_url: { url: imageUrl },
            },
          ],
        },
      ],
      max_tokens: 1500,
      temperature: 0.1,
      response_format: { type: "json_object" },
    };

    console.log(
      `🚀 OCR API: Sending request to ${
        provider.baseUrl || "OpenRouter"
      } with model ${model.modelName}`
    );
    console.log(`📊 OCR API: Request payload:`, {
      model: requestPayload.model,
      max_tokens: requestPayload.max_tokens,
      temperature: requestPayload.temperature,
      response_format: requestPayload.response_format,
      message_type: "text + image_url",
      image_size: `${Math.round(imageData.length / 1024)}KB`,
    });

    const requestStartTime = Date.now();
    const response = await provider.client.chat.completions.create(
      requestPayload
    );
    const requestDuration = Date.now() - requestStartTime;

    const usage = response.usage;
    const cost = this.calculateCost(usage, model);
    const responseContent = response.choices[0].message.content;

    console.log(
      `📨 OCR API: Received response from ${model.provider} in ${requestDuration}ms`
    );
    console.log(`📊 OCR API: Usage statistics:`, {
      prompt_tokens: usage?.prompt_tokens || 0,
      completion_tokens: usage?.completion_tokens || 0,
      total_tokens: usage?.total_tokens || 0,
      cost: `$${cost.toFixed(6)}`,
      duration: `${requestDuration}ms`,
    });
    console.log(`📄 OCR API: Raw response from ${model.modelName}:`);
    console.log("=".repeat(80));
    console.log(responseContent);
    console.log("=".repeat(80));

    console.log(`🔄 OCR API: Parsing response from ${model.id}...`);
    const ocrResult = this.parseOCRResponse(responseContent, model.id);

    console.log(`📋 OCR API: Detailed parsed result from ${model.id}:`, {
      success: !!ocrResult,
      dataPresent: !!ocrResult && Object.keys(ocrResult).length > 0,
      fields: ocrResult
        ? {
            firstName: ocrResult.firstName || "[EMPTY]",
            lastName: ocrResult.lastName || "[EMPTY]",
            licenseNumber: ocrResult.licenseNumber || "[EMPTY]",
            state: ocrResult.state || "[EMPTY]",
            rawText: ocrResult.rawText
              ? `[${ocrResult.rawText.length} chars]`
              : "[EMPTY]",
            confidence: ocrResult.confidence || 0,
          }
        : "[NO DATA]",
    });

    console.log(`✅ OCR API: Successfully parsed OCR result:`, {
      firstName: ocrResult.firstName || "[empty]",
      lastName: ocrResult.lastName || "[empty]",
      licenseNumber: ocrResult.licenseNumber || "[empty]",
      state: ocrResult.state || "[empty]",
      confidence: this.calculateConfidence(ocrResult),
    });

    return {
      ocrResult,
      cost,
      confidence: this.calculateConfidence(ocrResult),
      usage,
    };
  }

  async processWithLocalModel(imageData, model, options = {}) {
    // Implement local model processing via Ollama or direct Python integration
    throw new Error("Local model processing not yet implemented");
  }

  async preprocessImage(imageData, model) {
    // Optimize image for OCR
    let processedImage = sharp(imageData);

    // Resize if too large
    const metadata = await processedImage.metadata();
    if (metadata.width > 2048 || metadata.height > 2048) {
      processedImage = processedImage.resize(2048, 2048, {
        fit: "inside",
        withoutEnlargement: true,
      });
    }

    // Convert to supported format
    if (!model.supportedFormats.includes(metadata.format)) {
      processedImage = processedImage.jpeg({ quality: 90 });
    }

    // Enhance for OCR
    processedImage = processedImage.normalize().sharpen().gamma(1.2);

    return await processedImage.toBuffer();
  }

  buildOCRPrompt(
    extractionType = "driver_license",
    mode = "us",
    cardSide = null
  ) {
    const prompts = {
      driver_license:
        mode === "australian"
          ? this.buildAustralianPrompt(cardSide)
          : this.buildUSPrompt(),
      passport: this.buildPassportPrompt(),
      selfie: this.buildSelfiePrompt(),
      auto_detect: this.buildAutoDetectPrompt(),
      // Legacy support
      us_driver_license: this.buildUSPrompt(),
      australian_driver_license: this.buildAustralianPrompt(cardSide),
      // New document-specific prompts
      us_id_card: this.buildUSIDCardPrompt(),
      us_passport: this.buildUSPassportPrompt(),
      australian_id_card: this.buildAustralianIDCardPrompt(),
      australian_passport: this.buildAustralianPassportPrompt(),
    };

    return prompts[extractionType] || prompts.driver_license;
  }

  buildUSPrompt() {
    return `
      Analyze this US driver's license image and extract the following information into a JSON object.

      CRITICAL EXTRACTION PRIORITIES:
      1. SCAN FOR MULTIPLE GIVEN NAMES: Look carefully for 2-3 given names before the surname
         Examples: "Sarah Jane SMITH", "Mary Ellen Rose JOHNSON", "Michael James BROWN"
      2. IDENTIFY CARD SIDE: Determine if this is FRONT or BACK side:
         - FRONT: photo, name, address, DOB, license number, issue/expiration dates
         - BACK: restrictions, endorsements, donor info, barcodes, conditions

      {
        "firstName": "extracted first name",
        "middleName": "extracted middle name (if present)",
        "lastName": "extracted last name",
        "dateOfBirth": "YYYY-MM-DD format",
        "address": "full address",
        "licenseNumber": "license number",
        "expirationDate": "YYYY-MM-DD format",
        "issueDate": "YYYY-MM-DD format",
        "state": "two-letter state code (e.g., CA, NY, TX)",
        "cardSide": "front or back - determine which side of the license this is",
        "confidence": 0.0-1.0,
        "rawText": "all visible text from the image",
        "mode": "us"
      }

      Important notes:
      - Use exact formatting for dates (YYYY-MM-DD)
      - Extract complete address including city, state, zip code
      - Be precise with license numbers (include all digits/letters)
      - CRITICAL NAME PARSING RULES:
        * Multiple given names: "Eric Hunter LEAN" → firstName="Eric", middleName="Hunter", lastName="LEAN"
        * Two given names: "Sarah Jane SMITH" → firstName="Sarah", middleName="Jane", lastName="SMITH"
        * Three given names: "Mary Ellen Rose JOHNSON" → firstName="Mary", middleName="Ellen Rose", lastName="JOHNSON"
        * Single given name: "John SMITH" → firstName="John", middleName="", lastName="SMITH"
        * SCAN CAREFULLY for multiple first/middle names - they are commonly separated by spaces
        * ALL CAPS surnames are common on licenses - preserve exact capitalization
      - Set confidence based on text clarity and completeness (0.0-1.0)
      - Include ALL visible text in rawText field
      - If any field is unclear or missing, use empty string ""
      - State should be 2-letter code (e.g., CA, NY, TX, FL)
      - CRITICAL: Return ONLY the JSON object, no additional text or explanations
    `;
  }

  buildAustralianPrompt(cardSide = null) {
    console.log(
      "🇦🇺 ENHANCED Australian OCR prompt being built - v2.1 ACTIVE with CARD NUMBER detection!"
    );
    console.log("📝 Australian prompt parameters:", { cardSide });

    const sideInstructions =
      cardSide === "back"
        ? "This is the BACK side of an Australian driver license. Focus on extracting address information and any additional details."
        : cardSide === "front"
        ? "This is the FRONT side of an Australian driver license. Focus on personal details, license numbers, and expiration dates."
        : "Analyze this Australian driver license image. Determine if this is the front or back side based on the visible information.";

    console.log("🇦🇺 Australian prompt side instructions:", sideInstructions);

    return `
      ${sideInstructions}

      Extract the following information into a JSON object:
      {
        "surname": "family/last name",
        "givenNames": "given names (first and middle names combined)",
        "firstName": "first name only (derived from given names)",
        "middleName": "middle name(s) if present (derived from given names)",
        "lastName": "family name (same as surname)",
        "dateOfBirth": "YYYY-MM-DD format (DOB)",
        "address": "full residential address",
        "licenseNumber": "driver license number (DL #)",
        "cardNumber": "card number if present (state-dependent)",
        "expirationDate": "YYYY-MM-DD format (EXP DATE)",
        "state": "Australian state/territory (NSW, VIC, QLD, WA, SA, TAS, NT, ACT)",
        "confidence": 0.0-1.0,
        "rawText": "all visible text from the image",
        "mode": "australian",
        "cardSide": "front or back (determine based on content)"
      }

      CRITICAL FIELD DETECTION GUIDE for Australian Driver Licenses:

      **DUAL NUMBER SYSTEM - MUST DETECT BOTH:**
      1. **LICENSE NUMBER** - Look for: "Licence No.", "License No.", "DL No.", "Driver Licence No."
         Example: "Licence No. 6898DZ" → licenseNumber: "6898DZ"

      2. **CARD NUMBER** - Look for: "Card No.", "Card Number", separate numeric sequence
         This is DIFFERENT from license number - search independently!

      **NAME PARSING - FULL GIVEN NAMES:**
      3. **GIVEN NAMES**: Capture ALL first and middle names together
         Example: "Ross James BUDDEN" → givenNames: "Ross James", firstName: "Ross", middleName: "James", surname: "BUDDEN"

      **DATE CONVERSION - Australian to ISO:**
      4. **DATE OF BIRTH**: Convert "DD MMM YYYY" to "YYYY-MM-DD" format
         Example: "27 MAY 1971" → dateOfBirth: "1971-05-27"
         Example: "05 JUN 1985" → dateOfBirth: "1985-06-05"

      **OTHER CRITICAL FIELDS:**
      5. **ADDRESS**: Full residential address (street, suburb, state, postcode)
      6. **EXPIRY DATE**: Convert to YYYY-MM-DD format
      7. **STATE**: NSW, VIC, QLD, WA, SA, TAS, NT, ACT

      **ENHANCED PARSING INSTRUCTIONS:**
      - MANDATORY: Look for BOTH license number AND card number separately
      - MANDATORY: Capture complete given names - SCAN THOROUGHLY:
        * "Ross James BUDDEN" → givenNames: "Ross James", firstName: "Ross", middleName: "James", lastName: "BUDDEN"
        * "Sarah Jane Marie SMITH" → givenNames: "Sarah Jane Marie", firstName: "Sarah", middleName: "Jane Marie", lastName: "SMITH"
        * "Michael JONES" → givenNames: "Michael", firstName: "Michael", middleName: "", lastName: "JONES"
        * CRITICAL: Do NOT truncate multiple given names - capture ALL of them in givenNames field
      - MANDATORY: Convert Australian date format DD MMM YYYY to YYYY-MM-DD
      - Extract complete address with suburb, state, and postcode
      - Map surname → lastName for compatibility
      - Set confidence based on field completeness and text clarity
      - Include ALL visible text in rawText field
      - If any field is unclear or missing, use empty string ""
      - CRITICAL: Return ONLY the JSON object, no additional text or explanations
    `;
  }

  parseOCRResponse(responseText, modelId) {
    try {
      // Clean the response text in case there's extra content
      let cleanedResponse = responseText.trim();

      // Handle cases where the model might add extra text before/after JSON
      const jsonStart = cleanedResponse.indexOf("{");
      const jsonEnd = cleanedResponse.lastIndexOf("}") + 1;

      if (jsonStart !== -1 && jsonEnd !== -1) {
        cleanedResponse = cleanedResponse.substring(jsonStart, jsonEnd);
      }

      const parsed = JSON.parse(cleanedResponse);

      // Handle auto-detect mode nested structure (enhanced format)
      let ocrData = parsed;
      if (parsed.documentType && parsed.data) {
        console.log(
          `📋 Enhanced auto-detect response detected for ${modelId}:`,
          {
            documentType: parsed.documentType,
            detectionConfidence:
              parsed.detectionConfidence || parsed.confidence,
            hasData: !!parsed.data,
          }
        );

        // Extract the nested data and merge with top-level properties
        ocrData = {
          ...parsed.data,
          documentType: parsed.documentType,
          confidence: parsed.detectionConfidence || parsed.confidence || 0,
          // Preserve any additional top-level fields
          ...Object.fromEntries(
            Object.entries(parsed).filter(
              ([key]) =>
                ![
                  "data",
                  "documentType",
                  "confidence",
                  "detectionConfidence",
                ].includes(key)
            )
          ),
        };

        // Handle Australian license field mapping for auto-detect
        if (
          ocrData.mode === "australian" ||
          (ocrData.surname && ocrData.givenNames) ||
          (parsed.documentType === "driver_license" &&
            ocrData.state &&
            ["NSW", "VIC", "QLD", "WA", "SA", "TAS", "NT", "ACT"].includes(
              ocrData.state.toUpperCase()
            ))
        ) {
          console.log(
            `🇦🇺 Auto-detected Australian license format - applying field mapping`
          );

          // Ensure Australian-specific fields are properly mapped
          if (ocrData.surname && !ocrData.lastName) {
            ocrData.lastName = ocrData.surname;
          }
          // Always parse givenNames for Australian licenses to extract firstName and middleName
          if (ocrData.givenNames) {
            const names = ocrData.givenNames.trim().split(/\s+/);
            ocrData.firstName = names[0] || "";
            ocrData.middleName = names.slice(1).join(" ") || "";
            console.log(`🔍 Parsed givenNames "${ocrData.givenNames}" into:`, {
              firstName: ocrData.firstName,
              middleName: ocrData.middleName,
              totalNames: names.length,
            });
          }

          // Ensure mode is set to australian
          ocrData.mode = "australian";

          console.log(`📝 Australian field mapping applied:`, {
            surname: ocrData.surname,
            givenNames: ocrData.givenNames,
            firstName: ocrData.firstName,
            middleName: ocrData.middleName,
            lastName: ocrData.lastName,
            cardNumber: ocrData.cardNumber || "[none]",
            mode: ocrData.mode,
          });
        }

        console.log(`📋 Flattened auto-detect data for ${modelId}:`, {
          firstName: ocrData.firstName || "[EMPTY]",
          lastName: ocrData.lastName || "[EMPTY]",
          documentType: ocrData.documentType,
          confidence: ocrData.confidence,
        });
      }

      // Validate required fields and ensure all necessary fields exist
      const requiredFields = [
        "firstName",
        "middleName",
        "lastName",
        "dateOfBirth",
        "licenseNumber",
      ];
      const optionalFields = [
        "cardNumber",
        "surname",
        "givenNames",
        "mode",
        "cardSide",
        "address",
        "expirationDate",
        "issueDate",
        "state",
      ];

      // Ensure required fields exist
      for (const field of requiredFields) {
        if (!ocrData[field]) {
          ocrData[field] = "";
        }
      }

      // Ensure optional fields exist with empty string defaults
      for (const field of optionalFields) {
        if (ocrData[field] === undefined || ocrData[field] === null) {
          ocrData[field] = "";
        }
      }

      // Special handling for mode field
      if (!ocrData.mode) {
        // Auto-detect mode based on state or other indicators
        if (
          ocrData.state &&
          ["NSW", "VIC", "QLD", "WA", "SA", "TAS", "NT", "ACT"].includes(
            ocrData.state.toUpperCase()
          )
        ) {
          ocrData.mode = "australian";
        } else {
          ocrData.mode = "us";
        }
      }

      // Add metadata
      ocrData.processingTime = Date.now();
      ocrData.modelUsed = modelId;

      // Preserve raw response text if not already present
      if (!ocrData.rawText) {
        ocrData.rawText = responseText;
      }

      // 🚑 Fallback: If Australian mode and cardNumber missing, attempt extraction from rawText
      if (
        (ocrData.mode === "australian" ||
          (ocrData.state &&
            ["NSW", "VIC", "QLD", "WA", "SA", "TAS", "NT", "ACT"].includes(
              (ocrData.state || "").toUpperCase()
            ))) &&
        !ocrData.cardNumber &&
        ocrData.rawText
      ) {
        try {
          // Look for 9+ digit sequence that is NOT the licenseNumber
          const digits = ocrData.rawText.match(/\b\d{9,12}\b/g);
          if (digits) {
            const uniqueDigits = digits.filter(
              (d) => d !== (ocrData.licenseNumber || "")
            );
            if (uniqueDigits.length > 0) {
              // Heuristic: choose first unique sequence
              ocrData.cardNumber = uniqueDigits[0];
              console.log(
                "🔍 Fallback cardNumber extracted from rawText:",
                ocrData.cardNumber
              );
            }
          }
        } catch (err) {
          console.warn("Card number fallback extraction failed:", err);
        }
      }

      // Quality validation for multiple given names
      const qualityWarnings = [];

      // Check for potential multiple given names in rawText
      if (ocrData.rawText && ocrData.firstName && !ocrData.middleName) {
        const namePattern = new RegExp(
          `\\b${ocrData.firstName}\\s+([A-Z][a-z]+)\\s+${ocrData.lastName}`,
          "i"
        );
        const multiNameMatch = ocrData.rawText.match(namePattern);
        if (multiNameMatch) {
          qualityWarnings.push({
            field: "middleName",
            message: `Possible middle name detected: "${multiNameMatch[1]}" - consider manual verification`,
            severity: "warning",
          });
        }
      }

      // Australian-specific validation
      if (ocrData.mode === "australian" && ocrData.givenNames) {
        const givenNameParts = ocrData.givenNames.trim().split(/\s+/);
        if (givenNameParts.length > 1 && !ocrData.middleName) {
          qualityWarnings.push({
            field: "middleName",
            message: `Given names "${ocrData.givenNames}" appear to contain multiple names - verify middle name extraction`,
            severity: "warning",
          });
        }
      }

      // Add warnings to result
      if (qualityWarnings.length > 0) {
        ocrData.qualityWarnings = qualityWarnings;
        console.log(`⚠️ OCR Quality warnings for ${modelId}:`, qualityWarnings);
      }

      console.log(`✅ Successfully parsed OCR response from ${modelId}`, {
        hasFirstName: !!ocrData.firstName,
        hasLastName: !!ocrData.lastName,
        hasLicenseNumber: !!ocrData.licenseNumber,
        documentType: ocrData.documentType || "not specified",
        modelUsed: ocrData.modelUsed,
        qualityWarnings: qualityWarnings.length,
      });

      return ocrData;
    } catch (error) {
      console.error("❌ Failed to parse OCR response:", error);
      console.error("Raw response:", responseText);
      return {
        firstName: "",
        middleName: "",
        lastName: "",
        dateOfBirth: "",
        address: "",
        licenseNumber: "",
        cardNumber: "", // Ensure cardNumber is always included
        expirationDate: "",
        issueDate: "",
        state: "",
        surname: "", // Australian field
        givenNames: "", // Australian field
        mode: "us", // Default mode
        cardSide: "", // Card side detection
        confidence: 0.0,
        rawText: responseText,
        processingTime: Date.now(),
        modelUsed: modelId,
        parseError: error.message,
      };
    }
  }

  selectOptimalModel() {
    const availableModels = this.getAvailableModels();

    console.log(
      `🔍 OCR Service: Selecting optimal model from ${availableModels.length} available models`
    );
    console.log(
      `Available models: ${availableModels.map((m) => m.id).join(", ")}`
    );

    if (availableModels.length === 0) {
      console.error("❌ No available models configured");
      throw new Error("No available models configured");
    }

    // Try default model first
    const defaultModel = availableModels.find(
      (m) => m.id === this.config.defaultModel
    );
    if (defaultModel) {
      console.log(
        `✅ Using default model: ${defaultModel.id} (${defaultModel.name})`
      );
      return defaultModel.id;
    }

    // Sort by preferences as fallback
    const { prioritizeAccuracy, prioritizeSpeed, prioritizeCost } =
      this.config.preferences;

    const sortedModels = availableModels.sort((a, b) => {
      let scoreA = 0,
        scoreB = 0;

      if (prioritizeAccuracy) {
        scoreA += a.accuracy * 10;
        scoreB += b.accuracy * 10;
      }

      if (prioritizeSpeed) {
        scoreA += (10000 - a.avgProcessingTime) / 1000;
        scoreB += (10000 - b.avgProcessingTime) / 1000;
      }

      if (prioritizeCost) {
        const costA = a.costPer1kTokens || 0;
        const costB = b.costPer1kTokens || 0;
        scoreA += costA === 0 ? 10 : 0.01 / costA;
        scoreB += costB === 0 ? 10 : 0.01 / costB;
      }

      return scoreB - scoreA;
    });

    const selectedModel = sortedModels[0];
    console.log(
      `✅ Selected optimal model: ${selectedModel.id} (${selectedModel.name})`
    );
    return selectedModel.id;
  }

  selectFallbackModel(excludeModelId) {
    // Intelligent fallback model selection with provider-aware logic
    const fallbackChain = {
      // OpenAI models fallback to OpenRouter
      "gpt-4-vision-preview": [
        "llama-3.2-90b-vision-instruct:free",
        "llama-3.2-11b-vision-instruct:free",
      ],
      "gpt-4o": ["gpt-4-vision-preview", "llama-3.2-90b-vision-instruct:free"],

      // OpenRouter models fallback to other OpenRouter models or OpenAI
      "llama-3.2-90b-vision-instruct:free": [
        "llama-3.2-11b-vision-instruct:free",
        "gpt-4-vision-preview",
      ],
      "llama-3.2-11b-vision-instruct:free": [
        "llama-3.2-90b-vision-instruct:free",
        "gpt-4-vision-preview",
      ],

      // Local models fallback to cloud models
      "llava-v1.6-mistral-7b": [
        "llama-3.2-11b-vision-instruct:free",
        "gpt-4-vision-preview",
      ],
      "qwen2-vl-7b-instruct": [
        "llama-3.2-11b-vision-instruct:free",
        "gpt-4-vision-preview",
      ],
    };

    const fallbacks = fallbackChain[excludeModelId] || [];

    // Find first available fallback model
    for (const fallbackId of fallbacks) {
      const fallbackModel = this.findModel(fallbackId);
      if (fallbackModel && this.isModelAvailable(fallbackModel)) {
        console.log(`Using fallback model ${fallbackId} for ${excludeModelId}`);
        return fallbackModel;
      }
    }

    // If no specific fallback found, try configured fallback models
    const configuredFallbacks = (this.config.fallbackModels || [])
      .filter((id) => id !== excludeModelId)
      .map((id) => this.findModel(id))
      .filter((model) => model && this.isModelAvailable(model));

    if (configuredFallbacks.length > 0) {
      return configuredFallbacks[0];
    }

    // Last resort: find any available model
    return this.findBestAvailableModel(excludeModelId);
  }

  findBestAvailableModel(excludeModelId) {
    // Prioritize by reliability and cost
    const priorityOrder = [
      "llama-3.2-11b-vision-instruct:free", // Free and reliable
      "llama-3.2-90b-vision-instruct:free", // Free but rate limited
      "gpt-4-vision-preview", // Paid but very reliable
      "gpt-4o", // Most expensive, last resort
      "meta-llama/llama-3.2-11b-vision-instruct", // Alternative free option
    ];

    for (const modelId of priorityOrder) {
      if (modelId === excludeModelId) continue;

      const model = this.findModel(modelId);
      if (model && this.isModelAvailable(model)) {
        return model;
      }
    }

    return null;
  }

  isModelAvailable(model) {
    // Check if model is available based on configuration and limits
    if (!model.isEnabled) {
      console.log(`⚠️ Model ${model.id} is disabled`);
      return false;
    }

    // Check provider configuration
    const provider = this.providers.get(model.provider);
    if (!provider || !provider.isConfigured) {
      console.log(
        `⚠️ Provider ${model.provider} for model ${model.id} is not configured`
      );
      return false;
    }

    // Check cost limits
    if (this.config.enableCostTracking && this.costTracker) {
      const costCheck = this.costTracker.checkLimits(
        model.id,
        "single",
        model.costPerRequest || 0
      );
      if (!costCheck.allowed) return false;
    }

    // Check rate limits
    if (model.rateLimit && this.costTracker) {
      const rateCheck = this.costTracker.checkRateLimit(
        model.id,
        model.rateLimit
      );
      if (!rateCheck.allowed) return false;
    }

    return true;
  }

  getAvailableModels() {
    const models = [];
    for (const provider of this.providers.values()) {
      if (
        provider.isConfigured &&
        provider.models &&
        Array.isArray(provider.models)
      ) {
        models.push(...provider.models.filter((m) => m.isEnabled));
      }
    }
    return models.sort((a, b) => b.priority - a.priority);
  }

  findModel(modelId) {
    console.log(`🔍 OCR Service: Looking up model by ID: "${modelId}"`);

    for (const provider of this.providers.values()) {
      // First try exact ID match (OpenRouter format like 'google/gemini-flash-1.5')
      let model = provider.models.find((m) => m.id === modelId);
      if (model) {
        console.log(
          `✅ OCR Service: Found model by ID - ${model.id} (${model.name})`
        );
        return model;
      }

      // Then try by modelName (should be same as ID for OpenRouter format)
      model = provider.models.find((m) => m.modelName === modelId);
      if (model) {
        console.log(
          `✅ OCR Service: Found model by modelName - ${model.id} (${model.name}) for "${modelId}"`
        );
        return model;
      }

      // Fallback: try legacy ID mapping
      const legacyMappings = {
        "gpt-4o": "openai/gpt-4o",
        "gpt-4o-mini": "openai/gpt-4o-mini",
        "claude-3-5-sonnet": "anthropic/claude-3.5-sonnet",
        "claude-3-haiku": "anthropic/claude-3-5-haiku",
        "gemini-1.5-flash": "google/gemini-flash-1.5",
      };

      if (legacyMappings[modelId]) {
        model = provider.models.find((m) => m.id === legacyMappings[modelId]);
        if (model) {
          console.log(
            `✅ OCR Service: Found model by legacy mapping - ${modelId} → ${model.id} (${model.name})`
          );
          return model;
        }
      }
    }

    console.error(
      `❌ OCR Service: Model "${modelId}" not found in any provider`
    );
    const availableModels = [];
    for (const provider of this.providers.values()) {
      availableModels.push(
        ...provider.models.map((m) => `${m.id} (${m.modelName})`)
      );
    }
    console.error(`Available models: ${availableModels.join(", ")}`);

    return null;
  }

  calculateCost(usage, model) {
    if (!usage || !model.costPer1kTokens) return 0;

    const totalTokens =
      (usage.prompt_tokens || 0) + (usage.completion_tokens || 0);
    return (totalTokens / 1000) * model.costPer1kTokens;
  }

  calculateConfidence(ocrResult) {
    let confidence = 0;
    const fields = ["firstName", "lastName", "dateOfBirth", "licenseNumber"];

    fields.forEach((field) => {
      if (ocrResult[field] && ocrResult[field].trim()) {
        confidence += 0.25;
      }
    });

    return Math.min(confidence, 1.0);
  }

  isRateLimited(model) {
    const rateLimitKey = `${model.provider}-${model.id}`;
    const rateLimit = this.rateLimits.get(rateLimitKey);

    if (!rateLimit || !model.rateLimit) return false;

    const now = Date.now();
    const windowStart = now - model.rateLimit.period * 1000;

    // Clean old requests
    rateLimit.requests = rateLimit.requests.filter(
      (time) => time > windowStart
    );

    return rateLimit.requests.length >= model.rateLimit.requests;
  }

  isCostLimitExceeded() {
    // Ensure cost tracker is initialized
    if (
      !this.costTracker ||
      !this.costTracker.data ||
      !Array.isArray(this.costTracker.data.transactions)
    ) {
      return false; // Allow operation if cost tracker not initialized
    }

    const now = new Date();
    const monthKey = `${now.getFullYear()}-${String(
      now.getMonth() + 1
    ).padStart(2, "0")}`;
    const transactions = this.costTracker.data.transactions;
    const monthlyTransactions = transactions.filter((t) =>
      t.timestamp.startsWith(monthKey)
    );

    const monthlySpend = monthlyTransactions.reduce(
      (sum, t) => sum + t.cost,
      0
    );
    return monthlySpend >= this.config.costLimit;
  }

  updateCostTracking(modelId, cost, success) {
    // Ensure cost tracker is initialized before use
    if (
      !this.costTracker ||
      !this.costTracker.data ||
      !Array.isArray(this.costTracker.data.transactions)
    ) {
      console.warn(
        "Cost tracker not properly initialized, skipping cost tracking"
      );
      return;
    }

    const transaction = {
      timestamp: new Date().toISOString(),
      modelId,
      cost,
      success,
    };

    this.costTracker.data.transactions.push(transaction);
    if (this.costTracker.data.statistics) {
      this.costTracker.data.statistics.totalSpent =
        (this.costTracker.data.statistics.totalSpent || 0) + cost;
    }

    // Update rate limits
    const rateLimitKey = `${modelId}`;
    if (!this.rateLimits.has(rateLimitKey)) {
      this.rateLimits.set(rateLimitKey, { requests: [] });
    }
    this.rateLimits.get(rateLimitKey).requests.push(Date.now());
  }

  async sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  // Save OCR results to files
  async saveOCRResults(ocrResult, imagePath, options = {}) {
    const formats = options.formats || ["txt", "json"];
    const baseFileName = path.parse(imagePath).name;
    const outputDir = options.outputDir || path.dirname(imagePath);

    const results = {};

    for (const format of formats) {
      try {
        if (format === "txt") {
          const txtContent = this.formatOCRResultAsTxt(ocrResult);
          const txtPath = path.join(
            outputDir,
            `${baseFileName}_ocr_results.txt`
          );
          await fs.writeFile(txtPath, txtContent, "utf8");
          results.txt = txtPath;
          console.log(`💾 Saved TXT results: ${txtPath}`);
        }

        if (format === "json") {
          const jsonContent = JSON.stringify(ocrResult, null, 2);
          const jsonPath = path.join(
            outputDir,
            `${baseFileName}_ocr_results.json`
          );
          await fs.writeFile(jsonPath, jsonContent, "utf8");
          results.json = jsonPath;
          console.log(`💾 Saved JSON results: ${jsonPath}`);
        }
      } catch (error) {
        console.error(`❌ Failed to save ${format} results:`, error);
      }
    }

    return results;
  }

  formatOCRResultAsTxt(ocrResult) {
    const timestamp = new Date().toISOString();
    const mode = ocrResult.mode || "us";

    if (mode === "australian") {
      return `Driver's License OCR Results (Australian)
=====================================
Extracted: ${timestamp}
Model Used: ${ocrResult.modelUsed || "Unknown"}
Document Type: ${ocrResult.documentType || "Australian DL"}
Confidence: ${Math.round((ocrResult.confidence || 0) * 100)}%

Personal Information:
- Surname: ${ocrResult.surname || ocrResult.lastName || ""}
- Given Names: ${ocrResult.givenNames || ""}
- First Name: ${ocrResult.firstName || ""}
- Middle Name: ${ocrResult.middleName || ""}
- Date of Birth: ${ocrResult.dateOfBirth || ""}

License Information:
- Driver License Number: ${ocrResult.licenseNumber || ""}
- Card Number: ${ocrResult.cardNumber || ""}
- State/Territory: ${ocrResult.state || ""}
- Expiration Date: ${ocrResult.expirationDate || ""}
- Card Side: ${ocrResult.cardSide || "Unknown"}

Address:
${ocrResult.address || "Not provided"}

${
  ocrResult.otherNotes ? `Additional Notes:\n${ocrResult.otherNotes}\n\n` : ""
}Raw Extracted Text:
==================
${ocrResult.rawText || "No raw text available"}

Processing Details:
- Processing Time: ${ocrResult.processingTime || "Unknown"}ms
- Cost: $${(ocrResult.cost || 0).toFixed(4)}
`;
    } else {
      return `Driver's License OCR Results (US)
==================================
Extracted: ${timestamp}
Model Used: ${ocrResult.modelUsed || "Unknown"}
Document Type: ${ocrResult.documentType || "USA DL"}
Confidence: ${Math.round((ocrResult.confidence || 0) * 100)}%

Personal Information:
- First Name: ${ocrResult.firstName || ""}
- Middle Name: ${ocrResult.middleName || ""}
- Last Name: ${ocrResult.lastName || ""}
- Date of Birth: ${ocrResult.dateOfBirth || ""}

License Information:
- License Number: ${ocrResult.licenseNumber || ""}
- State: ${ocrResult.state || ""}
- Issue Date: ${ocrResult.issueDate || ""}
- Expiration Date: ${ocrResult.expirationDate || ""}

Address:
${ocrResult.address || "Not provided"}

${
  ocrResult.otherNotes ? `Additional Notes:\n${ocrResult.otherNotes}\n\n` : ""
}Raw Extracted Text:
==================
${ocrResult.rawText || "No raw text available"}

Processing Details:
- Processing Time: ${ocrResult.processingTime || "Unknown"}ms
- Cost: $${(ocrResult.cost || 0).toFixed(4)}
`;
    }
  }

  // Test API connection
  async testConnection(modelId = null) {
    const testModel = modelId
      ? this.findModel(modelId)
      : this.findModel(this.selectOptimalModel());

    if (!testModel) {
      throw new Error("No test model available");
    }

    console.log(`🧪 Testing connection with model: ${testModel.id}`);

    // Create a simple test image (1x1 white pixel)
    const testImageBuffer = await sharp({
      create: {
        width: 1,
        height: 1,
        channels: 3,
        background: { r: 255, g: 255, b: 255 },
      },
    })
      .jpeg()
      .toBuffer();

    try {
      const result = await this.processImage(testImageBuffer, {
        extractionType: "auto_detect",
        modelOverride: testModel.id,
      });

      return {
        success: true,
        model: testModel.id,
        provider: testModel.provider,
        responseTime: result.processingTime,
        cost: result.cost,
      };
    } catch (error) {
      return {
        success: false,
        model: testModel.id,
        provider: testModel.provider,
        error: error.message,
      };
    }
  }

  getStats() {
    return this.costTracker.getStatistics();
  }

  getProviders() {
    return Array.from(this.providers.values());
  }

  getModels() {
    return this.getAvailableModels();
  }

  buildAutoDetectPrompt() {
    return `
### Step 1: Auto-Detect the Document Type
First, carefully examine the input to determine the document type. Use these criteria (prioritize clear indicators; if ambiguous, default to "Unknown" and explain why):

**🔴 PRIMARY INDICATORS (Definitive - 95%+ confidence)**
1. **Issue Date field present** → **USA Driver's License**
   - AUS DLs NEVER have issue dates - this is USA-specific
2. **Card Number field present** → **Australian Driver's License**
   - USA DLs NEVER have card numbers - this is AUS-specific

**🟡 SECONDARY INDICATORS (Strong supporting evidence)**
- **State Identifiers**:
  - USA: CA, TX, FL, NY, IL, PA, OH, etc. (US state abbreviations)
  - AUS: NSW, VIC, QLD, SA, WA, TAS, ACT, NT (Australian states)
- **Address Format**:
  - USA: ZIP codes (5 digits: 12345 or 9 digits: 12345-6789)
  - AUS: Postcodes (4 digits: 1234)
- **Date Field Labels**:
  - USA: "Expiration Date" or "EXP"
  - AUS: "Expiry Date" or similar
- **Name Field Labels**:
  - USA: "First Name", "Middle Name", "Last Name"
  - AUS: "Surname", "Given Names"

**🟢 TERTIARY INDICATORS (If still ambiguous)**
- License class format patterns
- Physical description field structure
- Overall document layout

**If ambiguous after all checks**: Default to "Unknown" and explain conflicting indicators.

### Step 2: Extract Fields Based on Detected Type

Return a JSON object with this exact structure. Always include ALL fields (even if blank). Use **empty string ""** for non-applicable or missing fields:

{
  "documentType": "USA DL|AUS DL|USA ID|AUS ID|Passport|Unknown",
  "detectionConfidence": 0.95,
  "data": {
    // UNIVERSAL FIELDS (All Documents)
    "firstName": "primary given name only",
    "middleName": "middle name(s) - comma-separated if multiple",
    "lastName": "complete surname (including suffixes like Jr., Sr.)",
    "dateOfBirth": "YYYY-MM-DD format",
    "address": "complete address string",
    "rawText": "all visible text from the image",
    "mode": "us|australian|passport|unknown",

    // DOCUMENT-SPECIFIC FIELDS
    "licenseNumber": "license/DL number (leave blank for passports/selfies)",
    "cardNumber": "card number (AUS DL only - leave blank for USA)",
    "issueDate": "YYYY-MM-DD (USA DL/ID only - leave blank for AUS)",
    "expirationDate": "YYYY-MM-DD format",
    "state": "state/territory abbreviation",
    "cardSide": "front|back|unknown",

    // AUSTRALIAN-SPECIFIC FIELDS
    "surname": "family/last name (AUS format - leave blank for USA)",
    "givenNames": "ALL given names as complete string (AUS format - leave blank for USA)",

    // PASSPORT-SPECIFIC FIELDS
    "passportNumber": "passport number (passports only)",
    "nationality": "nationality (passports only)",
    "placeOfBirth": "place of birth (passports only)",

    // QUALITY ASSURANCE
    "confidence": 0.0-1.0,
    "otherNotes": "extraction issues, ambiguities, or special observations"
  }
}

### Step 3: CRITICAL Field Extraction Rules

**NAME PARSING (Document-Type Specific):**

**For USA DL/ID (mode: "us"):**
- **firstName**: Primary given name only
- **middleName**: All middle names (comma-separated if multiple)
- **lastName**: Complete surname (including suffixes like Jr., Sr.)
- **givenNames**: Leave BLANK (not applicable for USA)
- **surname**: Leave BLANK (not applicable for USA)
- Example: "Eric Hunter LEAN Jr." → firstName="Eric", middleName="Hunter", lastName="LEAN Jr.", givenNames="", surname=""

**For AUS DL/ID (mode: "australian"):**
- **firstName**: First given name only (from "Given Names" field)
- **middleName**: Remaining given names after first (comma-separated)
- **lastName**: Surname field content
- **givenNames**: ALL given names as complete string (e.g., "Eric Hunter")
- **surname**: Same as lastName for compatibility
- Example: "Given Names: Eric Hunter, Surname: LEAN" → firstName="Eric", middleName="Hunter", lastName="LEAN", givenNames="Eric Hunter", surname="LEAN"

**DOCUMENT-SPECIFIC REQUIREMENTS:**

**USA DL/ID Only:**
- **issueDate**: REQUIRED (format: YYYY-MM-DD)
- **cardNumber**: Leave BLANK
- **mode**: "us"

**AUS DL/ID Only:**
- **issueDate**: Leave BLANK (AUS doesn't have this)
- **cardNumber**: Extract if present (state-dependent)
- **mode**: "australian"
- **Dual Number System**: Look for BOTH "Licence No." AND "Card No." separately

**Date Conversion:**
- Australian format "DD MMM YYYY" → "YYYY-MM-DD"
- Example: "27 MAY 1971" → "1971-05-27"

**Quality Assurance:**
- Set **confidence** based on field completeness and text clarity
- Use **otherNotes** for ambiguities or extraction warnings
- Always populate **rawText** with ALL visible text

**CRITICAL: Return ONLY the JSON object, no additional text or explanations**
    `;
  }

  buildPassportPrompt() {
    return `
      Analyze this passport image and extract the following information into a JSON object:
      {
        "firstName": "extracted first name",
        "lastName": "extracted last name",
        "dateOfBirth": "YYYY-MM-DD format",
        "placeOfBirth": "place of birth",
        "passportNumber": "passport number",
        "nationality": "nationality",
        "country": "issuing country",
        "issueDate": "YYYY-MM-DD format",
        "expirationDate": "YYYY-MM-DD format",
        "gender": "M or F",
        "documentType": "passport",
        "extractedText": "all visible text from the document"
      }

      If any field cannot be clearly read, set it to null.
      Return only the JSON object, no additional text.
    `;
  }

  buildSelfiePrompt() {
    return `
      Analyze this selfie/photo image and provide a description of the person shown.

      Return a JSON object with this structure:
      {
        "documentType": "selfie",
        "description": "1-3 sentence description of the person in the image, including physical appearance, clothing, and setting",
        "gender": "apparent gender (male/female/other)",
        "estimatedAge": "estimated age range (e.g., '20-25', '30-35')",
        "confidence": 0.95,
        "details": {
          "hairColor": "hair color if visible",
          "clothing": "description of clothing",
          "accessories": "glasses, jewelry, etc. if visible",
          "setting": "background/setting if discernible"
        }
      }

      Focus on providing a respectful, factual description suitable for identification purposes.
      Return only the JSON object, no additional text.
    `;
  }

  buildUSIDCardPrompt() {
    return `
      Analyze this US ID card image and extract the following information into a JSON object.

      {
        "firstName": "extracted first name",
        "middleName": "extracted middle name (if present)",
        "lastName": "extracted last name",
        "dateOfBirth": "YYYY-MM-DD format",
        "address": "full address (if present)",
        "idNumber": "ID number",
        "expirationDate": "YYYY-MM-DD format",
        "issueDate": "YYYY-MM-DD format",
        "state": "two-letter state code (e.g., CA, NY, TX)",
        "cardSide": "front or back - determine which side of the ID this is",
        "confidence": 0.0-1.0,
        "rawText": "all visible text from the image",
        "mode": "us"
      }

      Important notes:
      - Use exact formatting for dates (YYYY-MM-DD)
      - Extract complete address if present
      - Be precise with ID numbers (include all digits/letters)
      - Set confidence based on text clarity and completeness (0.0-1.0)
      - Include ALL visible text in rawText field
      - If any field is unclear or missing, use empty string ""
      - State should be 2-letter code (e.g., CA, NY, TX, FL)
      - CRITICAL: Return ONLY the JSON object, no additional text or explanations
    `;
  }

  buildUSPassportPrompt() {
    return `
      Analyze this US passport image and extract the following information into a JSON object.

      {
        "firstName": "extracted first name",
        "middleName": "extracted middle name (if present)",
        "lastName": "extracted last name",
        "dateOfBirth": "YYYY-MM-DD format",
        "placeOfBirth": "place of birth",
        "passportNumber": "passport number",
        "nationality": "nationality (USA/United States)",
        "expirationDate": "YYYY-MM-DD format",
        "issueDate": "YYYY-MM-DD format",
        "issuingAuthority": "issuing authority",
        "confidence": 0.0-1.0,
        "rawText": "all visible text from the image",
        "mode": "us"
      }

      Important notes:
      - Use exact formatting for dates (YYYY-MM-DD)
      - Extract all personal identification information
      - Be precise with passport numbers (include all characters)
      - Set confidence based on text clarity and completeness (0.0-1.0)
      - Include ALL visible text in rawText field
      - If any field is unclear or missing, use empty string ""
      - CRITICAL: Return ONLY the JSON object, no additional text or explanations
    `;
  }

  buildAustralianIDCardPrompt() {
    return `
      Analyze this Australian ID card image and extract the following information into a JSON object.

      {
        "surname": "family/last name",
        "givenNames": "given names (first and middle names combined)",
        "firstName": "first name only (derived from given names)",
        "middleName": "middle name(s) if present (derived from given names)",
        "lastName": "family name (same as surname)",
        "dateOfBirth": "YYYY-MM-DD format",
        "address": "full residential address",
        "idNumber": "ID card number",
        "cardNumber": "card number if present (state-dependent)",
        "expirationDate": "YYYY-MM-DD format",
        "state": "Australian state/territory (NSW, VIC, QLD, WA, SA, TAS, NT, ACT)",
        "confidence": 0.0-1.0,
        "rawText": "all visible text from the image",
        "mode": "australian",
        "cardSide": "front or back (determine based on content)"
      }

      Australian ID card field guide:
      - Extract all personal identification information
      - Note: Australian ID cards do NOT typically have issue dates
      - They have CARD NUMBERS instead of issue dates
      - Split given names PROPERLY: If given names are "Eric Hunter", then firstName="Eric" and middleName="Hunter"
      - Map surname → lastName for compatibility
      - Use exact formatting for dates (YYYY-MM-DD)
      - Include ALL visible text in rawText field
      - State should be NSW, VIC, QLD, WA, SA, TAS, NT, or ACT
      - CRITICAL: Return ONLY the JSON object, no additional text or explanations
    `;
  }

  buildAustralianPassportPrompt() {
    return `
      Analyze this Australian passport image and extract the following information into a JSON object.

      {
        "surname": "family/last name",
        "givenNames": "given names (first and middle names combined)",
        "firstName": "first name only (derived from given names)",
        "middleName": "middle name(s) if present (derived from given names)",
        "lastName": "family name (same as surname)",
        "dateOfBirth": "YYYY-MM-DD format",
        "placeOfBirth": "place of birth",
        "passportNumber": "passport number",
        "nationality": "nationality (Australian)",
        "expirationDate": "YYYY-MM-DD format",
        "issueDate": "YYYY-MM-DD format",
        "issuingAuthority": "issuing authority",
        "confidence": 0.0-1.0,
        "rawText": "all visible text from the image",
        "mode": "australian"
      }

      Important notes:
      - Use exact formatting for dates (YYYY-MM-DD)
      - Split given names PROPERLY: If given names are "Eric Hunter", then firstName="Eric" and middleName="Hunter"
      - Map surname → lastName for compatibility
      - Extract all passport identification information
      - Be precise with passport numbers (include all characters)
      - Set confidence based on text clarity and completeness (0.0-1.0)
      - Include ALL visible text in rawText field
      - If any field is unclear or missing, use empty string ""
      - CRITICAL: Return ONLY the JSON object, no additional text or explanations
    `;
  }
}

module.exports = OCRService;
