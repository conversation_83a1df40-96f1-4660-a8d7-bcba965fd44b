import { ImageFile } from '@/types';
import { Filter, FilterStack } from '@/types/filters';

// Performance optimization: pre-compute expensive operations
const imageCache = new Map<string, {
  sizeKB: number;
  dimensions: string;
  lowerFilename: string;
}>();

function getImageCacheEntry(image: ImageFile) {
  if (!imageCache.has(image.id)) {
    imageCache.set(image.id, {
      sizeKB: Math.round(image.fileSize / 1024),
      dimensions: `${image.width}x${image.height}`,
      lowerFilename: image.filename.toLowerCase()
    });
  }
  return imageCache.get(image.id)!;
}

// Efficient filter pipeline that applies filters in sequence
export const applyFilters = (
  images: ImageFile[],
  filters: FilterStack,
  hasCacheMap: Map<string, boolean>,
  sideCache?: Map<string, 'front' | 'back' | 'selfie' | 'unknown'>
): ImageFile[] => {
  if (filters.length === 0) return images;

  let result = images;

  for (const filter of filters) {
    switch (filter.kind) {
      case 'size-min':
        result = result.filter(img => {
          const cached = getImageCacheEntry(img);
          return cached.sizeKB >= filter.kb;
        });
        break;
      
      case 'size-max':
        result = result.filter(img => {
          const cached = getImageCacheEntry(img);
          return cached.sizeKB <= filter.kb;
        });
        break;
      
      case 'resolution-min':
        result = result.filter(img => img.width >= filter.w && img.height >= filter.h);
        break;
      
      case 'resolution-max':
        result = result.filter(img => img.width <= filter.w && img.height <= filter.h);
        break;
      
      case 'resolution-exact':
        result = result.filter(img => img.width === filter.w && img.height === filter.h);
        break;
      
      case 'filename-contains':
        const searchText = filter.caseSensitive ? filter.text : filter.text.toLowerCase();
        result = result.filter(img => {
          const cached = getImageCacheEntry(img);
          const filename = filter.caseSensitive ? img.filename : cached.lowerFilename;
          return filename.includes(searchText);
        });
        break;
      
      case 'cached':
        result = result.filter(img => hasCacheMap.get(img.id) === filter.value);
        break;
      
      case 'side':
        if (sideCache) {
          result = result.filter(img => sideCache.get(img.id) === filter.value);
        }
        break;
      
      case 'size-range':
        result = result.filter(img => {
          const cached = getImageCacheEntry(img);
          return matchesSizeRange(cached.sizeKB, filter.range);
        });
        break;
      
      case 'resolution-range':
        result = result.filter(img => {
          return matchesResolutionRange(img.width, img.height, filter.range);
        });
        break;

      case 'select-visible':
        // This filter acts as a pass-through and does not modify the list
        break;

      default:
        // TypeScript exhaustiveness check will catch missing cases
        const _exhaustiveCheck: never = filter;
        break;
    }
  }

  return result;
};

// Cache cleanup function to prevent memory leaks
export const clearImageFilterCache = () => {
  imageCache.clear();
};

// Cache size monitoring for debugging
export const getImageFilterCacheSize = () => imageCache.size;

// Helper to get filter statistics for display
export const getFilterStats = (
  originalCount: number,
  filteredCount: number,
  filters: FilterStack
): { 
  originalCount: number;
  filteredCount: number;
  filtersActive: number;
  reductionPercentage: number;
} => {
  const reductionPercentage = originalCount > 0 
    ? Math.round(((originalCount - filteredCount) / originalCount) * 100)
    : 0;

  return {
    originalCount,
    filteredCount,
    filtersActive: filters.length,
    reductionPercentage
  };
};

// Helper to create quick filter presets
export const createQuickFilters = {
  unprocessed: (): Filter => ({ kind: 'cached', value: false }),
  processed: (): Filter => ({ kind: 'cached', value: true }),
  minSize: (kb: number): Filter => ({ kind: 'size-min', kb }),
  maxSize: (kb: number): Filter => ({ kind: 'size-max', kb }),
  exactResolution: (w: number, h: number): Filter => ({ kind: 'resolution-exact', w, h }),
  minResolution: (w: number, h: number): Filter => ({ kind: 'resolution-min', w, h }),
  maxResolution: (w: number, h: number): Filter => ({ kind: 'resolution-max', w, h }),
  filenameContains: (text: string, caseSensitive = false): Filter => ({ 
    kind: 'filename-contains', 
    text, 
    caseSensitive 
  })
};

// Common filter combinations for quick access
export const commonFilterSets = {
  largeImages: [
    createQuickFilters.minSize(500),
    createQuickFilters.minResolution(1000, 1000)
  ],
  smallImages: [
    createQuickFilters.maxSize(100)
  ],
  highResolution: [
    createQuickFilters.minResolution(1920, 1080)
  ],
  unprocessedLarge: [
    createQuickFilters.unprocessed(),
    createQuickFilters.minSize(300)
  ]
};

// Helper functions for smart filters
function matchesSizeRange(sizeKB: number, range: string): boolean {
  switch (range) {
    case '< 100KB':
      return sizeKB < 100;
    case '100KB - 300KB':
      return sizeKB >= 100 && sizeKB < 300;
    case '300KB - 500KB':
      return sizeKB >= 300 && sizeKB < 500;
    case '500KB - 1MB':
      return sizeKB >= 500 && sizeKB < 1024;
    case '1MB - 2MB':
      return sizeKB >= 1024 && sizeKB < 2048;
    case '2MB - 5MB':
      return sizeKB >= 2048 && sizeKB < 5120;
    case '> 5MB':
      return sizeKB >= 5120;
    default:
      return false;
  }
}

function matchesResolutionRange(width: number, height: number, range: string): boolean {
  const megapixels = (width * height) / (1024 * 1024);
  
  switch (range) {
    case '< 1MP':
      return megapixels < 1;
    case '1-2MP':
      return megapixels >= 1 && megapixels < 2;
    case '2-5MP':
      return megapixels >= 2 && megapixels < 5;
    case '5-8MP':
      return megapixels >= 5 && megapixels < 8;
    case '8-12MP':
      return megapixels >= 8 && megapixels < 12;
    case '> 12MP':
      return megapixels >= 12;
    default:
      return false;
  }
}

// Smart filter creators
export const createSmartFilters = {
  ...createQuickFilters,
  side: (value: 'front' | 'back' | 'selfie' | 'unknown'): Filter => ({ kind: 'side', value }),
  sizeRange: (range: string): Filter => ({ kind: 'size-range', range }),
  resolutionRange: (range: string): Filter => ({ kind: 'resolution-range', range })
};