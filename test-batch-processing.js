#!/usr/bin/env node

/**
 * Test script for batch processing with ReadySearch integration
 * This script tests the batch OCR + ReadySearch functionality
 */

const fs = require('fs');
const path = require('path');

// Mock test data for batch processing
const testImages = [
  {
    imageId: Buffer.from('./files/test1.jpg').toString('base64'),
    filename: 'test1.jpg',
    path: './files/test1.jpg'
  },
  {
    imageId: Buffer.from('./files/test2.jpg').toString('base64'),
    filename: 'test2.jpg', 
    path: './files/test2.jpg'
  },
  {
    imageId: Buffer.from('./files/test3.jpg').toString('base64'),
    filename: 'test3.jpg',
    path: './files/test3.jpg'
  },
  {
    imageId: Buffer.from('./files/test4.jpg').toString('base64'),
    filename: 'test4.jpg',
    path: './files/test4.jpg'
  }
];

const testPayload = {
  images: testImages,
  modelId: "gpt-4o-mini",
  extractionType: "aus_driver_license",
  mode: "australian",
  exportFormats: ["json", "txt"],
  readySearchOptions: {
    enabled: true,
    useFullGivenNames: false,
    includeBirthYear: true,
  }
};

async function testBatchProcessing() {
  console.log('🧪 Testing Batch Processing with ReadySearch Integration');
  console.log('=' .repeat(60));
  
  try {
    // Test 1: Check if backend is running
    console.log('1. Checking backend server...');
    const healthResponse = await fetch('http://localhost:3003/health');
    if (!healthResponse.ok) {
      throw new Error('Backend server not responding');
    }
    console.log('✅ Backend server is running');
    
    // Test 2: Test batch processing endpoint
    console.log('\n2. Testing batch processing endpoint...');
    const response = await fetch('http://localhost:3003/api/batch-ocr/process-images', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Batch processing failed: ${response.status} - ${errorText}`);
    }
    
    const result = await response.json();
    console.log('✅ Batch processing completed successfully');
    
    // Test 3: Analyze results
    console.log('\n3. Analyzing results...');
    console.log(`📊 Processing Stats:`);
    console.log(`   - Total images: ${result.stats.processedImages}`);
    console.log(`   - Successful OCR: ${result.stats.successfulExtractions}`);
    console.log(`   - Failed OCR: ${result.stats.failedExtractions}`);
    
    if (result.readySearchStats) {
      console.log(`   - ReadySearch processed: ${result.readySearchStats.processed}`);
      console.log(`   - ReadySearch failed: ${result.readySearchStats.failed}`);
      console.log(`   - ReadySearch skipped: ${result.readySearchStats.skipped}`);
    }
    
    // Test 4: Check individual results
    console.log('\n4. Individual results:');
    result.results.forEach((item, index) => {
      console.log(`   Image ${index + 1} (${item.filename}):`);
      console.log(`     - OCR Success: ${item.success ? '✅' : '❌'}`);
      if (item.success && item.data) {
        console.log(`     - Name: ${item.data.firstName} ${item.data.lastName}`);
        console.log(`     - DOB: ${item.data.dateOfBirth || 'N/A'}`);
      }
      if (item.readySearchSuccess !== undefined) {
        console.log(`     - ReadySearch: ${item.readySearchSuccess ? '✅' : '❌'}`);
        if (item.readySearchData) {
          console.log(`     - RS Query: ${item.readySearchData.searchQuery}`);
          console.log(`     - RS Results: ${item.readySearchData.results ? 'Found' : 'None'}`);
        }
      }
      console.log('');
    });
    
    // Test 5: Check file outputs
    console.log('5. Checking saved files...');
    const outputDir = path.join(__dirname, 'data', 'ocr-results');
    if (fs.existsSync(outputDir)) {
      const files = fs.readdirSync(outputDir);
      const jsonFiles = files.filter(f => f.endsWith('.json'));
      const txtFiles = files.filter(f => f.endsWith('.txt'));
      
      console.log(`   - JSON files: ${jsonFiles.length}`);
      console.log(`   - TXT files: ${txtFiles.length}`);
      
      // Show sample file content
      if (jsonFiles.length > 0) {
        const sampleFile = path.join(outputDir, jsonFiles[0]);
        const content = JSON.parse(fs.readFileSync(sampleFile, 'utf8'));
        console.log(`   - Sample JSON content keys: ${Object.keys(content).join(', ')}`);
      }
    } else {
      console.log('   ⚠️ Output directory not found');
    }
    
    console.log('\n🎉 Batch processing test completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testBatchProcessing();
}

module.exports = { testBatchProcessing, testPayload };
