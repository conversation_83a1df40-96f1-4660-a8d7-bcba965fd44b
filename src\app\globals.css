@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&family=JetBrains+Mono:ital,wght@0,400;0,500;0,700;1,400;1,500;1,700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }

  .gray {
    --background: 220 13% 13%;
    --foreground: 220 9% 92%;
    --card: 220 13% 18%;
    --card-foreground: 220 9% 95%;
    --popover: 220 13% 18%;
    --popover-foreground: 220 9% 95%;
    --primary: 220 9% 80%;
    --primary-foreground: 220 13% 13%;
    --secondary: 220 13% 23%;
    --secondary-foreground: 220 9% 92%;
    --muted: 220 13% 23%;
    --muted-foreground: 220 9% 72%;
    --accent: 220 13% 25%;
    --accent-foreground: 220 9% 95%;
    --destructive: 0 62.8% 50%;
    --destructive-foreground: 220 9% 95%;
    --border: 220 13% 28%;
    --input: 220 13% 28%;
    --ring: 220 9% 70%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground antialiased;
    font-family: 'Roboto', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
    font-weight: 400;
    letter-spacing: 0.02em;
    line-height: 1.6;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-medium tracking-tight;
  }
  
  button {
    @apply font-medium;
  }
  
  /* Improve card contrast */
  .card {
    @apply shadow-sm;
  }
  
  /* Ensure proper contrast for all text */
  .text-muted-foreground {
    @apply opacity-90;
  }
  
  /* ReadySearch Panel Styling - Maximum Contrast Text */
  .readysearch-panel {
    @apply bg-card border border-border;
    color: hsl(var(--foreground)) !important;
  }
  
  /* High-contrast base text color for ReadySearch, while still allowing utility text-color classes (e.g. text-red-600) to win */
  .readysearch-panel {
    color: hsl(var(--foreground));
  }

  /* Preserve the intended colours of utility classes by NOT overriding their colour */
  .readysearch-panel button {
    color: inherit;
  }
  
  /* ReadySearch specific button styling */
  .readysearch-panel .btn-outline {
    @apply bg-background hover:bg-accent hover:text-accent-foreground;
  }
  
  /* JSON output area styling */
  .readysearch-panel pre {
    @apply font-mono text-sm leading-relaxed;
    word-wrap: break-word;
    white-space: pre-wrap;
  }
  
  /* Scrollbar styling for JSON output */
  .readysearch-panel .scroll-area {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--border)) transparent;
  }
  
  .readysearch-panel .scroll-area::-webkit-scrollbar {
    width: 8px;
  }
  
  .readysearch-panel .scroll-area::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .readysearch-panel .scroll-area::-webkit-scrollbar-thumb {
    background-color: hsl(var(--border));
    border-radius: 4px;
  }
  
  .readysearch-panel .scroll-area::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground));
  }
}

/* ========================================
   MODERN STYLING GUIDELINES & COMPONENTS
   ======================================== */

/* High-Contrast Code Bubbles */
.code-bubble {
  @apply font-mono text-sm px-3 py-1.5 rounded-md border;
  background: hsl(var(--card));
  color: hsl(var(--card-foreground));
  border-color: hsl(var(--border));
  font-weight: 500;
}

.code-bubble-dark {
  @apply font-mono text-sm px-3 py-1.5 rounded-md;
  background: hsl(var(--foreground));
  color: hsl(var(--background));
  font-weight: 500;
}

/* Tier Badge Styling */
.badge-free {
  @apply bg-green-100 text-green-800 border-green-200;
}

.badge-paid {
  @apply bg-orange-100 text-orange-800 border-orange-200;
}

.badge-premium {
  @apply bg-purple-100 text-purple-800 border-purple-200;
}

/* Info Panel with Better Contrast */
.info-panel {
  @apply p-4 rounded-lg border;
  background: hsl(var(--card));
  border-color: hsl(var(--border));
  color: hsl(var(--card-foreground));
}

.info-panel-blue {
  @apply bg-blue-50 border-blue-200 text-blue-900;
}

.info-panel-green {
  @apply bg-green-50 border-green-200 text-green-900;
}

.info-panel-orange {
  @apply bg-orange-50 border-orange-200 text-orange-900;
}

/* Model Cards with Modern Styling */
.model-card {
  @apply p-4 border rounded-lg bg-card text-card-foreground hover:bg-accent hover:text-accent-foreground transition-colors;
  border-color: hsl(var(--border));
}

.model-card-selected {
  @apply ring-2 ring-primary ring-offset-2;
}

/* Benefits List Styling */
.benefits-list {
  @apply space-y-2;
}

.benefits-item {
  @apply flex items-start gap-2 text-sm;
}

.benefits-bullet {
  @apply w-1.5 h-1.5 rounded-full bg-current mt-2 flex-shrink-0;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/20 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/30;
}

/* Custom focus styles */
.focus-visible:focus {
  @apply outline-none ring-2 ring-ring ring-offset-2;
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Image grid responsive styles */
.image-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fill, minmax(var(--grid-size, 200px), 1fr));
}

/* Hover preview styles */
.hover-preview {
  position: fixed;
  z-index: 9999;
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  padding: 8px;
  transition: opacity 0.2s ease;
  opacity: 0;
  pointer-events: none;
  color: hsl(var(--card-foreground));
}

.hover-preview.visible {
  opacity: 1;
}

.hover-preview img {
  max-width: min(400px, 80vw);
  max-height: min(300px, 80vh);
  border-radius: 4px;
  transform: scale(2.0);
  transform-origin: center;
}

/* Loading animation */
.loading-spinner {
  border: 2px solid hsl(var(--muted));
  border-top: 2px solid hsl(var(--primary));
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* File input styles */
.file-input {
  position: relative;
  overflow: hidden;
  display: inline-block;
}

.file-input input[type=file] {
  position: absolute;
  left: -9999px;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}

@media (max-width: 480px) {
  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }
}