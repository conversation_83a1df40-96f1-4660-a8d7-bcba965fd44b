import { NextRequest, NextResponse } from 'next/server'

const BACKEND_URL = process.env.BACKEND_PORT 
  ? `http://127.0.0.1:${process.env.BACKEND_PORT}` 
  : 'http://127.0.0.1:3003'

export async function GET(request: NextRequest) {
  try {
    console.log('OCR Prompts API Route: Forwarding request to backend', `${BACKEND_URL}/api/ocr/prompts`)
    
    // Forward the request to the backend
    const response = await fetch(`${BACKEND_URL}/api/ocr/prompts`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': request.headers.get('User-Agent') || '',
      },
    })

    // Get response data
    const data = await response.text()
    
    console.log('OCR Prompts API Route: Backend response status:', response.status)
    
    // Return response with same status and headers
    return new NextResponse(data, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        'Content-Type': 'application/json',
      },
    })
    
  } catch (error) {
    console.error('API Proxy Error (ocr/prompts):', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Proxy error: Failed to forward OCR prompts request to backend',
        details: error instanceof Error ? error.message : 'Unknown error',
        backendUrl: BACKEND_URL
      }, 
      { status: 500 }
    )
  }
}