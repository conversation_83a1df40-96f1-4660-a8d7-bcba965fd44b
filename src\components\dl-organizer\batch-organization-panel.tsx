"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  FolderOpen, 
  Zap, 
  FileText, 
  CheckCircle2, 
  AlertCircle,
  Loader2,
  Users,
  Calendar,
  MapPin,
  Archive,
  Play,
  Pause,
  Square
} from 'lucide-react'
import { ImageFile, OCRResult } from '@/types'
import { cn } from '@/lib/utils'

interface BatchOrganizationPanelProps {
  sourceFolder: string
  images: ImageFile[]
  onOrganize: (organizationPlan: OrganizationPlan) => Promise<boolean>
  className?: string
  countryMode: 'us' | 'australian'
}

interface OrganizationRule {
  id: string
  name: string
  pattern: string
  description: string
  enabled: boolean
}

interface OrganizationPlan {
  sourceFolder: string
  operations: Array<{
    imageId: string
    imagePath: string
    targetFolder: string
    newName?: string
    ocrData?: Partial<OCRResult>
  }>
  rules: OrganizationRule[]
}

interface ProcessingProgress {
  total: number
  processed: number
  errors: number
  currentImage?: string
  status: 'idle' | 'analyzing' | 'organizing' | 'completed' | 'error' | 'paused'
}

interface OrganizationSettings {
  groupBy: 'person' | 'date' | 'state' | 'custom'
  createSubfolders: boolean
  includeTimestamp: boolean
  preserveOriginal: boolean
  handleDuplicates: 'skip' | 'rename' | 'overwrite'
  namingPattern: string
}

// Predefined organization rules
const DEFAULT_RULES: OrganizationRule[] = [
  {
    id: 'by-person',
    name: 'Group by Person',
    pattern: '{lastName}-{firstName}',
    description: 'Creates folders named: Smith-John, Doe-Jane',
    enabled: true
  },
  {
    id: 'by-person-dob',
    name: 'Group by Person + DOB',
    pattern: '{lastName}-{firstName}-{dob}',
    description: 'Creates folders named: Smith-John-1990-05-15',
    enabled: false
  },
  {
    id: 'by-state',
    name: 'Group by State',
    pattern: '{state}/{lastName}-{firstName}',
    description: 'Creates state folders with person subfolders',
    enabled: false
  },
  {
    id: 'by-expiration',
    name: 'Group by Expiration Year',
    pattern: 'Expires-{expirationYear}/{lastName}-{firstName}',
    description: 'Groups by license expiration year',
    enabled: false
  },
  {
    id: 'chronological',
    name: 'Chronological',
    pattern: '{year}/{month}-{day}/{lastName}-{firstName}',
    description: 'Organizes by processing date',
    enabled: false
  }
]

// Generate folder name from OCR data and pattern
const generateFolderName = (ocrData: Partial<OCRResult>, pattern: string, timestamp?: Date): string => {
  let folderName = pattern
  
  const replacements: Record<string, string> = {
    '{firstName}': ocrData.firstName || 'Unknown',
    '{lastName}': ocrData.lastName || 'Unknown',
    '{middleName}': ocrData.middleName || '',
    '{state}': ocrData.state || 'Unknown',
    '{licenseNumber}': ocrData.licenseNumber || 'Unknown',
    '{dob}': ocrData.dateOfBirth || 'Unknown',
    '{expirationDate}': ocrData.expirationDate || 'Unknown',
    '{expirationYear}': ocrData.expirationDate ? new Date(ocrData.expirationDate).getFullYear().toString() : 'Unknown',
    '{year}': timestamp ? timestamp.getFullYear().toString() : new Date().getFullYear().toString(),
    '{month}': timestamp ? (timestamp.getMonth() + 1).toString().padStart(2, '0') : (new Date().getMonth() + 1).toString().padStart(2, '0'),
    '{day}': timestamp ? timestamp.getDate().toString().padStart(2, '0') : new Date().getDate().toString().padStart(2, '0')
  }
  
  Object.entries(replacements).forEach(([placeholder, value]) => {
    folderName = folderName.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), value)
  })
  
  // Clean up folder name - remove invalid characters
  folderName = folderName
    .replace(/[<>:"/\\|?*]/g, '-')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
  
  return folderName || 'Unknown'
}

// Mock OCR analysis for demo
const performBatchOCRAnalysis = async (
  images: ImageFile[], 
  onProgress: (progress: ProcessingProgress) => void,
  countryMode: 'us' | 'australian'
): Promise<Map<string, Partial<OCRResult>>> => {
  const results = new Map<string, Partial<OCRResult>>()
  const total = images.length
  
  for (let i = 0; i < images.length; i++) {
    const image = images[i]
    
    onProgress({
      total,
      processed: i,
      errors: 0,
      currentImage: image.filename,
      status: 'analyzing'
    })
    
    // Simulate OCR processing time
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000))
    
    try {
      // Mock OCR result based on filename patterns
      const filename = image.filename.toLowerCase()
      const mockData: Partial<OCRResult> = {
        firstName: `Person${i + 1}`,
        lastName: filename.includes('smith') ? 'Smith' : 
                  filename.includes('jones') ? 'Jones' : 
                  filename.includes('brown') ? 'Brown' : 
                  `Surname${Math.floor(Math.random() * 100)}`,
        dateOfBirth: new Date(1970 + Math.floor(Math.random() * 40), 
                             Math.floor(Math.random() * 12), 
                             Math.floor(Math.random() * 28) + 1).toISOString().split('T')[0],
        licenseNumber: `${countryMode === 'australian' ? 'A' : 'D'}${Math.floor(Math.random() * 10000000)}`,
        state: countryMode === 'australian' 
          ? ['NSW', 'VIC', 'QLD', 'WA', 'SA', 'TAS'][Math.floor(Math.random() * 6)]
          : ['CA', 'NY', 'TX', 'FL', 'IL', 'PA'][Math.floor(Math.random() * 6)],
        expirationDate: new Date(2025 + Math.floor(Math.random() * 5), 
                                Math.floor(Math.random() * 12), 
                                Math.floor(Math.random() * 28) + 1).toISOString().split('T')[0],
        confidence: 0.8 + Math.random() * 0.2
      }
      
      results.set(image.id, mockData)
    } catch (error) {
      console.error(`OCR failed for ${image.filename}:`, error)
    }
  }
  
  onProgress({
    total,
    processed: total,
    errors: 0,
    status: 'completed'
  })
  
  return results
}

export default function BatchOrganizationPanel({
  sourceFolder,
  images,
  onOrganize,
  className,
  countryMode
}: BatchOrganizationPanelProps) {
  const [rules, setRules] = useState<OrganizationRule[]>(DEFAULT_RULES)
  const [progress, setProgress] = useState<ProcessingProgress>({
    total: 0,
    processed: 0,
    errors: 0,
    status: 'idle'
  })
  const [ocrResults, setOcrResults] = useState<Map<string, Partial<OCRResult>>>(new Map())
  const [organizationPlan, setOrganizationPlan] = useState<OrganizationPlan | null>(null)
  const [settings, setSettings] = useState<OrganizationSettings>({
    groupBy: 'person',
    createSubfolders: true,
    includeTimestamp: false,
    preserveOriginal: false,
    handleDuplicates: 'rename',
    namingPattern: '{lastName}-{firstName}'
  })

  const activeRules = rules.filter(rule => rule.enabled)

  const handleRuleToggle = (ruleId: string, enabled: boolean) => {
    setRules(prev => prev.map(rule => 
      rule.id === ruleId ? { ...rule, enabled } : rule
    ))
  }

  const handleStartAnalysis = async () => {
    if (images.length === 0) return
    
    setProgress({ total: images.length, processed: 0, errors: 0, status: 'analyzing' })
    
    try {
      const results = await performBatchOCRAnalysis(images, setProgress, countryMode)
      setOcrResults(results)
      
      // Generate organization plan
      const plan = generateOrganizationPlan(results)
      setOrganizationPlan(plan)
      
    } catch (error) {
      setProgress(prev => ({ ...prev, status: 'error' }))
      console.error('Batch analysis failed:', error)
    }
  }

  const generateOrganizationPlan = (results: Map<string, Partial<OCRResult>>): OrganizationPlan => {
    const operations = []
    
    for (const [imageId, ocrData] of results) {
      const image = images.find(img => img.id === imageId)
      if (!image || !ocrData) continue
      
      const activeRule = activeRules[0] // Use first active rule
      if (!activeRule) continue
      
      const targetFolder = generateFolderName(
        ocrData, 
        activeRule.pattern, 
        settings.includeTimestamp ? new Date() : undefined
      )
      
      operations.push({
        imageId,
        imagePath: image.path,
        targetFolder: `${sourceFolder}/${targetFolder}`,
        newName: settings.preserveOriginal ? undefined : `${ocrData.firstName}-${ocrData.lastName}-${ocrData.state}.${image.filename.split('.').pop()}`,
        ocrData
      })
    }
    
    return {
      sourceFolder,
      operations,
      rules: activeRules
    }
  }

  const handleExecuteOrganization = async () => {
    if (!organizationPlan) return
    
    setProgress(prev => ({ ...prev, status: 'organizing' }))
    
    try {
      const success = await onOrganize(organizationPlan)
      
      if (success) {
        setProgress(prev => ({ ...prev, status: 'completed' }))
      } else {
        setProgress(prev => ({ ...prev, status: 'error' }))
      }
    } catch (error) {
      setProgress(prev => ({ ...prev, status: 'error' }))
      console.error('Organization failed:', error)
    }
  }

  const getStatusColor = (status: ProcessingProgress['status']) => {
    switch (status) {
      case 'idle': return 'text-muted-foreground'
      case 'analyzing': return 'text-blue-500'
      case 'organizing': return 'text-yellow-500'
      case 'completed': return 'text-green-500'
      case 'error': return 'text-red-500'
      case 'paused': return 'text-orange-500'
    }
  }

  const getStatusIcon = (status: ProcessingProgress['status']) => {
    switch (status) {
      case 'idle': return <Square className="h-4 w-4" />
      case 'analyzing': return <Loader2 className="h-4 w-4 animate-spin" />
      case 'organizing': return <Loader2 className="h-4 w-4 animate-spin" />
      case 'completed': return <CheckCircle2 className="h-4 w-4" />
      case 'error': return <AlertCircle className="h-4 w-4" />
      case 'paused': return <Pause className="h-4 w-4" />
    }
  }

  return (
    <Card className={cn("h-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Archive className="h-5 w-5" />
          Batch Organization
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Automatically organize {images.length} images into folders based on OCR data
        </p>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Source Information */}
        <div className="space-y-2">
          <h4 className="font-medium flex items-center gap-2">
            <FolderOpen className="h-4 w-4" />
            Source Folder
          </h4>
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm font-mono break-all">{sourceFolder}</p>
            <div className="flex items-center gap-4 mt-2">
              <Badge variant="outline">{images.length} images</Badge>
              <Badge variant="outline">{countryMode.toUpperCase()} mode</Badge>
            </div>
          </div>
        </div>

        {/* Organization Rules */}
        <div className="space-y-3">
          <h4 className="font-medium">Organization Rules</h4>
          <div className="space-y-2">
            {rules.map(rule => (
              <div key={rule.id} className="flex items-start gap-3 p-3 rounded border">
                <Checkbox
                  checked={rule.enabled}
                  onCheckedChange={(checked) => handleRuleToggle(rule.id, checked as boolean)}
                  id={`rule-${rule.id}`}
                />
                <div className="flex-1 min-w-0">
                  <label htmlFor={`rule-${rule.id}`} className="text-sm font-medium">
                    {rule.name}
                  </label>
                  <p className="text-xs text-muted-foreground mt-1">{rule.description}</p>
                  <code className="text-xs bg-muted px-1 rounded mt-1 inline-block">
                    {rule.pattern}
                  </code>
                </div>
              </div>
            ))}
          </div>
          
          {activeRules.length === 0 && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-800">
                Please select at least one organization rule to proceed.
              </p>
            </div>
          )}
        </div>

        {/* Processing Progress */}
        {progress.status !== 'idle' && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium flex items-center gap-2">
                {getStatusIcon(progress.status)}
                <span className={getStatusColor(progress.status)}>
                  Processing Status
                </span>
              </h4>
              <Badge variant="outline">
                {progress.processed}/{progress.total}
              </Badge>
            </div>
            
            {progress.total > 0 && (
              <Progress 
                value={(progress.processed / progress.total) * 100} 
                className="w-full"
              />
            )}
            
            {progress.currentImage && (
              <p className="text-sm text-muted-foreground">
                Processing: {progress.currentImage}
              </p>
            )}
            
            {progress.errors > 0 && (
              <Badge variant="destructive">
                {progress.errors} errors
              </Badge>
            )}
          </div>
        )}

        {/* Organization Plan Preview */}
        {organizationPlan && (
          <div className="space-y-3">
            <h4 className="font-medium">Organization Preview</h4>
            <div className="max-h-48 overflow-y-auto space-y-2">
              {organizationPlan.operations.slice(0, 10).map((op, index) => (
                <div key={index} className="p-3 rounded border bg-muted/50">
                  <div className="flex items-center gap-2 mb-1">
                    <Users className="h-3 w-3 text-muted-foreground" />
                    <span className="text-sm font-medium">
                      {op.ocrData?.firstName} {op.ocrData?.lastName}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {op.ocrData?.state}
                    </Badge>
                  </div>
                  <div className="text-xs space-y-1">
                    <div className="flex">
                      <span className="w-16 text-muted-foreground">To:</span>
                      <span className="font-mono text-green-600">{op.targetFolder}</span>
                    </div>
                    {op.newName && (
                      <div className="flex">
                        <span className="w-16 text-muted-foreground">Name:</span>
                        <span className="font-mono">{op.newName}</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
              
              {organizationPlan.operations.length > 10 && (
                <div className="text-center text-sm text-muted-foreground py-2">
                  ... and {organizationPlan.operations.length - 10} more operations
                </div>
              )}
            </div>
            
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>{organizationPlan.operations.length}</strong> images will be organized into{' '}
                <strong>{new Set(organizationPlan.operations.map(op => op.targetFolder)).size}</strong> folders
              </p>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2">
          {progress.status === 'idle' && (
            <Button
              onClick={handleStartAnalysis}
              disabled={activeRules.length === 0 || images.length === 0}
              className="flex-1"
            >
              <Zap className="h-4 w-4 mr-2" />
              Start Analysis
            </Button>
          )}
          
          {organizationPlan && progress.status === 'completed' && (
            <Button
              onClick={handleExecuteOrganization}
              disabled={false}
              className="flex-1"
            >
              <Play className="h-4 w-4 mr-2" />
              Execute Organization
            </Button>
          )}
          
          {progress.status === 'organizing' && (
            <Button disabled className="flex-1">
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Organizing...
            </Button>
          )}
        </div>

        {/* Results Summary */}
        {progress.status === 'completed' && organizationPlan && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle2 className="h-5 w-5 text-green-600" />
              <h4 className="font-medium text-green-800">Organization Plan Ready</h4>
            </div>
            <div className="text-sm text-green-700 space-y-1">
              <p>✓ Analyzed {progress.total} images successfully</p>
              <p>✓ Generated {organizationPlan.operations.length} organization operations</p>
              <p>✓ Will create {new Set(organizationPlan.operations.map(op => op.targetFolder)).size} new folders</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}