#!/usr/bin/env python3
"""
Test script for ReadySearch GUI improvements
Tests the new column structure with sample data
"""

import tkinter as tk
from tkinter import messagebox
import sys
from pathlib import Path
from dataclasses import dataclass
from typing import List, Dict, Any, Optional

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

from readysearch_gui import GUISearchResult, ReadySearchG<PERSON>

def create_sample_results() -> List[GUISearchResult]:
    """Create sample results that mimic real search data"""
    
    sample_results = [
        # Result with matches and location data
        GUISearchResult(
            name="NADER GHARSA",
            status="Match",
            search_duration=2.34,
            matches_found=3,
            exact_matches=2,
            partial_matches=1,
            match_category="EXACT_MATCH",
            match_reasoning="Found exact first and last name match",
            detailed_results=[
                {
                    'matched_name': 'NADER GHARSA',
                    'match_type': 'EXACT MATCH',
                    'date_of_birth': '27/02/1977',
                    'location': 'CAIRO EGYPT',
                    'confidence': 0.95
                },
                {
                    'matched_name': 'NADER A GHARSA',
                    'match_type': 'EXACT MATCH',
                    'date_of_birth': '27/02/1977',
                    'location': 'CAIRO EGYPT',
                    'confidence': 0.92
                },
                {
                    'matched_name': 'NADER GHARA',
                    'match_type': 'PARTIAL MATCH',
                    'date_of_birth': '15/03/1978',
                    'location': 'ALEXANDRIA EGYPT',
                    'confidence': 0.78
                }
            ],
            timestamp="2025-07-23T01:35:14",
            birth_year=1977
        ),
        
        # Result with no matches
        GUISearchResult(
            name="JOHN DOE UNKNOWN",
            status="No Match",
            search_duration=1.89,
            matches_found=0,
            exact_matches=0,
            partial_matches=0,
            match_category="NO_MATCH",
            match_reasoning="No matching records found",
            detailed_results=[],
            timestamp="2025-07-23T01:35:16",
            birth_year=1985
        ),
        
        # Result with error
        GUISearchResult(
            name="ERROR TEST NAME",
            status="Error",
            search_duration=0.45,
            matches_found=0,
            exact_matches=0,
            partial_matches=0,
            match_category="ERROR",
            match_reasoning="Connection timeout",
            detailed_results=[],
            timestamp="2025-07-23T01:35:18",
            error="Connection timeout after 30 seconds"
        ),
        
        # Result with multiple location data
        GUISearchResult(
            name="GLORIA JOY NADER MCQUILLAN",
            status="Match",
            search_duration=3.12,
            matches_found=2,
            exact_matches=2,
            partial_matches=0,
            match_category="EXACT_MATCH",
            match_reasoning="Found exact match with multiple middle names",
            detailed_results=[
                {
                    'matched_name': 'GLORIA JOY NADER MCQUILLAN',
                    'match_type': 'EXACT MATCH',
                    'date_of_birth': '06/08/1951',
                    'location': 'PERTH WA',
                    'confidence': 0.98
                },
                {
                    'matched_name': 'GLORIA J MCQUILLAN',
                    'match_type': 'EXACT MATCH',
                    'date_of_birth': '06/08/1951',
                    'location': 'PERTH WA',
                    'confidence': 0.94
                }
            ],
            timestamp="2025-07-23T01:35:20",
            birth_year=1951
        )
    ]
    
    return sample_results

def test_gui_with_sample_data():
    """Test the GUI with sample data"""
    try:
        # Create GUI instance
        gui = ReadySearchGUI()
        
        # Add sample results to simulate a completed search
        sample_results = create_sample_results()
        gui._add_results(sample_results)
        
        # Show success message
        messagebox.showinfo("✅ GUI Improvements Applied!", 
                          f"Successfully loaded {len(sample_results)} sample results!\n\n"
                          "🔥 NEW IMPROVEMENTS:\n"
                          "• ❌ Removed redundant location column from main table\n"
                          "• ✅ Expanded match summary (readable format)\n" 
                          "• 📍 Location now shows in detailed results tab\n"
                          "• 🎯 Professional column layout\n\n"
                          "Check the Summary tab for the new layout,\n"
                          "then switch to Detailed tab to see locations!")
        
        # Run GUI
        gui.run()
        
    except Exception as e:
        messagebox.showerror("Test Error", f"Failed to test GUI improvements:\n\n{str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    print("🧪 Testing ReadySearch GUI improvements...")
    print("📊 Loading sample data with new column structure...")
    
    success = test_gui_with_sample_data()
    
    if success:
        print("✅ GUI test completed successfully!")
    else:
        print("❌ GUI test failed!")
        sys.exit(1)