"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, SelectGroup, SelectLabel, SelectSeparator } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Settings, Save, RefreshCw, AlertCircle, CheckCircle, Bot, ExternalLink, Eye, EyeOff } from 'lucide-react'
import { Checkbox } from '@/components/ui/checkbox'
import { cn } from '@/lib/utils'
import { useSettingsStandalone, type VisionModel, type OpenRouterConfig, type OCRSettings } from '@/hooks/use-settings-sync'

interface EnhancedOCRSettingsProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  onSettingsChange?: (settings: OCRSettings & { selectedModel: string; ocrMode: string }) => void
  currentModel?: string
  className?: string
}

export default function EnhancedOCRSettings({
  isOpen,
  onOpenChange,
  onSettingsChange,
  currentModel = 'gpt-4o-mini',
  className
}: EnhancedOCRSettingsProps) {
  // Use shared settings synchronization
  const {
    openRouterConfig,
    ocrSettings,
    isLoading,
    error,
    connectionStatus,
    saveOpenRouterConfig,
    saveOCRSettings,
    testConnection,
    updateModel,
    updateOCRMode,
    clearError
  } = useSettingsStandalone()

  const [showApiKey, setShowApiKey] = useState(false)
  const [localConfig, setLocalConfig] = useState<OpenRouterConfig>(openRouterConfig)
  const [localOCRSettings, setLocalOCRSettings] = useState<OCRSettings>(ocrSettings)

  // Sync with global settings when they change
  useEffect(() => {
    setLocalConfig(openRouterConfig)
    setLocalOCRSettings(ocrSettings)
  }, [openRouterConfig, ocrSettings])

  // Update local model when currentModel prop changes
  useEffect(() => {
    if (currentModel && currentModel !== localConfig.selectedModel) {
      setLocalConfig(prev => ({ ...prev, selectedModel: currentModel }))
    }
  }, [currentModel, localConfig.selectedModel])

  // Vision models available on OpenRouter (matching main settings)
  const visionModels: VisionModel[] = [
    // Free models
    {
      id: 'google/gemini-flash-1.5',
      name: 'Gemini Flash 1.5',
      provider: 'Google',
      cost: 'Free',
      description: 'Fast and efficient vision model for document analysis',
      tier: 'free',
      contextWindow: 1000000,
      maxOutputTokens: 8192
    },
    {
      id: 'qwen/qwen-2-vl-7b-instruct',
      name: 'Qwen2-VL 7B',
      provider: 'Qwen',
      cost: 'Free',
      description: 'Open-source vision-language model with good OCR performance',
      tier: 'free',
      contextWindow: 32768,
      maxOutputTokens: 8192
    },
    {
      id: 'meta-llama/llama-3.2-11b-vision-instruct',
      name: 'Llama 3.2 11B Vision',
      provider: 'Meta',
      cost: 'Free',
      description: 'Meta\'s vision-capable language model',
      tier: 'free',
      contextWindow: 131072,
      maxOutputTokens: 2048
    },
    // Paid models
    {
      id: 'openai/gpt-4o',
      name: 'GPT-4o',
      provider: 'OpenAI',
      cost: '$15/1M tokens',
      description: 'Most capable multimodal model for complex document analysis',
      tier: 'paid',
      contextWindow: 128000,
      maxOutputTokens: 4096
    },
    {
      id: 'openai/gpt-4o-mini',
      name: 'GPT-4o Mini',
      provider: 'OpenAI',
      cost: '$0.15/1M tokens',
      description: 'Affordable multimodal model with excellent performance',
      tier: 'paid',
      contextWindow: 128000,
      maxOutputTokens: 16384
    },
    {
      id: 'anthropic/claude-3.5-sonnet',
      name: 'Claude 3.5 Sonnet',
      provider: 'Anthropic',
      cost: '$3/1M tokens',
      description: 'Excellent vision and reasoning capabilities',
      tier: 'paid',
      contextWindow: 200000,
      maxOutputTokens: 4096
    }
  ]

  const saveConfig = async () => {
    clearError()

    try {
      // Save OpenRouter config
      await saveOpenRouterConfig(localConfig)

      // Save OCR settings
      await saveOCRSettings(localOCRSettings)

      // Notify parent component of settings change
      if (onSettingsChange) {
        onSettingsChange({
          ...localOCRSettings,
          selectedModel: localConfig.selectedModel,
          ocrMode: localConfig.ocrMode
        })
      }

      // Test connection if API key and model are configured
      if (localConfig.apiKey && localConfig.selectedModel) {
        await testConnection()
      }

    } catch (error) {
      // Error is already handled by the shared hook
      console.error('Failed to save settings:', error)
    }
  }

  const getModelsByTier = (tier: 'free' | 'paid') => {
    return visionModels.filter(model => model.tier === tier)
  }

  const getSelectedModel = () => {
    return visionModels.find(model => model.id === localConfig.selectedModel)
  }

  const formatContextWindow = (tokens: number) => {
    if (tokens >= 1000000) {
      return `${(tokens / 1000000).toFixed(1)}M`
    } else if (tokens >= 1000) {
      return `${(tokens / 1000).toFixed(0)}K`
    }
    return tokens.toString()
  }

  const updateLocalOCRSettings = (key: keyof OCRSettings, value: any) => {
    setLocalOCRSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const updateProcessingOption = (key: keyof OCRSettings['processingOptions'], value: boolean) => {
    setLocalOCRSettings(prev => ({
      ...prev,
      processingOptions: {
        ...prev.processingOptions,
        [key]: value
      }
    }))
  }

  const updateQualitySetting = (key: keyof OCRSettings['qualitySettings'], value: any) => {
    setLocalOCRSettings(prev => ({
      ...prev,
      qualitySettings: {
        ...prev.qualitySettings,
        [key]: value
      }
    }))
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[85vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            OCR Settings & Configuration
          </DialogTitle>
        </DialogHeader>
        
        <Tabs defaultValue="processing" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="processing">Processing</TabsTrigger>
            <TabsTrigger value="model">AI Model</TabsTrigger>
            <TabsTrigger value="quality">Quality</TabsTrigger>
          </TabsList>

          {/* Processing Settings */}
          <TabsContent value="processing" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Document Processing Options</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {error && (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                {/* Extraction Type */}
                <div className="space-y-3">
                  <Label>Document Extraction Type</Label>
                  <Select value={localOCRSettings.extractionType} onValueChange={(value: any) => updateLocalOCRSettings('extractionType', value)}>
                    <SelectTrigger className="h-12">
                      <SelectValue placeholder="Select extraction type" />
                    </SelectTrigger>
                    <SelectContent className="max-h-[300px]" position="popper" sideOffset={5}>
                      <SelectItem value="auto-detect">
                        <div className="flex flex-col">
                          <span className="font-medium">Auto-Detect (Recommended)</span>
                          <span className="text-xs text-muted-foreground">
                            Automatically detects document type and extracts appropriate fields
                          </span>
                        </div>
                      </SelectItem>
                      <SelectSeparator />
                      <SelectItem value="driver_license">
                        <div className="flex flex-col">
                          <span className="font-medium">Driver&apos;s License</span>
                          <span className="text-xs text-muted-foreground">
                            US/Australian driver&apos;s licenses with name, DOB, address, license #
                          </span>
                        </div>
                      </SelectItem>
                      <SelectItem value="passport">
                        <div className="flex flex-col">
                          <span className="font-medium">Passport</span>
                          <span className="text-xs text-muted-foreground">
                            International passports with name, nationality, passport #
                          </span>
                        </div>
                      </SelectItem>
                      <SelectItem value="id_card">
                        <div className="flex flex-col">
                          <span className="font-medium">ID Card</span>
                          <span className="text-xs text-muted-foreground">
                            Government ID cards and identification documents
                          </span>
                        </div>
                      </SelectItem>
                      <SelectItem value="general">
                        <div className="flex flex-col">
                          <span className="font-medium">General OCR</span>
                          <span className="text-xs text-muted-foreground">
                            Extract all text without structured field parsing
                          </span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* OCR Mode Selection */}
                <div className="space-y-3">
                  <Label>OCR Processing Mode</Label>
                  <Select value={localConfig.ocrMode} onValueChange={(value: 'us' | 'australian' | 'auto-detect') => setLocalConfig({ ...localConfig, ocrMode: value })}>
                    <SelectTrigger className="h-12">
                      <SelectValue placeholder="Select OCR mode" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="auto-detect">
                        <div className="flex flex-col">
                          <span className="font-medium">Auto-Detect Mode</span>
                          <span className="text-xs text-muted-foreground">
                            Automatically detects document type: Driver License, Passport, ID, or Selfie
                          </span>
                        </div>
                      </SelectItem>
                      <SelectItem value="us">
                        <div className="flex flex-col">
                          <span className="font-medium">US Driver License / ID</span>
                          <span className="text-xs text-muted-foreground">
                            Standard US format with first, middle, last name
                          </span>
                        </div>
                      </SelectItem>
                      <SelectItem value="australian">
                        <div className="flex flex-col">
                          <span className="font-medium">Australian Driver License</span>
                          <span className="text-xs text-muted-foreground">
                            Australian format with surname, given names, front/back detection
                          </span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Processing Options */}
                <div className="space-y-3">
                  <Label>Processing Options</Label>
                  <div className="space-y-3 p-3 border rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="autoSave"
                        checked={localOCRSettings.processingOptions.autoSave}
                        onCheckedChange={(checked) => updateProcessingOption('autoSave', checked as boolean)}
                      />
                      <Label htmlFor="autoSave" className="text-sm">
                        Auto-save OCR results to text files
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="showConfidence"
                        checked={localOCRSettings.processingOptions.showConfidence}
                        onCheckedChange={(checked) => updateProcessingOption('showConfidence', checked as boolean)}
                      />
                      <Label htmlFor="showConfidence" className="text-sm">
                        Show confidence scores in results
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="batchMode"
                        checked={localOCRSettings.processingOptions.batchMode}
                        onCheckedChange={(checked) => updateProcessingOption('batchMode', checked as boolean)}
                      />
                      <Label htmlFor="batchMode" className="text-sm">
                        Enable batch processing mode
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="highAccuracy"
                        checked={localOCRSettings.processingOptions.highAccuracy}
                        onCheckedChange={(checked) => updateProcessingOption('highAccuracy', checked as boolean)}
                      />
                      <Label htmlFor="highAccuracy" className="text-sm">
                        High accuracy mode (slower, more accurate)
                      </Label>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* AI Model Configuration */}
          <TabsContent value="model" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bot className="h-5 w-5" />
                  AI Model Configuration
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* API Key */}
                <div className="space-y-2">
                  <Label htmlFor="apiKey">OpenRouter API Key</Label>
                  <div className="relative">
                    <Input
                      id="apiKey"
                      type={showApiKey ? "text" : "password"}
                      placeholder="Enter your OpenRouter API key"
                      value={localConfig.apiKey}
                      onChange={(e) => setLocalConfig({ ...localConfig, apiKey: e.target.value })}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowApiKey(!showApiKey)}
                    >
                      {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Get your API key from{' '}
                    <a href="https://openrouter.ai/keys" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                      OpenRouter.ai
                    </a>
                  </p>
                </div>

                {/* Model Selection */}
                <div className="space-y-3">
                  <Label htmlFor="model">Vision Model</Label>
                  <Select value={localConfig.selectedModel} onValueChange={(value) => setLocalConfig({ ...localConfig, selectedModel: value })}>
                    <SelectTrigger className="h-12">
                      <SelectValue placeholder="Select a vision model" />
                    </SelectTrigger>
                    <SelectContent className="max-h-[400px]" position="popper" sideOffset={5}>
                      <SelectGroup>
                        <SelectLabel className="text-green-600 font-semibold">🆓 Free Models</SelectLabel>
                        {getModelsByTier('free').map((model) => (
                          <SelectItem key={model.id} value={model.id} className="py-2">
                            <div className="flex flex-col">
                              <div className="flex items-center gap-2">
                                <span className="font-medium">{model.name}</span>
                                <span className="text-xs text-muted-foreground">({model.provider})</span>
                              </div>
                              <span className="text-xs text-muted-foreground">{model.cost}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectGroup>
                      
                      <SelectSeparator />
                      
                      <SelectGroup>
                        <SelectLabel className="text-blue-600 font-semibold">💰 Paid Models</SelectLabel>
                        {getModelsByTier('paid').map((model) => (
                          <SelectItem key={model.id} value={model.id} className="py-2">
                            <div className="flex flex-col">
                              <div className="flex items-center gap-2">
                                <span className="font-medium">{model.name}</span>
                                <span className="text-xs text-muted-foreground">({model.provider})</span>
                              </div>
                              <span className="text-xs text-muted-foreground">{model.cost}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                </div>

                {/* Selected Model Info */}
                {localConfig.selectedModel && (
                  <Card className="bg-muted/50">
                    <CardContent className="pt-4">
                      {(() => {
                        const model = getSelectedModel()
                        if (!model) return null
                        
                        return (
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium">{model.name}</h4>
                              <Badge variant="secondary">{model.provider}</Badge>
                              <Badge variant={model.tier === 'free' ? 'default' : 'outline'}>
                                {model.tier === 'free' ? 'FREE' : model.cost}
                              </Badge>
                            </div>
                            <p className="text-sm text-muted-foreground">{model.description}</p>
                            <div className="flex gap-4 text-xs text-muted-foreground">
                              <span>Context: {formatContextWindow(model.contextWindow)} tokens</span>
                              <span>Max Output: {formatContextWindow(model.maxOutputTokens)} tokens</span>
                            </div>
                          </div>
                        )
                      })()}
                    </CardContent>
                  </Card>
                )}

                {/* Connection Status */}
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-2">
                    <div className={cn(
                      "h-2 w-2 rounded-full",
                      connectionStatus === 'connected' ? 'bg-green-500' : 
                      connectionStatus === 'failed' ? 'bg-red-500' : 'bg-gray-300'
                    )} />
                    <span className="text-sm text-muted-foreground">
                      {connectionStatus === 'connected' ? 'Connected' : 
                       connectionStatus === 'failed' ? 'Connection Failed' : 'Not Tested'}
                    </span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={testConnection}
                    disabled={!localConfig.apiKey || !localConfig.selectedModel || isLoading}
                  >
                    {isLoading ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <CheckCircle className="h-4 w-4 mr-2" />
                    )}
                    Test Connection
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Quality Settings */}
          <TabsContent value="quality" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Quality & Accuracy Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Minimum Confidence */}
                <div className="space-y-3">
                  <Label>Minimum Confidence Threshold</Label>
                  <div className="flex items-center gap-4">
                    <Input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={localOCRSettings.qualitySettings.minConfidence}
                      onChange={(e) => updateQualitySetting('minConfidence', parseFloat(e.target.value))}
                      className="flex-1"
                    />
                    <span className="text-sm font-mono w-16">
                      {Math.round(localOCRSettings.qualitySettings.minConfidence * 100)}%
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Results below this confidence level will be flagged for review
                  </p>
                </div>

                {/* Quality Options */}
                <div className="space-y-3">
                  <Label>Quality Enhancement Options</Label>
                  <div className="space-y-3 p-3 border rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="retryFailures"
                        checked={localOCRSettings.qualitySettings.retryFailures}
                        onCheckedChange={(checked) => updateQualitySetting('retryFailures', checked as boolean)}
                      />
                      <Label htmlFor="retryFailures" className="text-sm">
                        Automatically retry failed extractions
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="enhanceImages"
                        checked={localOCRSettings.qualitySettings.enhanceImages}
                        onCheckedChange={(checked) => updateQualitySetting('enhanceImages', checked as boolean)}
                      />
                      <Label htmlFor="enhanceImages" className="text-sm">
                        Pre-process images for better OCR results
                      </Label>
                    </div>
                  </div>
                </div>

                {/* Quality Information */}
                <div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded-md">
                  <h4 className="font-medium text-sm mb-2">Quality Enhancement Tips:</h4>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    <li>• Higher confidence thresholds improve accuracy but may reject valid results</li>
                    <li>• Image enhancement can help with poor quality photos but increases processing time</li>
                    <li>• Retry failures automatically attempts extraction with different settings</li>
                    <li>• High accuracy mode uses more computational resources but provides better results</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Actions */}
        <div className="flex gap-2 pt-4 border-t">
          <Button onClick={saveConfig} disabled={isLoading}>
            {isLoading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Settings
              </>
            )}
          </Button>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}