"use client"

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Button } from '@/components/ui/button'
import { 
  DollarSign, 
  TrendingUp, 
  AlertTriangle, 
  RefreshCw,
  Calendar,
  BarChart3
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface CostStats {
  totalSpent: number
  totalRequests: number
  averageCostPerRequest: number
  modelUsage: Record<string, { requests: number; cost: number }>
  providerUsage: Record<string, { requests: number; cost: number }>
  currentLimits: {
    daily: { spent: number; limit: number; remaining: number }
    monthly: { spent: number; limit: number; remaining: number }
    total: { spent: number; limit: number; remaining: number }
  }
  recentTransactions: Array<{
    timestamp: string
    modelId: string
    cost: number
    success: boolean
  }>
}

interface CostTrackerPanelProps {
  lastOperationCost?: {
    modelUsed: string
    estimatedCost: number
    currentLimits: CostStats['currentLimits']
  }
  className?: string
}

export default function CostTrackerPanel({ lastOperationCost, className }: CostTrackerPanelProps) {
  const [costStats, setCostStats] = useState<CostStats | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchCostStats = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('http://localhost:3003/api/ocr/cost-stats')
      const data = await response.json()
      
      if (data.success) {
        setCostStats(data.data)
      } else {
        setError(data.error || 'Failed to fetch cost statistics')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch cost statistics')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchCostStats()
  }, [])

  // Update stats when a new operation completes
  useEffect(() => {
    if (lastOperationCost) {
      fetchCostStats()
    }
  }, [lastOperationCost])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4
    }).format(amount)
  }

  const getUsageColor = (used: number, limit: number) => {
    const percentage = (used / limit) * 100
    if (percentage >= 90) return 'text-red-500'
    if (percentage >= 80) return 'text-yellow-500'
    return 'text-green-500'
  }

  const getProgressColor = (used: number, limit: number) => {
    const percentage = (used / limit) * 100
    if (percentage >= 90) return 'bg-red-500'
    if (percentage >= 80) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  if (error) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-500">
            <AlertTriangle className="h-5 w-5" />
            Cost Tracking Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">{error}</p>
          <Button 
            onClick={fetchCostStats} 
            size="sm" 
            variant="outline" 
            className="mt-2"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5 text-green-500" />
            Cost Tracker
          </CardTitle>
          <Button 
            onClick={fetchCostStats} 
            size="sm" 
            variant="ghost"
            disabled={isLoading}
          >
            <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Last Operation */}
        {lastOperationCost && (
          <div className="p-3 bg-muted rounded-lg">
            <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-blue-500" />
              Last Operation
            </h4>
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span>Model:</span>
                <Badge variant="outline">{lastOperationCost.modelUsed}</Badge>
              </div>
              <div className="flex justify-between text-sm">
                <span>Cost:</span>
                <span className="font-medium text-green-600">
                  {formatCurrency(lastOperationCost.estimatedCost)}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Current Limits */}
        {costStats && (
          <div className="space-y-4">
            <h4 className="font-medium text-sm flex items-center gap-2">
              <BarChart3 className="h-4 w-4 text-blue-500" />
              Usage Limits
            </h4>
            
            {/* Daily Usage */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Daily</span>
                <span className={getUsageColor(costStats.currentLimits.daily.spent, costStats.currentLimits.daily.limit)}>
                  {formatCurrency(costStats.currentLimits.daily.spent)} / {formatCurrency(costStats.currentLimits.daily.limit)}
                </span>
              </div>
              <Progress 
                value={(costStats.currentLimits.daily.spent / costStats.currentLimits.daily.limit) * 100}
                className="h-2"
              />
            </div>

            {/* Monthly Usage */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Monthly</span>
                <span className={getUsageColor(costStats.currentLimits.monthly.spent, costStats.currentLimits.monthly.limit)}>
                  {formatCurrency(costStats.currentLimits.monthly.spent)} / {formatCurrency(costStats.currentLimits.monthly.limit)}
                </span>
              </div>
              <Progress 
                value={(costStats.currentLimits.monthly.spent / costStats.currentLimits.monthly.limit) * 100}
                className="h-2"
              />
            </div>

            {/* Total Usage */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Total</span>
                <span className={getUsageColor(costStats.currentLimits.total.spent, costStats.currentLimits.total.limit)}>
                  {formatCurrency(costStats.currentLimits.total.spent)} / {formatCurrency(costStats.currentLimits.total.limit)}
                </span>
              </div>
              <Progress 
                value={(costStats.currentLimits.total.spent / costStats.currentLimits.total.limit) * 100}
                className="h-2"
              />
            </div>
          </div>
        )}

        {/* Statistics */}
        {costStats && (
          <div className="space-y-4">
            <h4 className="font-medium text-sm flex items-center gap-2">
              <Calendar className="h-4 w-4 text-blue-500" />
              Statistics
            </h4>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Total Requests</span>
                <p className="font-medium">{costStats.totalRequests}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Avg Cost</span>
                <p className="font-medium">{formatCurrency(costStats.averageCostPerRequest)}</p>
              </div>
            </div>

            {/* Recent Transactions */}
            {costStats.recentTransactions.length > 0 && (
              <div className="space-y-2">
                <span className="text-sm text-muted-foreground">Recent Operations</span>
                <div className="space-y-1 max-h-32 overflow-y-auto">
                  {costStats.recentTransactions.slice(0, 5).map((transaction, index) => (
                    <div key={index} className="flex justify-between text-xs p-2 bg-muted/50 rounded">
                      <div className="flex items-center gap-2">
                        <Badge variant={transaction.success ? "default" : "destructive"} className="text-xs px-1">
                          {transaction.modelId}
                        </Badge>
                      </div>
                      <span className="font-medium">
                        {formatCurrency(transaction.cost)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {!costStats && !isLoading && (
          <div className="text-center py-4 text-muted-foreground">
            <p className="text-sm">No cost data available</p>
            <p className="text-xs">Perform an OCR operation to see cost tracking</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}