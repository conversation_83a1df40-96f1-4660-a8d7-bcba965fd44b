import { NextRequest, NextResponse } from 'next/server'

const BACKEND_URL = process.env.BACKEND_PORT 
  ? `http://localhost:${process.env.BACKEND_PORT}` 
  : 'http://localhost:3003'

export async function GET(request: NextRequest) {
  try {
    // Forward the request to the backend
    const response = await fetch(`${BACKEND_URL}/api/settings/openrouter`, {
      method: 'GET',
      headers: {
        'User-Agent': request.headers.get('User-Agent') || '',
      },
    })

    // Get response data
    const data = await response.text()
    
    // Return response with same status and headers
    return new NextResponse(data, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        'Content-Type': 'application/json',
      },
    })
    
  } catch (error) {
    console.error('API Proxy Error (settings/openrouter GET):', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Proxy error: Failed to forward request to backend',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.text()
    
    // Forward the request to the backend
    const response = await fetch(`${BACKEND_URL}/api/settings/openrouter`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': request.headers.get('User-Agent') || '',
      },
      body: body,
    })

    // Get response data
    const data = await response.text()
    
    // Return response with same status and headers
    return new NextResponse(data, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        'Content-Type': 'application/json',
      },
    })
    
  } catch (error) {
    console.error('API Proxy Error (settings/openrouter POST):', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Proxy error: Failed to forward request to backend',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    )
  }
}