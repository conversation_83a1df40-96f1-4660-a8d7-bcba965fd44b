import { test, expect } from '@playwright/test';

// Specific test for hover preview implementation
test.describe('Hover Preview Specific Test', () => {
  test('should test group-hover Tailwind functionality', async ({ page }) => {
    await page.goto('http://localhost:3001/ocr-testing');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Find the first image container with the group class
    const groupContainer = page.locator('.group').first();
    
    if (await groupContainer.isVisible()) {
      console.log('✅ Found group container');
      
      // Check if the container has the group class
      const hasGroupClass = await groupContainer.evaluate((el) => el.classList.contains('group'));
      console.log('Has group class:', hasGroupClass);
      
      // Find the hover preview element inside this container
      const hoverPreview = groupContainer.locator('.group-hover\\:block');
      const previewExists = await hoverPreview.count();
      console.log(`Found ${previewExists} hover preview elements`);
      
      if (previewExists > 0) {
        // Check if the preview is initially hidden
        const isInitiallyHidden = await hoverPreview.first().isHidden();
        console.log('Preview initially hidden:', isInitiallyHidden);
        
        // Check the computed style before hover
        const initialDisplay = await hoverPreview.first().evaluate((el) => {
          return window.getComputedStyle(el).display;
        });
        console.log('Initial display style:', initialDisplay);
        
        // Take screenshot before hover
        await page.screenshot({ path: 'hover-specific-before.png' });
        
        // Hover over the group container
        await groupContainer.hover();
        console.log('✅ Hovered over group container');
        
        // Wait for CSS transition
        await page.waitForTimeout(1000);
        
        // Take screenshot after hover
        await page.screenshot({ path: 'hover-specific-after.png' });
        
        // Check if the preview is now visible
        const isVisibleAfterHover = await hoverPreview.first().isVisible();
        console.log('Preview visible after hover:', isVisibleAfterHover);
        
        // Check the computed style after hover
        const afterHoverDisplay = await hoverPreview.first().evaluate((el) => {
          return window.getComputedStyle(el).display;
        });
        console.log('After hover display style:', afterHoverDisplay);
        
        // Check if the preview image inside is loading
        const previewImage = hoverPreview.first().locator('img');
        if (await previewImage.isVisible()) {
          const previewSrc = await previewImage.getAttribute('src');
          console.log('Preview image src:', previewSrc);
          
          // Test if the preview image loads
          try {
            const naturalWidth = await previewImage.evaluate((img: HTMLImageElement) => img.naturalWidth);
            const naturalHeight = await previewImage.evaluate((img: HTMLImageElement) => img.naturalHeight);
            console.log(`Preview image dimensions: ${naturalWidth}x${naturalHeight}`);
            
            if (naturalWidth > 0 && naturalHeight > 0) {
              console.log('✅ Preview image loaded successfully');
            } else {
              console.log('❌ Preview image failed to load');
            }
          } catch (error) {
            console.log('❌ Preview image evaluation error:', (error as Error).message);
          }
        } else {
          console.log('❌ Preview image not visible');
        }
        
        // Move mouse away to test hover out
        await page.mouse.move(0, 0);
        await page.waitForTimeout(1000);
        
        // Check if preview is hidden again
        const isHiddenAfterHoverOut = await hoverPreview.first().isHidden();
        console.log('Preview hidden after hover out:', isHiddenAfterHoverOut);
        
        // Take screenshot after hover out
        await page.screenshot({ path: 'hover-specific-after-out.png' });
        
      } else {
        console.log('❌ No hover preview elements found');
      }
    } else {
      console.log('❌ No group container found');
    }
  });
  
  test('should test CSS selector specificity', async ({ page }) => {
    await page.goto('http://localhost:3001/ocr-testing');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Test if Tailwind CSS is working by checking for specific classes
    const hasHiddenClass = await page.locator('.hidden').count();
    console.log(`Found ${hasHiddenClass} elements with .hidden class`);
    
    const hasGroupHoverClass = await page.locator('[class*="group-hover"]').count();
    console.log(`Found ${hasGroupHoverClass} elements with group-hover classes`);
    
    // Test direct CSS hover using :hover selector
    const hoverableElements = await page.locator(':hover').count();
    console.log(`Found ${hoverableElements} elements in hover state`);
    
    // Check if the page has Tailwind CSS loaded
    const tailwindCheck = await page.evaluate(() => {
      const styles = Array.from(document.styleSheets);
      return styles.some(sheet => {
        try {
          const rules = Array.from(sheet.cssRules || []);
          return rules.some(rule => rule.cssText.includes('group-hover') || rule.cssText.includes('hidden'));
        } catch (e) {
          return false;
        }
      });
    });
    console.log('Tailwind CSS loaded:', tailwindCheck);
  });
  
  test('should test manual hover with JavaScript', async ({ page }) => {
    await page.goto('http://localhost:3001/ocr-testing');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Find the first group container
    const groupContainer = page.locator('.group').first();
    
    if (await groupContainer.isVisible()) {
      // Get the preview element
      const hoverPreview = groupContainer.locator('.group-hover\\:block').first();
      
      if (await hoverPreview.count() > 0) {
        // Manually trigger hover using JavaScript
        await groupContainer.evaluate((el) => {
          el.dispatchEvent(new MouseEvent('mouseenter', { bubbles: true }));
        });
        
        await page.waitForTimeout(1000);
        
        // Check if preview is visible
        const isVisible = await hoverPreview.isVisible();
        console.log('Preview visible after JS hover:', isVisible);
        
        // Try to force show the preview by removing hidden class
        await hoverPreview.evaluate((el) => {
          el.classList.remove('hidden');
          el.classList.add('block');
        });
        
        await page.waitForTimeout(1000);
        
        const isVisibleAfterForce = await hoverPreview.isVisible();
        console.log('Preview visible after force show:', isVisibleAfterForce);
        
        // Take screenshot after force show
        await page.screenshot({ path: 'hover-force-show.png' });
        
        // Check the preview image
        const previewImage = hoverPreview.locator('img');
        if (await previewImage.isVisible()) {
          const previewSrc = await previewImage.getAttribute('src');
          console.log('Forced preview image src:', previewSrc);
          
          // Test direct URL access
          if (previewSrc) {
            try {
              const response = await page.request.get(previewSrc);
              console.log(`Preview URL ${previewSrc}: ${response.status()}`);
            } catch (error) {
              console.log(`Preview URL error: ${(error as Error).message}`);
            }
          }
        }
      }
    }
  });
});