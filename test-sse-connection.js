const EventSource = require('eventsource');

// Test SSE connection to Smart Analyzer
console.log('🔧 Testing SSE connection to Smart Analyzer...');

// First, test if we can start a job (will fail because no images, but should return job ID)
fetch('http://localhost:3033/api/smart-analyzer/analyze', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    folderPath: 'C:/claude/dl-organizer' // Use project root which should have some files
  })
})
.then(response => response.json())
.then(data => {
  console.log('📊 Analysis response:', data);
  
  if (data.jobId) {
    console.log(`🔗 Testing SSE stream for job: ${data.jobId}`);
    
    // Test SSE connection
    const eventSource = new EventSource(`http://localhost:3033/api/smart-analyzer/stream/${data.jobId}`);
    
    eventSource.onopen = () => {
      console.log('✅ SSE connection opened successfully');
    };
    
    eventSource.onmessage = (event) => {
      console.log('📨 Received SSE message:', event.data);
      const data = JSON.parse(event.data);
      
      if (data.done) {
        console.log('🏁 Job completed, closing connection');
        eventSource.close();
      }
    };
    
    eventSource.onerror = (error) => {
      console.error('❌ SSE error:', error);
      eventSource.close();
    };
    
    // Close after 10 seconds regardless
    setTimeout(() => {
      console.log('⏰ Closing connection after timeout');
      eventSource.close();
    }, 10000);
    
  } else {
    console.log('❌ No job ID returned, cannot test SSE');
  }
})
.catch(error => {
  console.error('❌ Failed to start analysis job:', error);
});