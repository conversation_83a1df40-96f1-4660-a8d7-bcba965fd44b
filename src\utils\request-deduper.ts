/**
 * Request Deduplication Utility
 * Prevents duplicate API requests from being made simultaneously
 */

interface RequestCache {
  [key: string]: Promise<any>;
}

interface ThrottleCache {
  [key: string]: {
    lastCall: number;
    data: any;
  };
}

class RequestDeduper {
  private static pendingRequests: RequestCache = {};
  private static throttleCache: ThrottleCache = {};
  private static readonly THROTTLE_MS = 100; // 100ms throttle

  /**
   * Deduplicate identical requests to prevent React Strict Mode double-calling
   */
  static async dedupe<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    // If there's already a pending request with this key, return it
    if (key in this.pendingRequests) {
      console.log(`🔄 Deduplicating request: ${key}`);
      return this.pendingRequests[key];
    }

    // Create the request and cache it
    const request = requestFn()
      .finally(() => {
        // Clean up when done
        delete this.pendingRequests[key];
      });

    this.pendingRequests[key] = request;
    return request;
  }

  /**
   * Throttle requests to prevent spam
   */
  static async throttle<T>(key: string, requestFn: () => Promise<T>, throttleMs: number = this.THROTTLE_MS): Promise<T> {
    const now = Date.now();
    const cached = this.throttleCache[key];

    // If we have recent data, return it
    if (cached && (now - cached.lastCall) < throttleMs) {
      console.log(`⏱️ Throttling request: ${key} (${now - cached.lastCall}ms ago)`);
      return cached.data;
    }

    // Make the request
    const data = await requestFn();

    // Cache the result
    this.throttleCache[key] = {
      lastCall: now,
      data
    };

    return data;
  }

  /**
   * Combine deduplication and throttling
   */
  static async dedupeAndThrottle<T>(key: string, requestFn: () => Promise<T>, throttleMs: number = this.THROTTLE_MS): Promise<T> {
    return this.dedupe(key, () => this.throttle(key, requestFn, throttleMs));
  }

  /**
   * Special handling for fetch requests to avoid "body stream already read" errors
   */
  static async dedupeFetch(key: string, requestFn: () => Promise<Response>): Promise<Response> {
    // If there's already a pending request with this key, clone the response
    if (key in this.pendingRequests) {
      console.log(`🔄 Deduplicating fetch request: ${key}`);
      const originalResponse = await this.pendingRequests[key];
      
      // Clone the response to avoid "body stream already read" errors
      if (originalResponse instanceof Response) {
        return originalResponse.clone();
      }
      return originalResponse;
    }

    // Create the request and cache it
    const request = requestFn()
      .finally(() => {
        // Clean up when done
        delete this.pendingRequests[key];
      });

    this.pendingRequests[key] = request;
    return request;
  }

  /**
   * Clear cache (useful for testing or forced refresh)
   */
  static clearCache() {
    this.pendingRequests = {};
    this.throttleCache = {};
    console.log('🧹 Request cache cleared');
  }

  /**
   * Get cache status for debugging
   */
  static getCacheStatus() {
    return {
      pendingRequests: Object.keys(this.pendingRequests).length,
      throttleCache: Object.keys(this.throttleCache).length,
      pendingKeys: Object.keys(this.pendingRequests),
      throttleKeys: Object.keys(this.throttleCache)
    };
  }
}

export default RequestDeduper;

// Export convenience functions
export const dedupeRequest = RequestDeduper.dedupe.bind(RequestDeduper);
export const throttleRequest = RequestDeduper.throttle.bind(RequestDeduper);
export const dedupeAndThrottleRequest = RequestDeduper.dedupeAndThrottle.bind(RequestDeduper);
export const dedupeFetch = RequestDeduper.dedupeFetch.bind(RequestDeduper);