import { render, screen, fireEvent } from '@testing-library/react';
import SmartChips from '../src/components/dl-organizer/smart-chips';
import { AnalyzerManifest } from '../src/hooks/useSmartAnalyzer';

describe('SmartChips Select All Visible', () => {
  test('calls onSelectMatchingImages with select-visible filter', () => {
    const manifest: AnalyzerManifest = {
      sideCounts: { front: 0, back: 0, selfie: 0, unknown: 0 },
      filenameClusters: [],
      sizeBuckets: [{ label: '< 100KB', count: 1 }],
      resBuckets: [],
      cachedPct: 0,
      totalImages: 0,
      imageIds: [],
      imageSides: {},
      smartSelection: {
        totalGroups: 0,
        selectedImages: [],
        duplicatesFound: 0,
        potentialSavings: 0
      },
      generatedAt: new Date().toISOString(),
      version: '1.0.0'
    };

    const handleSelect = jest.fn();

    render(
      <SmartChips
        manifest={manifest}
        onAddFilter={() => {}}
        onSelectMatchingImages={handleSelect}
      />
    );

    fireEvent.click(screen.getByText('Select Visible'));
    expect(handleSelect).toHaveBeenCalledWith({ kind: 'select-visible' });
  });
});
