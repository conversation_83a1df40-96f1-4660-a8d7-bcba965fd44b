'use client'

import React, { useState, useCallback } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Checkbox } from '@/components/ui/checkbox'
import { AlertTriangle, Search, CheckCircle, Clock, ExternalLink, Eye, EyeOff, ChevronDown, ChevronRight, MapPin, Hash, Users, Copy, Check } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { OCRResult, AustralianOCRResult } from '@/types'
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'

interface ReadySearchResult {
  name: string
  matchType: 'EXACT MATCH' | 'PARTIAL MATCH' | 'SIMILAR'
  confidence: number
}

interface ReadySearchData {
  searchQuery: string
  matches: ReadySearchResult[]
  summary: string
  totalResults: number
  hasMatches: boolean
  rawOutput: string
  timestamp: string
  error?: string
}

interface ReadySearchPanelProps {
  ocrResult: OCRResult | null
  imageId: string | null
  onSearch?: (results: ReadySearchData) => void
}

export function ReadySearchPanel({ ocrResult, imageId, onSearch }: ReadySearchPanelProps) {
  const [isSearching, setIsSearching] = useState(false)
  const [searchResults, setSearchResults] = useState<ReadySearchData | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [showRawOutput, setShowRawOutput] = useState(false)
  const [showDetailedResults, setShowDetailedResults] = useState(false)
  const [copied, setCopied] = useState(false)
  
  // Search query customization options for Australian IDs
  const [useFullGivenNames, setUseFullGivenNames] = useState(false)
  const [includeBirthYear, setIncludeBirthYear] = useState(true)

  // Check if we have the required OCR data for search
  const canSearch = ocrResult && 
    ocrResult.firstName && 
    ocrResult.lastName && 
    ocrResult.dateOfBirth

  // Extract year from date of birth
  const getYearOfBirth = React.useCallback(() => {
    if (!ocrResult?.dateOfBirth) return null
    try {
      // Handle various date formats (YYYY-MM-DD, DD/MM/YYYY, etc.)
      const year = ocrResult.dateOfBirth.match(/\d{4}/)?.[0]
      return year || null
    } catch {
      return null
    }
  }, [ocrResult?.dateOfBirth])

  // Build search query based on checkbox options
  const buildSearchQuery = React.useCallback(() => {
    if (!ocrResult) return ''
    
    let namesPart = ''
    
    // Determine if we're dealing with an Australian OCR result
    const isAustralian = ocrResult.mode === 'australian'
    
    if (isAustralian && useFullGivenNames) {
      // For Australian IDs, construct full given names from available data
      // The OCR service should provide givenNames field or we construct it from firstName + middleName
      const australianResult = ocrResult as unknown as AustralianOCRResult
      let fullGivenNames = ''
      
      if (australianResult.givenNames) {
        // Direct givenNames field available
        fullGivenNames = australianResult.givenNames
      } else if (ocrResult.firstName && ocrResult.middleName) {
        // Construct from firstName + middleName
        fullGivenNames = `${ocrResult.firstName} ${ocrResult.middleName}`.trim()
      } else if (ocrResult.firstName) {
        // Just firstName available
        fullGivenNames = ocrResult.firstName
      }
      
      if (fullGivenNames) {
        namesPart = `${fullGivenNames} ${ocrResult.lastName}`
      } else {
        // Final fallback to first + last name
        namesPart = `${ocrResult.firstName} ${ocrResult.lastName}`
      }
    } else {
      // Use first + last name (standard behavior)
      namesPart = `${ocrResult.firstName} ${ocrResult.lastName}`
    }
    
    // Add birth year if enabled
    if (includeBirthYear) {
      const yearOfBirth = getYearOfBirth()
      if (yearOfBirth) {
        return `${namesPart}, ${yearOfBirth}`
      }
    }
    
    return namesPart
  }, [ocrResult, useFullGivenNames, includeBirthYear, getYearOfBirth])

  const handleSearch = useCallback(async () => {
    if (!canSearch || !imageId) return

    setIsSearching(true)
    setError(null)

    try {
      const yearOfBirth = includeBirthYear ? getYearOfBirth() : null
      
      // Only require birth year if the checkbox is enabled
      if (includeBirthYear && !yearOfBirth) {
        throw new Error('Could not extract year of birth from OCR results')
      }

      // Determine names to send based on checkbox options
      let firstName = ocrResult?.firstName
      let lastName = ocrResult?.lastName
      
      const isAustralian = ocrResult?.mode === 'australian'
      
      if (isAustralian && useFullGivenNames) {
        const australianResult = ocrResult as unknown as AustralianOCRResult
        let fullGivenNames = ''
        
        if (australianResult.givenNames) {
          // Direct givenNames field available
          fullGivenNames = australianResult.givenNames
        } else if (ocrResult?.firstName && ocrResult?.middleName) {
          // Construct from firstName + middleName
          fullGivenNames = `${ocrResult.firstName} ${ocrResult.middleName}`.trim()
        } else if (ocrResult?.firstName) {
          // Just firstName available
          fullGivenNames = ocrResult.firstName
        }
        
        if (fullGivenNames) {
          firstName = fullGivenNames
        }
      }

      console.log('Starting ReadySearch for:', {
        firstName,
        lastName,
        yearOfBirth: yearOfBirth || 'not included',
        fullGivenNames: useFullGivenNames,
        includeBirthYear
      })

      const response = await fetch('/api/readysearch/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName,
          lastName,
          yearOfBirth,
          imageId
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        const message = errorData.details ? `${errorData.error}: ${errorData.details}` : (errorData.error || 'ReadySearch failed')
        throw new Error(message)
      }

      const result = await response.json()
      
      if (result.success) {
        console.log('ReadySearch completed successfully:', result.data)
        setSearchResults(result.data.results)
        onSearch?.(result.data.results)
      } else {
        throw new Error(result.error || 'ReadySearch returned unsuccessful result')
      }

    } catch (err) {
      console.error('ReadySearch error:', err)
      setError(err instanceof Error ? err.message : 'ReadySearch failed')
    } finally {
      setIsSearching(false)
    }
  }, [canSearch, imageId, ocrResult, onSearch, getYearOfBirth, useFullGivenNames, includeBirthYear])

  const loadExistingResults = React.useCallback(async () => {
    if (!imageId) return

    try {
      const response = await fetch(`/api/readysearch/results/${imageId}`)
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          console.log('Loaded existing ReadySearch results:', result.data)
          setSearchResults(result.data)
        }
      }
    } catch (error) {
      console.log('No existing ReadySearch results found')
    }
  }, [imageId])

  // Load existing ReadySearch results when component mounts or imageId changes
  React.useEffect(() => {
    if (imageId) {
      loadExistingResults()
    }
  }, [imageId, loadExistingResults])

  const getMatchBadgeVariant = (matchType: string) => {
    switch (matchType) {
      case 'EXACT MATCH':
        return 'default' // Green
      case 'PARTIAL MATCH':
        return 'secondary' // Yellow
      case 'SIMILAR':
        return 'outline' // Gray
      default:
        return 'outline'
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.9) return 'text-green-600 dark:text-green-400 font-bold'
    if (confidence >= 0.7) return 'text-orange-600 dark:text-orange-400 font-bold'
    return 'text-red-600 dark:text-red-400 font-bold'
  }

  // Generate intelligent summary based on results
  const getIntelligentSummary = (results: ReadySearchData) => {
    if (results.hasMatches) {
      const exactMatches = results.matches.filter(m => m.matchType === 'EXACT MATCH').length
      const partialMatches = results.matches.filter(m => m.matchType === 'PARTIAL MATCH').length
      const similarMatches = results.matches.filter(m => m.matchType === 'SIMILAR').length
      
      let summary = `Found ${results.matches.length} match${results.matches.length !== 1 ? 'es' : ''}`
      if (results.totalResults > results.matches.length) {
        summary += ` out of ${results.totalResults} total results`
      }
      
      const breakdowns = []
      if (exactMatches > 0) breakdowns.push(`${exactMatches} exact`)
      if (partialMatches > 0) breakdowns.push(`${partialMatches} partial`)
      if (similarMatches > 0) breakdowns.push(`${similarMatches} similar`)
      
      if (breakdowns.length > 0) {
        summary += ` (${breakdowns.join(', ')})`
      }
      
      return summary
    } else {
      return 'Search completed - no matching records found in the database'
    }
  }

  // Copy to clipboard functionality
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  // Get result card styling based on match status - improved contrast and legibility
  const getResultCardStyle = (hasMatches: boolean) => {
    if (hasMatches) {
      return {
        container: 'bg-green-50/80 dark:bg-green-950/20 border-2 border-green-300 dark:border-green-700',
        badge: 'bg-green-600 text-white font-semibold px-3 py-1 dark:bg-green-700 dark:text-green-50'
      }
    } else {
      return {
        container: 'bg-red-50/80 dark:bg-red-950/20 border-2 border-red-300 dark:border-red-700',
        badge: 'bg-red-600 text-white font-semibold px-3 py-1 dark:bg-red-700 dark:text-red-50'
      }
    }
  }

  return (
    <Card className="w-full readysearch-panel" style={{color: 'var(--foreground)'}}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-gray-900 dark:text-gray-100" style={{color: 'inherit'}}>
          <Search className="h-5 w-5" />
          ReadySearch Integration
        </CardTitle>
        <CardDescription className="text-gray-700 dark:text-gray-300" style={{color: 'inherit'}}>
          Search for the subject using Australian driver&apos;s license data
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search trigger section */}
        {!canSearch && (
          <div className="flex items-center gap-3 p-4 bg-yellow-50 dark:bg-yellow-950/30 border-2 border-yellow-300 dark:border-yellow-600 rounded-lg">
            <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 flex-shrink-0" />
            <span className="text-sm font-semibold text-yellow-800 dark:text-yellow-200">
              OCR analysis required: First name, last name, and date of birth needed for search
            </span>
          </div>
        )}

        {canSearch && (
          <div className="space-y-3">
            {/* Theme-neutral styling for search query wrapper */}
            <div className="space-y-4">
              {/* Search query customization checkboxes - only show for Australian IDs */}
              {ocrResult?.mode === 'australian' && (
                <div className="p-4 rounded-lg bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800">
                  <div className="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-3">Search Options</div>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="fullGivenNames"
                        checked={useFullGivenNames}
                        onCheckedChange={setUseFullGivenNames}
                        className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                      />
                      <label htmlFor="fullGivenNames" className="text-sm font-medium text-blue-900 dark:text-blue-100 cursor-pointer">
                        Use full given names
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeBirthYear"
                        checked={includeBirthYear}
                        onCheckedChange={setIncludeBirthYear}
                        className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                      />
                      <label htmlFor="includeBirthYear" className="text-sm font-medium text-blue-900 dark:text-blue-100 cursor-pointer">
                        Include birth year
                      </label>
                    </div>
                  </div>
                </div>
              )}
              
              <div className="p-4 rounded-lg bg-muted/40 border border-border">
                <div className="text-sm font-semibold text-muted-foreground uppercase tracking-wide mb-2">Search Query</div>
                <div className="text-base font-mono font-semibold text-foreground bg-background px-4 py-2 rounded-md border border-border">
                  {buildSearchQuery()}
                </div>
              </div>
            </div>

            <Button 
              onClick={handleSearch}
              disabled={isSearching}
              variant="info"
              className="w-full"
            >
              {isSearching ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Searching...
                </>
              ) : (
                <>
                  <Search className="h-4 w-4 mr-2" />
                  Search ReadySearch Database
                </>
              )}
            </Button>
          </div>
        )}

        {/* Error display */}
        {error && (
          <Alert variant="destructive" className="readysearch-error bg-destructive/10 border-destructive/50">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="font-medium">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Enhanced Search Results Display */}
        {searchResults && !isSearching && (
          <div className="space-y-4">
            <Separator />
            
            {/* Results Summary Card with Color Coding */}
            <Card className={`readysearch-results ${getResultCardStyle(searchResults.hasMatches).container} transition-all duration-200`}>
              <CardContent className="p-4">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-4">
                  <div className="flex items-center gap-3 flex-shrink-0">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">Search Results</h3>
                    {searchResults.hasMatches ? (
                      <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
                    ) : (
                      <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
                    )}
                  </div>
                  <div className="flex flex-col xs:flex-row items-start xs:items-center gap-2 min-w-0">
                    <Badge className={`${getResultCardStyle(searchResults.hasMatches).badge} flex-shrink-0`}>
                      {searchResults.hasMatches ? "Matches Found" : "No Matches"}
                    </Badge>
                    {searchResults.timestamp && (
                      <span className="text-xs font-semibold text-gray-600 dark:text-gray-400 truncate">
                        {new Date(searchResults.timestamp).toLocaleString()}
                      </span>
                    )}
                  </div>
                </div>

                {/* Intelligent Summary */}
                <div className="mb-4">
                  <div className="text-sm font-bold text-gray-900 dark:text-gray-100 mb-2">Summary</div>
                  <div className="text-sm font-semibold text-gray-700 dark:text-gray-300 leading-relaxed">{getIntelligentSummary(searchResults)}</div>
                </div>

                {/* Statistics Row */}
                <div className="flex flex-wrap items-center gap-4 sm:gap-6 text-sm font-bold text-gray-600 dark:text-gray-400 mb-4">
                  <div className="flex items-center gap-2 flex-shrink-0">
                    <Users className="h-4 w-4" />
                    <span>Total: {searchResults.totalResults || 0}</span>
                  </div>
                  <div className="flex items-center gap-2 flex-shrink-0">
                    <Hash className="h-4 w-4" />
                    <span>Displayed: {searchResults.matches?.length || 0}</span>
                  </div>
                </div>

                {/* Detailed Results Dropdown */}
                <div className="space-y-3">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="w-full"
                    onClick={() => setShowDetailedResults(!showDetailedResults)}
                  >
                    {showDetailedResults ? (
                      <>
                        <ChevronDown className="h-4 w-4 mr-2" />
                        Hide Detailed Results
                      </>
                    ) : (
                      <>
                        <ChevronRight className="h-4 w-4 mr-2" />
                        Show Detailed Results
                      </>
                    )}
                  </Button>
                  
                  {showDetailedResults && (
                    <div className="space-y-3 animate-in slide-in-from-top-2 duration-200">
                    {/* Individual Matches */}
                    {searchResults.matches && searchResults.matches.length > 0 && (
                      <div className="space-y-2">
                        <div className="text-base font-bold flex items-center gap-2 text-gray-900 dark:text-gray-100">
                          <Users className="h-5 w-5" />
                          Individual Matches ({searchResults.matches.length})
                        </div>
                        <ScrollArea className="max-h-64">
                          <div className="space-y-2">
                            {searchResults.matches.map((match, index) => (
                              <div key={index} className="p-4 border-2 border-border rounded-lg bg-card shadow-sm">
                                <div className="flex items-center justify-between mb-3">
                                  <div className="flex items-center gap-2">
                                    <MapPin className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                                    <span className="font-bold text-lg text-gray-900 dark:text-gray-100">{match.name}</span>
                                  </div>
                                  <Badge variant={getMatchBadgeVariant(match.matchType)} className="text-xs">
                                    {match.matchType}
                                  </Badge>
                                </div>
                                <div className="flex items-center justify-between text-sm">
                                  <span className="font-bold text-gray-700 dark:text-gray-300">Confidence Score:</span>
                                  <div className="flex items-center gap-2">
                                    <div className="w-16 bg-muted rounded-full h-1">
                                      <div 
                                        className={`h-1 rounded-full transition-all duration-300 ${
                                          match.confidence >= 0.9 ? 'bg-green-500' : 
                                          match.confidence >= 0.7 ? 'bg-yellow-500' : 'bg-red-500'
                                        }`}
                                        style={{ width: `${match.confidence * 100}%` }}
                                      />
                                    </div>
                                    <span className={`font-medium ${getConfidenceColor(match.confidence)}`}>
                                      {(match.confidence * 100).toFixed(0)}%
                                    </span>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </ScrollArea>
                      </div>
                    )}

                    {/* No Matches Details */}
                    {(!searchResults.matches || searchResults.matches.length === 0) && (
                      <div className="p-6 text-center border-2 border-dashed border-border rounded-lg bg-muted/20">
                        <CheckCircle className="h-8 w-8 mx-auto mb-3 text-gray-500 dark:text-gray-400" />
                        <div className="text-base font-bold text-red-700 dark:text-red-400 mb-2">
                          No matching records found
                        </div>
                        <div className="text-sm font-semibold text-red-600 dark:text-red-500">
                          The search was completed successfully but found no records matching the search criteria
                        </div>
                      </div>
                    )}

                    {/* Raw Output Section */}
                    {searchResults.rawOutput && (
                      <div className="space-y-2">
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="w-full"
                          onClick={() => setShowRawOutput(!showRawOutput)}
                        >
                          {showRawOutput ? (
                            <>
                              <EyeOff className="h-4 w-4 mr-2" />
                              Hide Raw Output
                            </>
                          ) : (
                            <>
                              <Eye className="h-4 w-4 mr-2" />
                              Show Raw Output
                            </>
                          )}
                        </Button>
                        
                        {showRawOutput && (
                          <div className="animate-in slide-in-from-top-2 duration-200">
                            <div className="p-4 bg-muted/20 border-2 border-border rounded-lg">
                              <div className="flex flex-col gap-3 mb-4">
                                <div className="text-sm font-bold text-gray-900 dark:text-gray-100">
                                  Detailed Search Results (JSON)
                                </div>
                                <div className="flex items-center gap-2 flex-wrap">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => copyToClipboard(searchResults.rawOutput || 'No data available')}
                                    className="flex items-center justify-center h-8 w-8 p-0 flex-shrink-0"
                                    title={copied ? "Copied!" : "Copy to clipboard"}
                                  >
                                    {copied ? (
                                      <Check className="h-3 w-3" />
                                    ) : (
                                      <Copy className="h-3 w-3" />
                                    )}
                                  </Button>

                                  <Dialog>
                                    <DialogTrigger asChild>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="flex items-center justify-center gap-1 h-8 px-2 text-xs flex-shrink-0"
                                      >
                                        <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                        <span className="hidden sm:inline">Open Viewer</span>
                                        <span className="sm:hidden">View</span>
                                      </Button>
                                    </DialogTrigger>
                                    <DialogContent className="max-w-4xl w-[90vw] max-h-[90vh]">
                                      <DialogHeader>
                                        <DialogTitle>Detailed Search Results (JSON)</DialogTitle>
                                      </DialogHeader>
                                      <ScrollArea className="h-[70vh] w-full overflow-auto">
                                        <pre className="text-sm font-mono whitespace-pre overflow-auto bg-white dark:bg-gray-900 p-4 rounded border-2 border-gray-300 dark:border-gray-600 leading-relaxed select-text">
                                          {searchResults.rawOutput || 'No detailed results available - ensure ReadySearch API is properly connected'}
                                        </pre>
                                      </ScrollArea>
                                      <div className="pt-4 flex justify-end">
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          onClick={() => copyToClipboard(searchResults.rawOutput || 'No data available')}
                                          className="flex items-center gap-2 h-8 px-3"
                                        >
                                          {copied ? (
                                            <>
                                              <Check className="h-3 w-3" /> Copied!
                                            </>
                                          ) : (
                                            <>
                                              <Copy className="h-3 w-3" /> Copy All
                                            </>
                                          )}
                                        </Button>
                                      </div>
                                    </DialogContent>
                                  </Dialog>
                                </div>
                              </div>
                              <ScrollArea className="h-80 w-full overflow-auto">
                                <div className="min-w-max">
                                  <pre className="text-sm font-mono text-gray-800 dark:text-gray-200 whitespace-pre overflow-auto bg-white dark:bg-gray-900 p-4 rounded border-2 border-gray-300 dark:border-gray-600 leading-relaxed select-text">
                                    {searchResults.rawOutput || 'No detailed results available - ensure ReadySearch API is properly connected'}
                                  </pre>
                                </div>
                              </ScrollArea>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default ReadySearchPanel