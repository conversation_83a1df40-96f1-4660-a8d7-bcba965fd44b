const logger = require('../utils/logger');
const DatabaseManager = require('../config/database');

class HealthCheck {
  constructor() {
    this.checks = new Map();
    this.setupChecks();
  }
  
  setupChecks() {
    // Database health check
    this.checks.set('database', async () => {
      const dbManager = new DatabaseManager();
      try {
        await dbManager.initialize();
        const health = await dbManager.healthCheck();
        await dbManager.close();
        return health.status === 'healthy';
      } catch (error) {
        logger.error('Database health check failed', { error: error.message });
        return false;
      }
    });
    
    // File system health check
    this.checks.set('filesystem', async () => {
      const fs = require('fs').promises;
      const path = require('path');
      
      try {
        const testFile = path.join(__dirname, '../../data/health-check.tmp');
        await fs.writeFile(testFile, 'test');
        await fs.unlink(testFile);
        return true;
      } catch (error) {
        logger.error('File system health check failed', { error: error.message });
        return false;
      }
    });
    
    // Memory health check
    this.checks.set('memory', async () => {
      const used = process.memoryUsage();
      const totalMem = require('os').totalmem();
      const usedPercent = (used.heapUsed / totalMem) * 100;
      
      if (usedPercent > 80) {
        logger.warn('High memory usage detected', { 
          usedPercent: usedPercent.toFixed(2),
          heapUsed: Math.round(used.heapUsed / 1024 / 1024),
          heapTotal: Math.round(used.heapTotal / 1024 / 1024)
        });
        return false;
      }
      
      return true;
    });
    
    // Sharp library health check
    this.checks.set('sharp', async () => {
      try {
        const sharp = require('sharp');
        // Test basic Sharp functionality
        const testBuffer = Buffer.alloc(100);
        await sharp(testBuffer, { 
          raw: { width: 10, height: 10, channels: 1 } 
        }).jpeg().toBuffer();
        return true;
      } catch (error) {
        logger.error('Sharp health check failed', { error: error.message });
        return false;
      }
    });
  }
  
  async runCheck(name) {
    const check = this.checks.get(name);
    if (!check) {
      return { status: 'unknown', error: 'Check not found' };
    }
    
    try {
      const startTime = Date.now();
      const result = await check();
      const duration = Date.now() - startTime;
      
      return {
        status: result ? 'healthy' : 'unhealthy',
        duration,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
  
  async runAllChecks() {
    const results = {};
    const promises = Array.from(this.checks.keys()).map(async (name) => {
      results[name] = await this.runCheck(name);
    });
    
    await Promise.all(promises);
    
    const overallStatus = Object.values(results).every(r => r.status === 'healthy') 
      ? 'healthy' : 'unhealthy';
    
    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      checks: results,
      uptime: process.uptime(),
      version: require('../../package.json').version
    };
  }
  
  middleware() {
    return async (req, res) => {
      try {
        const health = await this.runAllChecks();
        const statusCode = health.status === 'healthy' ? 200 : 503;
        res.status(statusCode).json(health);
      } catch (error) {
        logger.error('Health check middleware error', { error: error.message });
        res.status(500).json({
          status: 'error',
          error: 'Health check failed',
          timestamp: new Date().toISOString()
        });
      }
    };
  }
}

module.exports = new HealthCheck();