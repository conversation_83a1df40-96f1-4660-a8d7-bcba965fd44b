"use client"

import { useState, useEffect, useCallback } from 'react'

export type OCRMode = 'us' | 'australian'
export type DocumentType = 'us_driver_license' | 'us_id_card' | 'us_passport' | 'australian_driver_license' | 'australian_id_card' | 'australian_passport' | 'auto_detect'

interface OCRModeState {
  mode: OCRMode
  documentType: DocumentType
  lastUpdated: number
}

const DEFAULT_STATE: OCRModeState = {
  mode: 'us',
  documentType: 'auto_detect',
  lastUpdated: Date.now()
}

const STORAGE_KEY = 'dlorganizer_ocr_mode_state'

export function useOCRMode() {
  const [state, setState] = useState<OCRModeState>(() => {
    if (typeof window === 'undefined') return DEFAULT_STATE
    
    try {
      const saved = localStorage.getItem(STORAGE_KEY)
      if (saved) {
        const parsed = JSON.parse(saved)
        return { ...DEFAULT_STATE, ...parsed }
      }
    } catch (error) {
      console.warn('Failed to load OCR mode state from localStorage:', error)
    }
    
    return DEFAULT_STATE
  })

  // Save to localStorage whenever state changes
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(state))
    } catch (error) {
      console.warn('Failed to save OCR mode state to localStorage:', error)
    }
  }, [state])

  const setMode = useCallback((mode: OCRMode) => {
    setState(prev => {
      // Always default to auto_detect when mode changes
      return {
        ...prev,
        mode,
        documentType: 'auto_detect',
        lastUpdated: Date.now()
      }
    })
  }, [])

  const setDocumentType = useCallback((documentType: DocumentType) => {
    setState(prev => ({
      ...prev,
      documentType,
      lastUpdated: Date.now()
    }))
  }, [])

  const reset = useCallback(() => {
    setState(DEFAULT_STATE)
  }, [])

  // Get all document type options - comprehensive list including US and AUS types
  const getDocumentTypeOptions = useCallback(() => {
    return [
      // Auto-detect first (default)
      { value: 'auto_detect', label: 'Auto-Detect', icon: '🤖' },
      // US document types
      { value: 'us_driver_license', label: 'US Driver License', icon: '🇺🇸' },
      { value: 'us_id_card', label: 'US ID Card', icon: '🇺🇸' },
      { value: 'us_passport', label: 'US Passport', icon: '🇺🇸' },
      // Australian document types
      { value: 'australian_driver_license', label: 'AUS Driver License', icon: '🇦🇺' },
      { value: 'australian_id_card', label: 'AUS ID Card', icon: '🇦🇺' },
      { value: 'australian_passport', label: 'AUS Passport', icon: '🇦🇺' }
    ]
  }, [])

  // Helper function to determine if document type is Australian
  const isAustralianDocumentType = useCallback((docType: DocumentType) => {
    return docType.startsWith('australian_')
  }, [])

  // Helper function to determine if document type is US
  const isUSDocumentType = useCallback((docType: DocumentType) => {
    return docType.startsWith('us_')
  }, [])

  // Determine if current document type is compatible with current mode
  const isDocumentTypeCompatible = useCallback(() => {
    const currentType = state.documentType
    const isUSType = currentType.startsWith('us_')
    const isAUSType = currentType.startsWith('australian_')
    
    if (state.mode === 'us') {
      return isUSType || currentType === 'auto_detect'
    } else {
      return isAUSType || currentType === 'auto_detect'
    }
  }, [state.mode, state.documentType])

  // Auto-correct document type if incompatible - default to auto_detect
  useEffect(() => {
    if (!isDocumentTypeCompatible()) {
      setDocumentType('auto_detect')
    }
  }, [state.mode, isDocumentTypeCompatible, setDocumentType])

  return {
    mode: state.mode,
    documentType: state.documentType,
    lastUpdated: state.lastUpdated,
    setMode,
    setDocumentType,
    reset,
    getDocumentTypeOptions,
    isDocumentTypeCompatible,
    isAustralianDocumentType,
    isUSDocumentType
  }
}

export default useOCRMode