// Default configuration for the application
const defaultConfig = {
  openRouter: {
    apiKey:
      "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", // Temporary API key for testing
    baseUrl: "https://openrouter.ai/api/v1",
    selectedModel: "meta-llama/llama-3.2-11b-vision-instruct:free",
    isEnabled: true,
    ocrMode: "driver_license",
    enableCostTracking: true,
    costLimit: 10.0,
    fallbackModels: [
      "meta-llama/llama-3.2-90b-vision-instruct:free",
      "qwen/qwen-2-vl-7b-instruct:free",
      "google/gemini-flash-1.5",
      "openai/gpt-4o-mini",
    ],
  },
};

module.exports = defaultConfig;
