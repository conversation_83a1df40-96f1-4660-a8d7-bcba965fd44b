"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Bot,
  Image as ImageIcon,
  CheckCircle2,
  Play,
  Pause,
  Square,
  FileText,
  Clock,
  DollarSign,
  Zap,
  Settings,
  RefreshCw,
  Search,
  Database,
} from "lucide-react";
import { ImageFile, OCRResult } from "@/types";
import OCRPanel from "./ocr-panel";
import Image from "next/image";
import { cn } from "@/lib/utils";

interface ProcessingModeTabsProps {
  images: ImageFile[];
  selectedImage: ImageFile | null;
  onImageSelect: (image: ImageFile) => void;
  onAnalyze: (imageId: string, modelId?: string) => void;
  ocrResult: OCRResult | null;
  isProcessing: boolean;
  onSave: (data: OCRResult) => void;
  onCancel: () => void;
  countryMode?: "us" | "australian";
  error?: string | null;
  successMessage?: string | null;
  className?: string;
  // New props for batch folder mode
  folderId?: string;
  folderPath?: string;
}

interface BatchProgress {
  total: number;
  completed: number;
  failed: number;
  currentImage?: string;
  currentStatus: "idle" | "processing" | "completed" | "paused";
  startTime?: Date;
  estimatedTime?: number;
  totalCost: number;
  readySearchProcessed?: number;
  readySearchFailed?: number;
}

interface ReadySearchOptions {
  enabled: boolean;
  useFullGivenNames: boolean;
  includeBirthYear: boolean;
  cachedResultsOnly: boolean;
}

export default function ProcessingModeTabs({
  images,
  selectedImage,
  onImageSelect,
  onAnalyze,
  ocrResult,
  isProcessing,
  onSave,
  onCancel,
  countryMode = "us",
  error,
  successMessage,
  className,
  folderId,
  folderPath,
}: ProcessingModeTabsProps) {
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [batchProgress, setBatchProgress] = useState<BatchProgress>({
    total: 0,
    completed: 0,
    failed: 0,
    currentStatus: "idle",
    totalCost: 0,
    readySearchProcessed: 0,
    readySearchFailed: 0,
  });
  const [selectedModel, setSelectedModel] = useState("gpt-4o-mini");
  const [readySearchOptions, setReadySearchOptions] =
    useState<ReadySearchOptions>({
      enabled: false,
      useFullGivenNames: false,
      includeBirthYear: true,
      cachedResultsOnly: false,
    });
  const [ocrResults, setOcrResults] = useState<OCRResult[]>([]);

  // New state for batch folder mode
  const [batchFolderImages, setBatchFolderImages] = useState<ImageFile[]>([]);
  const [batchProcessing, setBatchProcessing] = useState(false);

  // Handler for batch folder mode selection changes
  const handleBatchFolderSelectionChange = (selectedImages: ImageFile[]) => {
    setBatchFolderImages(selectedImages);
  };

  // Process batch folder mode OCR
  const processBatchFolderOCR = async () => {
    if (batchFolderImages.length === 0) return;

    setBatchProcessing(true);
    setBatchProgress({
      total: batchFolderImages.length,
      completed: 0,
      failed: 0,
      currentStatus: "processing",
      totalCost: 0,
      readySearchProcessed: 0,
      readySearchFailed: 0,
      startTime: new Date(),
    });

    try {
      const response = await fetch("/api/batch-ocr/process-batch-folders", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          images: batchFolderImages,
          mode: "auto-detect",
          regionContext: countryMode,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setBatchProgress((prev) => ({
          ...prev,
          currentStatus: "completed",
          completed: result.stats.successfulExtractions,
          failed: result.stats.failedExtractions,
          currentImage: `✅ Processed ${result.stats.processedImages} images`,
        }));
      } else {
        throw new Error("Batch processing failed");
      }
    } catch (error) {
      setBatchProgress((prev) => ({
        ...prev,
        currentStatus: "completed",
        currentImage: "❌ Batch processing failed",
        failed: batchFolderImages.length,
      }));
    } finally {
      setBatchProcessing(false);
    }
  };

  const handleBatchImageSelect = (imageId: string, selected: boolean) => {
    setSelectedImages((prev) =>
      selected ? [...prev, imageId] : prev.filter((id) => id !== imageId)
    );
  };

  const handleSelectAll = () => {
    if (selectedImages.length === images.length) {
      setSelectedImages([]);
    } else {
      setSelectedImages(images.map((img) => img.id));
    }
  };

  const handleBatchProcess = async () => {
    if (selectedImages.length === 0) return;

    const startTime = new Date();
    const totalSteps = readySearchOptions.enabled ? 2 : 1; // OCR + optionally ReadySearch

    setBatchProgress({
      total: selectedImages.length,
      completed: 0,
      failed: 0,
      currentStatus: "processing",
      startTime,
      totalCost: 0,
      readySearchProcessed: 0,
      readySearchFailed: 0,
    });

    try {
      // Step 1: OCR Processing
      setBatchProgress((prev) => ({
        ...prev,
        currentImage: "🔍 Starting batch OCR processing...",
      }));

      // Prepare batch request data
      const batchImages = selectedImages.map((imageId) => {
        const image = images.find((img) => img.id === imageId);
        return {
          id: imageId,
          filename: image?.filename || "Unknown",
          path: image?.path || "",
        };
      });

      console.log(
        "Starting batch OCR processing for",
        batchImages.length,
        "images"
      );

      // Call the batch OCR API
      const response = await fetch("/api/batch-ocr/process-images", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          images: batchImages,
          modelId: selectedModel || "gpt-4o-mini",
          extractionType:
            countryMode === "australian"
              ? "aus_driver_license"
              : "us_driver_license",
          mode: countryMode,
          exportFormats: ["json", "txt"],
          readySearchOptions: readySearchOptions.enabled
            ? {
                enabled: true,
                useFullGivenNames: readySearchOptions.useFullGivenNames,
                includeBirthYear: readySearchOptions.includeBirthYear,
              }
            : { enabled: false },
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `Batch processing failed (${response.status})`
        );
      }

      const result = await response.json();
      console.log("Batch OCR processing completed:", result);

      // Update final progress
      setBatchProgress((prev) => ({
        ...prev,
        currentStatus: "completed",
        completed: result.stats?.successfulExtractions || 0,
        failed: result.stats?.failedExtractions || 0,
        totalCost: result.stats?.totalCost || 0,
        currentImage: `✅ Processed ${
          result.stats?.processedImages || 0
        } images`,
      }));
    } catch (error) {
      console.error("Batch processing failed:", error);
      setBatchProgress((prev) => ({
        ...prev,
        currentStatus: "completed",
        currentImage: `❌ Batch processing failed: ${error.message}`,
        failed: selectedImages.length,
      }));
    }
  };

  // Function to collect OCR results from processed images
  const collectOcrResults = async (
    imageIds: string[]
  ): Promise<OCRResult[]> => {
    const results: OCRResult[] = [];

    for (const imageId of imageIds) {
      try {
        // Check if we have cached OCR results for this image
        const response = await fetch(`/api/images/${imageId}/cache`);
        if (response.ok) {
          const cacheData = await response.json();
          if (cacheData.success && cacheData.data) {
            results.push({
              ...cacheData.data,
              imageId: imageId,
              hasReadySearchCache: !!cacheData.data.readySearchResults,
            });
          }
        } else if (response.status === 404) {
          // If cachedResultsOnly is enabled, skip images without cache
          if (readySearchOptions.cachedResultsOnly) {
            console.log(
              `Skipping ${imageId} - no cached OCR results and cachedResultsOnly is enabled`
            );
            continue;
          }
          // Otherwise, try to get fresh results (this might not exist depending on implementation)
          console.log(`No cached OCR results found for ${imageId}`);
        }
      } catch (error) {
        console.warn(`Failed to collect OCR result for ${imageId}:`, error);
      }
    }

    return results;
  };

  // New function to handle batch ReadySearch processing
  const processBatchReadySearch = async (ocrResults: OCRResult[]) => {
    // Filter results based on options
    let filteredResults = ocrResults.filter(
      (result) =>
        result.firstName &&
        result.lastName &&
        result.dateOfBirth &&
        (!readySearchOptions.cachedResultsOnly || result.hasReadySearchCache)
    );

    if (filteredResults.length === 0) {
      setBatchProgress((prev) => ({
        ...prev,
        currentImage: "⚠️ No eligible images for ReadySearch",
        readySearchProcessed: 0,
      }));
      return;
    }

    setBatchProgress((prev) => ({
      ...prev,
      currentImage: `ReadySearch: Processing ${filteredResults.length} images...`,
    }));

    // Prepare search data according to ReadySearch API format
    const searchData = filteredResults.map((result) => {
      // Extract year from date of birth
      const yearMatch = result.dateOfBirth?.match(/\d{4}/);
      const yearOfBirth = yearMatch ? yearMatch[0] : "";

      // Format names based on options
      let firstName = result.firstName;
      let lastName = result.lastName;

      if (!readySearchOptions.useFullGivenNames) {
        // Use only first given name if not using full names
        firstName = firstName.split(" ")[0];
      }

      return {
        firstName,
        lastName,
        yearOfBirth: readySearchOptions.includeBirthYear ? yearOfBirth : "",
        imageId: result.imageId,
        cardSide: result.cardSide || "front",
      };
    });

    try {
      const response = await fetch(
        "http://localhost:3003/api/readysearch/batch",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ searchData }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Batch ReadySearch failed");
      }

      const result = await response.json();

      if (result.success) {
        setBatchProgress((prev) => ({
          ...prev,
          currentImage: `✅ ReadySearch: ${result.data.processedCount} processed, ${result.data.skippedCount} skipped`,
          readySearchProcessed: result.data.processedCount,
          readySearchFailed: result.data.results.filter((r: any) => !r.success)
            .length,
        }));
      } else {
        throw new Error(result.error || "ReadySearch batch processing failed");
      }
    } catch (error) {
      console.error("ReadySearch batch processing error:", error);
      setBatchProgress((prev) => ({
        ...prev,
        currentImage: `❌ ReadySearch failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
        readySearchFailed: filteredResults.length,
      }));
      throw error;
    }
  };

  const estimatedTime = selectedImages.length * 8; // ~8 seconds per image
  const estimatedCost = selectedImages.length * 0.02; // ~$0.02 per image

  return (
    <div className={cn("h-full", className)}>
      <Tabs defaultValue="single" className="h-full flex flex-col">
        <TabsList className="grid w-full grid-cols-2 mb-4">
          <TabsTrigger value="single" className="flex items-center gap-2">
            <ImageIcon className="h-4 w-4" />
            Single Image
          </TabsTrigger>
          <TabsTrigger value="batch" className="flex items-center gap-2">
            <Bot className="h-4 w-4" />
            Batch Processing
          </TabsTrigger>
        </TabsList>

        {/* Single Image Mode */}
        <TabsContent value="single" className="flex-1">
          <OCRPanel
            selectedImage={selectedImage}
            onAnalyze={onAnalyze}
            ocrResult={ocrResult}
            isProcessing={isProcessing}
            onSave={onSave}
            onCancel={onCancel}
            countryMode={countryMode}
            error={error}
            successMessage={successMessage}
            className="h-full"
          />
        </TabsContent>

        {/* Batch Processing Mode */}
        <TabsContent value="batch" className="flex-1 space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Batch OCR Processing</CardTitle>
                <Badge variant="outline">
                  {selectedImages.length} of {images.length} selected
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* ReadySearch Options */}
              {countryMode === "australian" && (
                <Card className="bg-muted/30">
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <Search className="h-4 w-4 text-blue-500" />
                      <CardTitle className="text-sm">
                        ReadySearch Integration
                      </CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="enableReadySearch"
                        checked={readySearchOptions.enabled}
                        onCheckedChange={(checked) =>
                          setReadySearchOptions((prev) => ({
                            ...prev,
                            enabled: !!checked,
                          }))
                        }
                      />
                      <label
                        htmlFor="enableReadySearch"
                        className="text-sm font-medium"
                      >
                        Run ReadySearch after OCR processing
                      </label>
                    </div>

                    {readySearchOptions.enabled && (
                      <div className="ml-6 space-y-2 border-l-2 border-blue-200 pl-4">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="useFullNames"
                            checked={readySearchOptions.useFullGivenNames}
                            onCheckedChange={(checked) =>
                              setReadySearchOptions((prev) => ({
                                ...prev,
                                useFullGivenNames: !!checked,
                              }))
                            }
                          />
                          <label htmlFor="useFullNames" className="text-xs">
                            Use full given names (not just first name)
                          </label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="includeBirthYear"
                            checked={readySearchOptions.includeBirthYear}
                            onCheckedChange={(checked) =>
                              setReadySearchOptions((prev) => ({
                                ...prev,
                                includeBirthYear: !!checked,
                              }))
                            }
                          />
                          <label htmlFor="includeBirthYear" className="text-xs">
                            Include birth year in search
                          </label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="cachedOnly"
                            checked={readySearchOptions.cachedResultsOnly}
                            onCheckedChange={(checked) =>
                              setReadySearchOptions((prev) => ({
                                ...prev,
                                cachedResultsOnly: !!checked,
                              }))
                            }
                          />
                          <label htmlFor="cachedOnly" className="text-xs">
                            <Database className="h-3 w-3 inline mr-1" />
                            Only process images with cached OCR results
                          </label>
                        </div>

                        <div className="text-xs text-muted-foreground bg-blue-50 dark:bg-blue-950/30 p-2 rounded">
                          <div className="font-medium mb-1">
                            ReadySearch will:
                          </div>
                          <div>• Search Australian driver license database</div>
                          <div>• Process only front-side license images</div>
                          <div>• Save results to image cache files</div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Batch Controls */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                  className="flex items-center gap-2"
                >
                  <CheckCircle2 className="h-4 w-4" />
                  {selectedImages.length === images.length
                    ? "Deselect All"
                    : "Select All"}
                </Button>

                {selectedImages.length > 0 && (
                  <Button
                    onClick={handleBatchProcess}
                    disabled={
                      isProcessing ||
                      batchProgress.currentStatus === "processing"
                    }
                    className="flex items-center gap-2"
                  >
                    <Play className="h-4 w-4" />
                    Process {selectedImages.length} Images
                  </Button>
                )}
              </div>

              {/* Batch Statistics */}
              {selectedImages.length > 0 && (
                <div className="grid grid-cols-2 gap-4 p-3 bg-muted/30 rounded-lg">
                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>Est. Time: {Math.ceil(estimatedTime / 60)}min</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                    <span>Est. Cost: ${estimatedCost.toFixed(2)}</span>
                  </div>
                </div>
              )}

              {/* Progress Indicator */}
              {batchProgress.currentStatus === "processing" && (
                <div className="space-y-3 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">
                      Processing Images...
                    </span>
                    <span className="text-sm text-muted-foreground">
                      {batchProgress.completed} / {batchProgress.total}
                    </span>
                  </div>

                  <Progress
                    value={
                      (batchProgress.completed / batchProgress.total) * 100
                    }
                    className="h-3"
                  />

                  {batchProgress.currentImage && (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        {!batchProgress.currentImage.includes("✅") &&
                          !batchProgress.currentImage.includes("❌") && (
                            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600" />
                          )}
                        <span
                          className={cn(
                            "font-mono text-xs",
                            batchProgress.currentImage.includes("✅") &&
                              "text-green-600",
                            batchProgress.currentImage.includes("❌") &&
                              "text-red-600"
                          )}
                        >
                          {batchProgress.currentImage}
                        </span>
                      </div>

                      {batchProgress.estimatedTime &&
                        batchProgress.estimatedTime > 0 && (
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            <span>
                              ~{batchProgress.estimatedTime}s remaining
                            </span>
                          </div>
                        )}
                    </div>
                  )}

                  <div
                    className={cn(
                      "grid gap-4 text-xs",
                      readySearchOptions.enabled ? "grid-cols-5" : "grid-cols-3"
                    )}
                  >
                    <div className="text-center">
                      <div className="font-medium text-green-600">
                        {batchProgress.completed}
                      </div>
                      <div className="text-muted-foreground">OCR Success</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-red-600">
                        {batchProgress.failed}
                      </div>
                      <div className="text-muted-foreground">OCR Failed</div>
                    </div>
                    {readySearchOptions.enabled && (
                      <>
                        <div className="text-center">
                          <div className="font-medium text-purple-600">
                            {batchProgress.readySearchProcessed || 0}
                          </div>
                          <div className="text-muted-foreground">RS Found</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium text-orange-600">
                            {batchProgress.readySearchFailed || 0}
                          </div>
                          <div className="text-muted-foreground">RS Failed</div>
                        </div>
                      </>
                    )}
                    <div className="text-center">
                      <div className="font-medium text-blue-600">
                        ${batchProgress.totalCost.toFixed(3)}
                      </div>
                      <div className="text-muted-foreground">Cost</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Completion Status */}
              {batchProgress.currentStatus === "completed" && (
                <div className="p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                  <div className="flex items-center gap-2 text-green-700 dark:text-green-300">
                    <CheckCircle2 className="h-5 w-5" />
                    <span className="font-medium">
                      {readySearchOptions.enabled
                        ? "OCR + ReadySearch Complete!"
                        : "Batch Processing Complete!"}
                    </span>
                  </div>
                  <div className="mt-2 text-sm text-green-600 dark:text-green-400">
                    <div>
                      OCR: {batchProgress.completed} successful,{" "}
                      {batchProgress.failed} failed
                    </div>
                    {readySearchOptions.enabled && (
                      <div>
                        ReadySearch: {batchProgress.readySearchProcessed || 0}{" "}
                        matches found, {batchProgress.readySearchFailed || 0}{" "}
                        failed
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Image Selection Grid */}
          <Card className="flex-1">
            <CardHeader className="pb-3">
              <CardTitle className="text-base">
                Select Images for Processing
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {images.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <ImageIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>No images in selected folder</p>
                  <p className="text-sm">Select a folder to see images</p>
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-3 max-h-96 overflow-y-auto">
                  {images.map((image) => (
                    <div
                      key={image.id}
                      className={cn(
                        "relative border rounded-lg p-2 transition-all hover:shadow-md cursor-pointer",
                        selectedImages.includes(image.id)
                          ? "border-primary bg-primary/5"
                          : "border-border hover:border-primary/50"
                      )}
                      onClick={() =>
                        handleBatchImageSelect(
                          image.id,
                          !selectedImages.includes(image.id)
                        )
                      }
                    >
                      <div className="absolute top-1 left-1 z-10">
                        <Checkbox
                          checked={selectedImages.includes(image.id)}
                          onCheckedChange={() => {}} // Handled by parent onClick
                          className="bg-background border-border shadow-sm"
                        />
                      </div>

                      <div
                        className="aspect-square relative overflow-hidden rounded"
                        style={{ position: "relative" }}
                      >
                        <Image
                          src={image.thumbnailUrl}
                          alt={image.filename}
                          fill
                          className="object-cover"
                        />
                      </div>

                      <div className="mt-1">
                        <p className="text-xs font-medium truncate">
                          {image.filename}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {Math.round(image.fileSize / 1024)}KB
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
