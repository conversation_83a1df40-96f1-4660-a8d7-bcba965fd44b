const express = require('express');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs').promises;
const logger = require('../utils/logger');
const SecurityMiddleware = require('../middleware/security');
const ErrorHandler = require('../middleware/error-handler');
const fsExistsSync = require('fs').existsSync;

function resolveReadySearchDir() {
  const candidates = [
    path.join(__dirname, '..', '..', 'ReadySearch'), // project root/ReadySearch
    path.join(process.cwd(), 'ReadySearch'),         // working dir
    path.join(__dirname, '..', 'ReadySearch')        // backend/ReadySearch (unlikely)
  ];
  for (const dir of candidates) {
    if (fsExistsSync(dir)) return dir;
  }
  // fallback to first candidate
  return candidates[0];
}

const router = express.Router();

// ReadySearch automation endpoint
router.post('/search', SecurityMiddleware.validateRequestBody(['firstName', 'lastName']), ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { firstName, lastName, yearOfBirth, imageId } = req.body;
    
    // Validate inputs
    const sanitizedFirstName = SecurityMiddleware.sanitizeText(firstName);
    const sanitizedLastName = SecurityMiddleware.sanitizeText(lastName);
    const sanitizedYear = yearOfBirth ? SecurityMiddleware.sanitizeText(yearOfBirth) : null;
    
    if (!sanitizedFirstName || !sanitizedLastName) {
      return res.status(400).json({ 
        success: false, 
        error: 'Invalid or missing required fields (firstName, lastName)' 
      });
    }
    
    // Format the search query as expected by ReadySearch
    // With year: "firstname lastname,year"  
    // Without year: "firstname lastname"
    const searchQuery = sanitizedYear 
      ? `${sanitizedFirstName} ${sanitizedLastName},${sanitizedYear}`
      : `${sanitizedFirstName} ${sanitizedLastName}`;
    
    logger.info('Executing ReadySearch automation', { 
      searchQuery, 
      imageId,
      requestId: req.id 
    });
    
    try {
      // Execute ReadySearch automation
      const searchResult = await executeReadySearch(searchQuery);
      
      // If imageId is provided, save the results using unified FileManager
      if (imageId) {
        await saveReadySearchToCache(imageId, searchResult);
        
        // Also update via FileManager for integrated saving
        try {
          const FileManager = require('../services/file-manager');
          const imagePath = Buffer.from(imageId, 'base64').toString();
          await FileManager.updateWithReadySearchResults(imagePath, searchResult);
          console.log('ReadySearch results integrated via FileManager');
        } catch (fmError) {
          logger.warn('Failed to update OCR results via FileManager', { 
            error: fmError.message, 
            imageId 
          });
        }
      }
      
      res.json({
        success: true,
        data: {
          searchQuery,
          results: searchResult,
          timestamp: new Date().toISOString()
        }
      });
      
    } catch (searchError) {
      logger.error('ReadySearch execution failed', { 
        error: searchError.message, 
        searchQuery,
        imageId 
      });
      
      res.status(500).json({
        success: false,
        error: 'ReadySearch automation failed',
        details: searchError.message
      });
    }
    
  } catch (error) {
    logger.error('Error in ReadySearch endpoint', { error: error.message });
    throw error;
  }
}));

// Batch ReadySearch automation endpoint for multiple images
router.post('/batch', SecurityMiddleware.validateRequestBody(['searchData']), ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { searchData } = req.body;
    
    // Validate searchData is an array of objects with required fields
    if (!Array.isArray(searchData) || searchData.length === 0) {
      return res.status(400).json({ 
        success: false, 
        error: 'searchData must be a non-empty array' 
      });
    }
    
    // Validate each search item
    const validatedSearchData = [];
    for (let i = 0; i < searchData.length; i++) {
      const item = searchData[i];
      if (!item.firstName || !item.lastName || !item.imageId) {
        return res.status(400).json({ 
          success: false, 
          error: `Invalid search item at index ${i}: missing required fields (firstName, lastName, imageId)` 
        });
      }
      
      // Sanitize inputs
      validatedSearchData.push({
        firstName: SecurityMiddleware.sanitizeText(item.firstName),
        lastName: SecurityMiddleware.sanitizeText(item.lastName),
        yearOfBirth: item.yearOfBirth ? SecurityMiddleware.sanitizeText(item.yearOfBirth) : null,
        imageId: item.imageId,
        cardSide: item.cardSide || 'unknown'
      });
    }
    
    // Filter out back-side cards for ReadySearch (only process front-side cards)
    const frontSideData = validatedSearchData.filter(item => 
      item.cardSide !== 'back' && 
      item.firstName.trim() !== '' && 
      item.lastName.trim() !== ''
    );
    
    if (frontSideData.length === 0) {
      return res.json({
        success: true,
        message: 'No front-side cards with valid data found for ReadySearch processing',
        processedCount: 0,
        skippedCount: validatedSearchData.length,
        results: []
      });
    }
    
    // Format the batch search query
    // With year: "firstname lastname,year;firstname2 lastname2,year2"  
    // Without year: "firstname lastname;firstname2 lastname2"
    const batchQuery = frontSideData.map(item => 
      item.yearOfBirth 
        ? `${item.firstName} ${item.lastName},${item.yearOfBirth}`
        : `${item.firstName} ${item.lastName}`
    ).join(';');
    
    logger.info('Executing ReadySearch batch automation', { 
      batchQuery: batchQuery.substring(0, 200) + (batchQuery.length > 200 ? '...' : ''),
      totalItems: frontSideData.length,
      skippedItems: validatedSearchData.length - frontSideData.length,
      requestId: req.id 
    });
    
    try {
      // Use optimized batch CLI for better performance (especially for 50+ items)
      const searchResults = await executeBatchReadySearch(batchQuery, frontSideData.length);
      
      // Parse batch results and map back to individual images
      const individualResults = await parseBatchResults(searchResults, frontSideData);
      
      // Save results to individual image JSON files
      const savedResults = [];
      for (let i = 0; i < individualResults.length; i++) {
        const result = individualResults[i];
        const item = frontSideData[i];
        
        try {
          await saveReadySearchToCache(item.imageId, result);
          
          // Also update via FileManager for integrated saving
          try {
            const FileManager = require('../services/file-manager');
            const imagePath = Buffer.from(item.imageId, 'base64').toString();
            await FileManager.updateWithReadySearchResults(imagePath, result);
          } catch (fmError) {
            logger.warn('Failed to update batch result via FileManager', { 
              imageId: item.imageId,
              error: fmError.message 
            });
            // Continue processing even if FileManager fails
          }
          
          savedResults.push({
            imageId: item.imageId,
            searchQuery: item.yearOfBirth 
              ? `${item.firstName} ${item.lastName},${item.yearOfBirth}`
              : `${item.firstName} ${item.lastName}`,
            success: true,
            results: result
          });
        } catch (saveError) {
          logger.warn('Failed to save batch result to cache', { 
            imageId: item.imageId,
            error: saveError.message 
          });
          savedResults.push({
            imageId: item.imageId,
            searchQuery: item.yearOfBirth 
              ? `${item.firstName} ${item.lastName},${item.yearOfBirth}`
              : `${item.firstName} ${item.lastName}`,
            success: false,
            error: 'Failed to save to cache',
            results: result
          });
        }
      }
      
      res.json({
        success: true,
        data: {
          processedCount: frontSideData.length,
          skippedCount: validatedSearchData.length - frontSideData.length,
          results: savedResults,
          batchQuery,
          timestamp: new Date().toISOString()
        }
      });
      
    } catch (searchError) {
      logger.error('ReadySearch batch execution failed', { 
        error: searchError.message, 
        batchItemCount: frontSideData.length
      });
      
      res.status(500).json({
        success: false,
        error: 'ReadySearch batch automation failed',
        details: searchError.message
      });
    }
    
  } catch (error) {
    logger.error('Error in ReadySearch batch endpoint', { error: error.message });
    throw error;
  }
}));

// Get ReadySearch results for an image from cache
router.get('/results/:imageId', ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { imageId } = req.params;
    
    // Decode image path from base64 ID
    const imagePath = Buffer.from(imageId, 'base64').toString();
    const validatedPath = SecurityMiddleware.validatePath(imagePath);
    
    // Look for existing OCR results JSON file
    const directory = path.dirname(validatedPath);
    const basename = path.basename(validatedPath, path.extname(validatedPath));
    const ocrResultsPath = path.join(directory, `${basename}_ocr_results.json`);
    
    try {
      const ocrData = await fs.readFile(ocrResultsPath, 'utf8');
      const ocrResults = JSON.parse(ocrData);
      
      if (ocrResults.readySearchResults) {
        res.json({
          success: true,
          data: ocrResults.readySearchResults
        });
      } else {
        res.json({
          success: false,
          error: 'No ReadySearch results found for this image'
        });
      }
    } catch (error) {
      res.json({
        success: false,
        error: 'No OCR results file found for this image'
      });
    }
    
  } catch (error) {
    logger.error('Error retrieving ReadySearch results', { error: error.message });
    throw error;
  }
}));

// Execute ReadySearch automation
function executeReadySearch(searchQuery) {
  return new Promise((resolve, reject) => {
    // Resolve ReadySearch directory relative to project root for portability
    const readySearchDir = resolveReadySearchDir();
    // Use unified CLI with JSON output mode
    const unifiedCliPath = path.join(readySearchDir, 'readysearch_cli.py');
    
    // Check if ReadySearch unified CLI exists
    if (!fsExistsSync(unifiedCliPath)) {
      reject(new Error(`ReadySearch unified CLI not found at: ${unifiedCliPath}`));
      return;
    }
    
    logger.info('Starting ReadySearch unified CLI v2', { searchQuery, cliPath: unifiedCliPath });
    
    // Spawn the ReadySearch CLI process with JSON output mode for better integration
    const childProcess = spawn('python', [unifiedCliPath, '--mode=json', searchQuery], {
      cwd: readySearchDir,
      windowsHide: true,
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let stdout = '';
    let stderr = '';
    
    childProcess.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    childProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    childProcess.on('close', (code) => {
      logger.info('ReadySearch production CLI completed', { 
        code, 
        searchQuery,
        stdoutLength: stdout.length,
        stderrLength: stderr.length 
      });
      
      if (code === 0) {
        // Parse the output to extract meaningful results
        const results = parseReadySearchOutput(stdout);
        resolve(results);
      } else {
        logger.error('ReadySearch CLI output on error', { stdout, stderr });
        reject(new Error(`ReadySearch failed with exit code ${code}. Error: ${stderr}`));
      }
    });
    
    childProcess.on('error', (error) => {
      logger.error('ReadySearch CLI process error', { error: error.message, searchQuery });
      reject(new Error(`Failed to start ReadySearch: ${error.message}`));
    });
    
    // Timeout after 60 seconds
    setTimeout(() => {
      if (!childProcess.killed) {
        childProcess.kill();
        reject(new Error('ReadySearch process timed out'));
      }
    }, 60000);
  });
}

// Execute ReadySearch batch automation using unified CLI
function executeBatchReadySearch(batchQuery, itemCount) {
  return new Promise((resolve, reject) => {
    // Resolve ReadySearch directory relative to project root for portability
    const readySearchDir = resolveReadySearchDir();
    // Use unified CLI with JSON output mode for batch processing
    const unifiedCliPath = path.join(readySearchDir, 'readysearch_cli.py');
    
    // Check if ReadySearch unified CLI exists
    if (!fsExistsSync(unifiedCliPath)) {
      reject(new Error(`ReadySearch unified CLI not found at: ${unifiedCliPath}`));
      return;
    }
    
    logger.info('Starting ReadySearch unified CLI for batch processing', { 
      itemCount, 
      batchQueryLength: batchQuery.length,
      cliPath: unifiedCliPath
    });
    
    // Spawn the ReadySearch CLI process with JSON output mode and batch query
    // The unified CLI automatically handles chunking for large batches (>=10 items)
    const childProcess = spawn('python', [unifiedCliPath, '--mode=json', batchQuery], {
      cwd: readySearchDir,
      windowsHide: true,
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let stdout = '';
    let stderr = '';
    
    childProcess.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    childProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    childProcess.on('close', (code) => {
      logger.info('ReadySearch batch CLI completed', { 
        code, 
        itemCount,
        cliFile,
        stdoutLength: stdout.length,
        stderrLength: stderr.length 
      });
      
      if (code === 0) {
        resolve({
          rawOutput: stdout,
          stderr: stderr,
          cliUsed: 'unified CLI',
          itemCount: itemCount
        });
      } else {
        logger.error('ReadySearch unified CLI output on error', { stdout, stderr });
        reject(new Error(`ReadySearch batch failed with exit code ${code}. Error: ${stderr}`));
      }
    });
    
    childProcess.on('error', (error) => {
      logger.error('ReadySearch batch CLI process error', { 
        error: error.message, 
        cliFile, 
        itemCount 
      });
      reject(new Error(`Failed to start ReadySearch batch CLI: ${error.message}`));
    });
    
    // Intelligent timeout logic for unified CLI with automatic chunking
    // The unified CLI automatically uses chunking for batches >10 items, providing 48-53% performance improvement
    let timeout;
    if (itemCount >= 50) {
      // Large batches: Chunking provides significant performance improvement
      timeout = Math.max(120000, itemCount * 1000); // 1 second per item with chunking
    } else if (itemCount >= 10) {
      // Medium batches: Moderate chunking benefits
      timeout = Math.max(120000, itemCount * 1500); // 1.5 seconds per item
    } else {
      // Small batches: Sequential processing
      timeout = Math.max(60000, itemCount * 2000); // 2 seconds per item
    }
    
    // Maximum timeout cap for very large batches
    timeout = Math.min(timeout, 1800000); // 30 minutes maximum
    
    setTimeout(() => {
      if (!childProcess.killed) {
        childProcess.kill();
        reject(new Error(`ReadySearch batch process timed out after ${timeout/1000} seconds (unified CLI)`));
      }
    }, timeout);
  });
}

// Parse batch ReadySearch results and map to individual queries
async function parseBatchResults(batchResults, searchData) {
  try {
    const { rawOutput, cliUsed } = batchResults;
    const lines = rawOutput.split('\n').map(line => line.trim()).filter(line => line);
    
    const individualResults = [];
    
    // The unified CLI outputs JSON format, so try to parse as JSON first
    try {
      const jsonOutput = JSON.parse(rawOutput);
      
      if (jsonOutput.results && Array.isArray(jsonOutput.results)) {
        // Map JSON results to individual search items
        for (let i = 0; i < searchData.length; i++) {
          const searchItem = searchData[i];
          const searchQuery = searchItem.yearOfBirth 
            ? `${searchItem.firstName} ${searchItem.lastName},${searchItem.yearOfBirth}`
            : `${searchItem.firstName} ${searchItem.lastName}`;
          
          // Find matching result by name (fuzzy matching)
          const matchingResult = jsonOutput.results.find(result => 
            result.name && result.name.toLowerCase().includes(searchItem.firstName.toLowerCase()) &&
            result.name.toLowerCase().includes(searchItem.lastName.toLowerCase())
          ) || jsonOutput.results[i] || {}; // Fallback to positional mapping
          
          const matches = (matchingResult.detailed_results || []).map(match => ({
            name: match.name || match.matched_name || 'Unknown',
            matchType: match.match_type || 'SIMILAR',
            confidence: match.confidence || 0.5
          }));
          
          individualResults.push({
            searchQuery: searchQuery,
            rawOutput: JSON.stringify(matchingResult, null, 2),
            matches: matches,
            summary: matchingResult.match_reasoning || `Found ${matches.length} matches`,
            totalResults: matchingResult.matches_found || matches.length,
            hasMatches: matches.length > 0,
            timestamp: matchingResult.timestamp || new Date().toISOString()
          });
        }
        
        return individualResults;
      }
    } catch (jsonError) {
      logger.warn('Failed to parse unified CLI output as JSON, falling back to text parsing', { 
        error: jsonError.message 
      });
    }
    
    // Fallback to text parsing if JSON parsing fails
    const globalResult = parseReadySearchOutput(rawOutput);
    
    // Distribute results across all search items (best effort)
    for (let i = 0; i < searchData.length; i++) {
      const searchItem = searchData[i];
      const searchQuery = searchItem.yearOfBirth 
        ? `${searchItem.firstName} ${searchItem.lastName},${searchItem.yearOfBirth}`
        : `${searchItem.firstName} ${searchItem.lastName}`;
      
      individualResults.push({
        searchQuery: searchQuery,
        rawOutput: globalResult.rawOutput,
        matches: i === 0 ? globalResult.matches : [], // Only first item gets matches in fallback mode
        summary: globalResult.summary,
        totalResults: i === 0 ? globalResult.totalResults : 0,
        hasMatches: i === 0 ? globalResult.hasMatches : false
      });
    }
    
    return individualResults;
    
  } catch (error) {
    logger.error('Error parsing batch ReadySearch results', { error: error.message });
    
    // Return error results for all items
    return searchData.map(item => ({
      searchQuery: item.yearOfBirth 
        ? `${item.firstName} ${item.lastName},${item.yearOfBirth}`
        : `${item.firstName} ${item.lastName}`,
      rawOutput: batchResults.rawOutput || '',
      matches: [],
      summary: 'Failed to parse batch results',
      totalResults: 0,
      hasMatches: false,
      error: error.message
    }));
  }
}

// Find result section for a specific search query in batch output
function findResultSection(lines, searchQuery, startIndex = 0) {
  const namePattern = searchQuery.split(',')[0].toLowerCase(); // Extract "firstname lastname"
  
  for (let i = startIndex; i < lines.length; i++) {
    const line = lines[i].toLowerCase();
    
    // Look for lines containing the name
    if (line.includes(namePattern)) {
      const startIdx = i;
      let endIdx = i + 1;
      
      // Find the end of this result section
      while (endIdx < lines.length && 
             !lines[endIdx].toLowerCase().includes('========') &&
             !lines[endIdx].toLowerCase().includes('------') &&
             endIdx < startIdx + 20) { // Limit section size
        endIdx++;
      }
      
      return {
        startIndex: startIdx,
        endIndex: endIdx,
        lines: lines.slice(startIdx, endIdx)
      };
    }
  }
  
  return null;
}

// Parse individual result section
function parseIndividualResult(resultSection, searchQuery) {
  try {
    const sectionText = resultSection.lines.join('\n');
    const matches = [];
    
    // Extract matches using the same pattern as single search
    const matchPattern = /[•·]\\s*(.+?)\\s*\\((EXACT MATCH|PARTIAL MATCH|SIMILAR)\\)/gi;
    const summaryPattern = /(\\d+)\\s+more|found\\s+(\\d+)|total.*?(\\d+)/gi;
    
    // Extract matches
    for (const line of resultSection.lines) {
      const matchResults = [...line.matchAll(matchPattern)];
      for (const match of matchResults) {
        matches.push({
          name: match[1].trim(),
          matchType: match[2],
          confidence: match[2] === 'EXACT MATCH' ? 1.0 : match[2] === 'PARTIAL MATCH' ? 0.7 : 0.5
        });
      }
    }
    
    // Extract summary information
    let totalResults = 0;
    let summary = '';
    
    for (const line of resultSection.lines) {
      const summaryMatches = [...line.matchAll(summaryPattern)];
      for (const match of summaryMatches) {
        const count = parseInt(match[1] || match[2] || match[3]);
        if (count && count > totalResults) {
          totalResults = count;
        }
      }
      
      if (line.includes('results') || line.includes('found') || line.includes('match')) {
        summary = line;
      }
    }
    
    return {
      searchQuery,
      rawOutput: sectionText,
      matches: matches,
      summary: summary || `Processed search for ${searchQuery}`,
      totalResults: totalResults || matches.length,
      hasMatches: matches.length > 0
    };
    
  } catch (error) {
    return {
      searchQuery,
      rawOutput: resultSection.lines.join('\n'),
      matches: [],
      summary: 'Failed to parse individual result',
      totalResults: 0,
      hasMatches: false,
      error: error.message
    };
  }
}

// Parse ReadySearch output to extract structured results
function parseReadySearchOutput(output) {
  try {
    // First try to parse as JSON if ReadySearch CLI outputs JSON
    try {
      const jsonOutput = JSON.parse(output);
      if (jsonOutput.results && Array.isArray(jsonOutput.results)) {
        // Convert ReadySearch JSON format to our expected format
        const result = jsonOutput.results[0] || {};
        const matches = (result.detailed_results || []).map(match => ({
          name: match.name || match.text || 'Unknown',
          matchType: match.match_type || 'SIMILAR',
          confidence: match.confidence || 0.5
        }));
        
        return {
          rawOutput: JSON.stringify(jsonOutput, null, 2), // Show formatted JSON instead of console output
          matches: matches,
          summary: result.match_reasoning || `Found ${matches.length} matches`,
          totalResults: result.matches_found || matches.length,
          hasMatches: matches.length > 0,
          timestamp: result.timestamp || new Date().toISOString()
        };
      }
    } catch (jsonError) {
      // Fall back to text parsing if not JSON
      logger.info('ReadySearch output is not JSON, falling back to text parsing');
    }
    
    // Look for structured output patterns in the ReadySearch results
    const lines = output.split('\n').map(line => line.trim()).filter(line => line);
    
    const results = {
      rawOutput: formatConsoleOutputAsJSON(output), // Format console output as structured JSON
      matches: [],
      summary: '',
      totalResults: 0,
      hasMatches: false
    };
    
    // Look for match patterns (e.g., "• NADER GHARSA (EXACT MATCH)")
    const matchPattern = /[•·]\s*(.+?)\s*\((EXACT MATCH|PARTIAL MATCH|SIMILAR)\)/gi;
    const summaryPattern = /(\d+)\s+more|found\s+(\d+)|total.*?(\d+)/gi;
    
    // Extract matches
    for (const line of lines) {
      const matches = [...line.matchAll(matchPattern)];
      for (const match of matches) {
        results.matches.push({
          name: match[1].trim(),
          matchType: match[2],
          confidence: match[2] === 'EXACT MATCH' ? 1.0 : match[2] === 'PARTIAL MATCH' ? 0.7 : 0.5
        });
      }
    }
    
    // Extract summary information
    for (const line of lines) {
      const summaryMatches = [...line.matchAll(summaryPattern)];
      for (const match of summaryMatches) {
        const count = parseInt(match[1] || match[2] || match[3]);
        if (count && count > results.totalResults) {
          results.totalResults = count;
        }
      }
    }
    
    // Find summary text
    const summaryLine = lines.find(line => 
      line.includes('results') || 
      line.includes('found') || 
      line.includes('match')
    );
    
    if (summaryLine) {
      results.summary = summaryLine;
    }
    
    results.hasMatches = results.matches.length > 0;
    
    logger.info('Parsed ReadySearch results', {
      matchCount: results.matches.length,
      totalResults: results.totalResults,
      hasMatches: results.hasMatches
    });
    
    return results;
    
  } catch (error) {
    logger.error('Error parsing ReadySearch output', { error: error.message });
    return {
      rawOutput: formatConsoleOutputAsJSON(output),
      matches: [],
      summary: 'Failed to parse results',
      totalResults: 0,
      hasMatches: false,
      error: error.message
    };
  }
}

// Format console output as structured JSON for better readability
function formatConsoleOutputAsJSON(output) {
  try {
    const lines = output.split('\n').map(line => line.trim()).filter(line => line);
    const structured = {
      search_execution: {
        timestamp: new Date().toISOString(),
        status: "completed",
        output_type: "console_log"
      },
      console_output: lines,
      parsed_data: {
        matches: [],
        summary: "Raw console output - see console_output for details"
      },
      note: "This is formatted console output. For structured results, ensure ReadySearch CLI outputs JSON format."
    };
    
    return JSON.stringify(structured, null, 2);
  } catch (error) {
    return output; // Fallback to original output if formatting fails
  }
}

// Save ReadySearch results to image's OCR cache
async function saveReadySearchToCache(imageId, searchResults) {
  try {
    // Decode image path from base64 ID
    const imagePath = Buffer.from(imageId, 'base64').toString();
    const validatedPath = SecurityMiddleware.validatePath(imagePath);
    
    // Find existing OCR results JSON file
    const directory = path.dirname(validatedPath);
    const basename = path.basename(validatedPath, path.extname(validatedPath));
    const ocrResultsPath = path.join(directory, `${basename}_ocr_results.json`);
    
    // Read existing OCR results
    let ocrData = {};
    try {
      const existingData = await fs.readFile(ocrResultsPath, 'utf8');
      ocrData = JSON.parse(existingData);
    } catch (error) {
      // File doesn't exist or is invalid - create new structure
      logger.warn('OCR results file not found, creating new entry', { ocrResultsPath });
    }
    
    // Add ReadySearch results to OCR data
    ocrData.readySearchResults = {
      ...searchResults,
      timestamp: new Date().toISOString(),
      imageId
    };
    
    // Write updated data back to file
    await fs.writeFile(ocrResultsPath, JSON.stringify(ocrData, null, 2));
    
    logger.info('ReadySearch results saved to cache', { 
      ocrResultsPath,
      matchCount: searchResults.matches?.length || 0 
    });
    
  } catch (error) {
    logger.error('Failed to save ReadySearch results to cache', { 
      error: error.message,
      imageId 
    });
    // Don't throw - this is not critical for the search operation
  }
}

module.exports = router;