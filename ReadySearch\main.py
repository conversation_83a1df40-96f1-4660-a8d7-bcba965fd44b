"""Main automation script for ReadySearch.com.au name matching."""

import asyncio
import logging
import sys
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime

from config import Config
from readysearch_automation import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>, BrowserController, Reporter
)
from readysearch_automation.enhanced_result_parser import (
    EnhancedResultParser, EnhancedNameMatcher, SearchStatistics
)

# Set up logging
def setup_logging(config: Dict[str, Any]):
    """Set up logging configuration."""
    logging.basicConfig(
        level=getattr(logging, config['log_level']),
        format=config['log_format'],
        handlers=[
            logging.FileHandler(config['log_file']),
            logging.StreamHandler(sys.stdout)
        ]
    )

class ReadySearchAutomation:
    """Main automation class for ReadySearch.com.au with enhanced validation."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.browser_controller = BrowserController(config)
        self.name_matcher = EnhancedNameMatcher(strict_mode=True)
        self.reporter = Reporter(config['output_file'])
        
        # Set up logging
        setup_logging(config)
        self.logger = logging.getLogger(__name__)
        
    async def run_automation(self, names: List[str]) -> bool:
        """
        Run the complete automation process with enhanced validation.
        
        Args:
            names: List of names to search
            
        Returns:
            True if automation completed successfully
        """
        try:
            self.logger.info(f"Starting enhanced automation for {len(names)} names")
            
            # Start browser
            await self.browser_controller.start_browser()
            
            # Navigate to search page
            navigation_success = await self.browser_controller.navigate_to_search_page()
            if not navigation_success:
                self.logger.error("Failed to navigate to search page")
                return False
                
            # Process each name with enhanced validation
            for i, name in enumerate(names, 1):
                # Extract name string for logging and reporting
                name_str = name.name if hasattr(name, 'name') else str(name)
                self.logger.info(f"Processing {i}/{len(names)}: {name_str}")
                
                try:
                    # Search for the name with enhanced validation
                    search_result = await self._search_single_name_enhanced(name)
                    
                    # Process results with detailed statistics
                    self._process_search_result(name, search_result)
                        
                except Exception as e:
                    self.logger.error(f"Error processing {name_str}: {str(e)}")
                    self.reporter.add_result(
                        name=name_str,
                        status='Error',
                        error=str(e)
                    )
                    
                # Rate limiting delay
                if i < len(names):
                    await asyncio.sleep(self.config['delay'])
                    
            # Save results
            self.reporter.save_results_csv()
            self.reporter.save_results_json()
            self.reporter.print_summary()
            
            self.logger.info("Enhanced automation completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Automation failed: {str(e)}")
            return False
            
        finally:
            # Clean up browser
            await self.browser_controller.cleanup()
    
    def _process_search_result(self, name: str, search_result: Dict[str, Any]):
        """Process search result with enhanced statistics."""
        try:
            status = search_result.get('status', 'Error')
            statistics = search_result.get('statistics')
            matches = search_result.get('exact_matches', [])
            all_results = search_result.get('all_results', [])
            
            if status == 'Match':
                # Add detailed match information
                match_details = []
                for match in matches:
                    match_details.append({
                        'matched_name': match.name,
                        'location': match.location,
                        'confidence': match.confidence_score,
                        'match_type': match.match_type,
                        'additional_info': match.additional_info
                    })
                
                self.reporter.add_result(
                    name=name,
                    status='Match',
                    matches_found=len(matches),
                    total_results=statistics.total_results_found if statistics else 0,
                    exact_matches=statistics.exact_matches if statistics else 0,
                    partial_matches=statistics.partial_matches if statistics else 0,
                    search_time=statistics.search_time if statistics else 0.0,
                    match_details=match_details
                )
                
                # Log detailed match information
                self.logger.info(f"✅ MATCH FOUND for '{name}':")
                self.logger.info(f"   Total results: {statistics.total_results_found if statistics else 0}")
                self.logger.info(f"   Exact matches: {len(matches)}")
                for i, match in enumerate(matches, 1):
                    self.logger.info(f"   Match {i}: {match.name} ({match.confidence_score:.2f} confidence)")
                    if match.location:
                        self.logger.info(f"            Location: {match.location}")
                
            elif status == 'No Match':
                self.reporter.add_result(
                    name=name,
                    status='No Match',
                    results_found=statistics.total_results_found if statistics else 0,
                    search_time=statistics.search_time if statistics else 0.0
                )
                
                self.logger.info(f"❌ NO MATCH for '{name}'")
                self.logger.info(f"   Total results checked: {statistics.total_results_found if statistics else 0}")
                
            else:
                # Error case
                error_msg = search_result.get('error', 'Unknown error')
                self.reporter.add_result(
                    name=name,
                    status='Error',
                    error=error_msg
                )
                
                self.logger.error(f"❌ ERROR for '{name}': {error_msg}")
                
        except Exception as e:
            self.logger.error(f"Error processing search result for {name}: {str(e)}")
    
    async def _search_single_name_enhanced(self, name: str) -> Dict[str, Any]:
        """
        Search for a single name with enhanced validation and statistics.
        
        Args:
            name: Name to search (can be string or SearchRecord)
            
        Returns:
            Dictionary with search results and statistics
        """
        try:
            # Handle both string names and SearchRecord objects
            search_name = name.name if hasattr(name, 'name') else str(name)
            birth_year = getattr(name, 'birth_year', None) if hasattr(name, 'birth_year') else None
            
            start_time = datetime.now()
            
            # Perform the search using browser controller
            search_result = await self.browser_controller.search_name(search_name)
            
            # If search failed at browser level, return error
            if search_result.get('status') == 'error':
                return {
                    'status': 'Error',
                    'error': search_result.get('error', 'Browser search failed')
                }
            
            # Get page content for result parsing
            page_content = await self.browser_controller.get_page_content()
            
            # Parse results with enhanced parser
            parser = EnhancedResultParser(
                name_matcher=self.name_matcher,
                search_name=search_name,
                birth_year=birth_year
            )
            
            parsed_results = parser.parse_results(page_content)
            search_time = (datetime.now() - start_time).total_seconds()
            
            # Create statistics
            statistics = SearchStatistics(
                total_results_found=len(parsed_results.all_results),
                exact_matches=len(parsed_results.exact_matches),
                partial_matches=len(parsed_results.partial_matches),
                search_time=search_time
            )
            
            # Determine overall status
            if parsed_results.exact_matches:
                status = 'Match'
            elif parsed_results.partial_matches:
                status = 'Match'  # Consider partial matches as matches
            else:
                status = 'No Match'
            
            return {
                'status': status,
                'exact_matches': parsed_results.exact_matches,
                'partial_matches': parsed_results.partial_matches,
                'all_results': parsed_results.all_results,
                'statistics': statistics
            }
            
        except Exception as e:
            self.logger.error(f"Enhanced search failed for {name}: {str(e)}")
            return {
                'status': 'Error',
                'error': str(e)
            }

async def main():
    """Main entry point for command line usage."""
    if len(sys.argv) < 2:
        print("Usage: python main.py <input_file.csv>")
        return
    
    input_file = sys.argv[1]
    
    try:
        # Load configuration
        config = Config.get_config()
        
        # Load input data
        loader = InputLoader(input_file)
        names = loader.load_names()
        
        # Run automation
        automation = ReadySearchAutomation(config)
        success = await automation.run_automation(names)
        
        if success:
            print(f"✅ Automation completed successfully for {len(names)} names")
        else:
            print("❌ Automation failed")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())