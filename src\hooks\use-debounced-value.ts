import { useState, useEffect } from 'react';

/**
 * Custom hook that debounces a value with configurable delay
 * Useful for search inputs and other frequently changing values
 */
export function useDebouncedValue<T>(value: T, delay: number = 300): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Custom hook that provides both immediate and debounced values
 * Useful when you need immediate UI feedback but want to debounce API calls
 */
export function useDebouncedState<T>(
  initialValue: T, 
  delay: number = 300
): [T, T, (value: T) => void] {
  const [immediateValue, setImmediateValue] = useState<T>(initialValue);
  const debouncedValue = useDebouncedValue(immediateValue, delay);

  return [immediateValue, debouncedValue, setImmediateValue];
}