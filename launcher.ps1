param([string]$Option)

# =============================================================================
# DL Organizer - Advanced PowerShell Launcher
# =============================================================================
# Professional development environment with comprehensive features:
# - Build management and testing
# - Development server with hot reload
# - Production server deployment
# - Ngrok tunnel with advanced monitoring
# - Connection monitoring and cleanup
# - Health checks and diagnostics
# - Customizable themes and gestures
# - Enhanced health with API pings and resource monitoring
# - Version control sync with Git
# - Automated dependency installation and updates
# =============================================================================

# Set console preferences
$Host.UI.RawUI.WindowTitle = "DL Organizer - Advanced Launcher"
$Host.UI.RawUI.BackgroundColor = "Black"
$Host.UI.RawUI.ForegroundColor = "Green"
Clear-Host

# Add Bun to PATH if not already present
$bunPath = "C:\Users\<USER>\.bun\bin"
if ($env:PATH -notlike "*$bunPath*") {
    $env:PATH = "$bunPath;$env:PATH"
    Write-Host "Added Bun to PATH for this session" -ForegroundColor Green
}

# Global variables
$script:ngrokProcess = $null
$script:frontendProcess = $null
$script:backendProcess = $null
$script:testProcess = $null
$script:buildProcess = $null
$script:activeConnections = @()
$script:ngrokApiUrl = "http://localhost:4040/api/tunnels"
$script:projectRoot = $PSScriptRoot
$script:logFile = "$projectRoot\data\logs\launcher.log"
$script:devFrontendPort = 3030
$script:prodFrontendPort = 3001
$script:backendPort = 3003  # Fixed to match API routes configuration
$script:portsToCheck = @($script:devFrontendPort, $script:prodFrontendPort, $script:backendPort)
$script:currentTheme = "Default"  # For themes
$script:requiredPorts = @{
    "Frontend" = $script:devFrontendPort
    "Backend" = $script:backendPort
    "Ngrok" = 4040
}

# Ensure logs directory exists
$logsDir = "$projectRoot\data\logs"
if (!(Test-Path $logsDir)) {
    New-Item -ItemType Directory -Path $logsDir -Force | Out-Null
}

# Logging function
function Write-Log {
    param(
        [string]$Message,
        [ValidateSet("INFO", "WARN", "ERROR", "SUCCESS")]
        [string]$Level = "INFO"
    )

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    # Write to console with colors
    switch ($Level) {
        "INFO" { Write-Host $logEntry -ForegroundColor Cyan }
        "WARN" { Write-Host $logEntry -ForegroundColor Yellow }
        "ERROR" { Write-Host $logEntry -ForegroundColor Red }
        "SUCCESS" { Write-Host $logEntry -ForegroundColor Green }
    }
    # Write to log file
    Add-Content -Path $script:logFile -Value $logEntry
}

# Enhanced Port Conflict Detection and Management
function Test-PortAvailability {
    param([int]$Port)

    try {
        $connection = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
        return $connection -eq $null
    } catch {
        # If Get-NetTCPConnection fails, try netstat as fallback
        $netstat = netstat -ano | Select-String ":$Port\s"
        return $netstat -eq $null
    }
}

function Get-ProcessUsingPort {
    param([int]$Port)

    try {
        # Method 1: Use Get-NetTCPConnection (faster and more reliable)
        $connection = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
        if ($connection) {
            $processId = $connection.OwningProcess
            $process = Get-Process -Id $processId -ErrorAction SilentlyContinue
            if ($process) {
                return @{
                    ProcessId = $processId
                    ProcessName = $process.Name
                    CommandLine = ""
                    Process = $process
                }
            }
        }
    } catch {
        # Method 2: Fallback to netstat
        try {
            $netstat = netstat -ano | Select-String ":$Port\s"
            if ($netstat) {
                $processId = ($netstat -split '\s+')[-1]
                if ($processId -match '^\d+$') {
                    $process = Get-Process -Id $processId -ErrorAction SilentlyContinue
                    if ($process) {
                        return @{
                            ProcessId = $processId
                            ProcessName = $process.Name
                            CommandLine = ""
                            Process = $process
                        }
                    }
                }
            }
        } catch {
            Write-Log "Failed to detect process using port $Port" "WARN"
        }
    }

    return $null
}

# FIXED AND HARDENED FUNCTION
function Resolve-PortConflicts {
    param([bool]$ForceKill = $false)

    Write-Log "Checking for port conflicts on required ports..." "INFO"
    $conflicts = @()

    foreach ($portName in $script:requiredPorts.Keys) {
        $port = $script:requiredPorts[$portName]
        if (-not (Test-PortAvailability -Port $port)) {
            $processInfo = Get-ProcessUsingPort -Port $port
            if ($processInfo) {
                $conflicts += @{
                    PortName = $portName
                    Port = $port
                    ProcessInfo = $processInfo
                }
                Write-Log "Port conflict detected: $portName ($port) used by $($processInfo.ProcessName) (PID: $($processInfo.ProcessId))" "WARN"
            }
        } else {
            Write-Log "Port $portName ($port) is available" "SUCCESS"
        }
    }

    # Hardening 1: Return early when no conflicts remain
    if ($conflicts.Count -eq 0) {
        Write-Log "No port conflicts detected – all required ports are available" "SUCCESS"
        return $true
    }

    Write-Host ""
    Write-Host "🚨 PORT CONFLICTS DETECTED" -ForegroundColor Red
    Write-Host "The following ports are required but currently in use:" -ForegroundColor Yellow
    Write-Host ""
    foreach ($conflict in $conflicts) {
        Write-Host "  • $($conflict.PortName) (Port $($conflict.Port)): " -NoNewline -ForegroundColor White
        Write-Host "$($conflict.ProcessInfo.ProcessName) (PID: $($conflict.ProcessInfo.ProcessId))" -ForegroundColor Red
    }
    Write-Host ""

    $validChoice = $false
    do {
        if ($ForceKill) {
            Write-Host "🔧 RESOLVING CONFLICTS (Force mode enabled)" -ForegroundColor Yellow
            foreach ($conflict in $conflicts) {
                try {
                    $conflict.ProcessInfo.Process.Kill()
                    Write-Log "Killed conflicting process: $($conflict.ProcessInfo.ProcessName) (PID: $($conflict.ProcessInfo.ProcessId)) on port $($conflict.Port)" "SUCCESS"
                    Start-Sleep -Milliseconds 500
                } catch {
                    Write-Log "Failed to kill process $($conflict.ProcessInfo.ProcessName) (PID: $($conflict.ProcessInfo.ProcessId)): $($_.Exception.Message)" "ERROR"
                }
            }
            $validChoice = $true # <-- MINIMAL FIX: This breaks the do/while loop
        } else {
            # Interactive branch
            Write-Host "Options:" -ForegroundColor Cyan
            Write-Host "  [Y] Kill conflicting processes and continue" -ForegroundColor Green
            Write-Host "  [N] Cancel startup" -ForegroundColor Red
            Write-Host "  [S] Show detailed process information" -ForegroundColor Yellow
            Write-Host ""
            $choice = Read-Host "Choose an option [Y/N/S]"
            switch ($choice.ToUpper()) {
                "Y" {
                    Write-Host ""
                    Write-Host "🔧 RESOLVING CONFLICTS" -ForegroundColor Yellow
                    foreach ($conflict in $conflicts) {
                        try {
                             # Check if process still exists before trying to kill it
                            if (Get-Process -Id $conflict.ProcessInfo.ProcessId -ErrorAction SilentlyContinue) {
                                $conflict.ProcessInfo.Process.Kill()
                                Write-Log "Killed conflicting process: $($conflict.ProcessInfo.ProcessName) (PID: $($conflict.ProcessInfo.ProcessId)) on port $($conflict.Port)" "SUCCESS"
                                Start-Sleep -Milliseconds 500
                            } else {
                                Write-Log "Process $($conflict.ProcessInfo.ProcessName) (PID: $($conflict.ProcessInfo.ProcessId)) already exited." "INFO"
                            }
                        } catch {
                            Write-Log "Failed to kill process $($conflict.ProcessInfo.ProcessName) (PID: $($conflict.ProcessInfo.ProcessId)): $($_.Exception.Message)" "ERROR"
                        }
                    }
                    $validChoice = $true # Proceed to the final verification step
                }
                "N" {
                    Write-Log "Port conflict resolution cancelled by user" "INFO"
                    return $false # Exit the function entirely
                }
                "S" {
                    Write-Host ""
                    Write-Host "📋 DETAILED PROCESS INFORMATION" -ForegroundColor Cyan
                    foreach ($conflict in $conflicts) {
                        $proc = Get-Process -Id $conflict.ProcessInfo.ProcessId -ErrorAction SilentlyContinue
                        if ($proc) {
                            Write-Host "  Port $($conflict.Port) ($($conflict.PortName)):" -ForegroundColor White
                            Write-Host "    Process: $($proc.Name)" -ForegroundColor Gray
                            Write-Host "    PID: $($proc.Id)" -ForegroundColor Gray
                            Write-Host "    Start Time: $($proc.StartTime)" -ForegroundColor Gray
                            Write-Host "    Memory: $([math]::Round($proc.WorkingSet / 1MB, 1)) MB" -ForegroundColor Gray
                            Write-Host ""
                        }
                    }
                    # Loop will repeat, showing the menu again
                }
                default {
                    Write-Host "Invalid choice. Please enter Y, N, or S." -ForegroundColor Red
                }
            }
        }
    } while (-not $validChoice)

    # Hardening 2: Single, robust verification step after resolution attempt
    Write-Host "🔍 Verifying ports are now available..." -ForegroundColor Yellow
    Start-Sleep -Seconds 2 # Give ports time to be released by the OS

    $remainingConflicts = @()
    foreach ($conflict in $conflicts) {
        if (-not (Test-PortAvailability -Port $conflict.Port)) {
            $remainingConflicts += $conflict
        }
    }

    if ($remainingConflicts.Count -eq 0) {
        Write-Log "All port conflicts resolved successfully!" "SUCCESS"
        return $true
    } else {
        Write-Log "Some port conflicts could not be resolved" "ERROR"
        foreach ($remaining in $remainingConflicts) {
            Write-Log "Port $($remaining.Port) ($($remaining.PortName)) is still in use." "ERROR"
        }
        return $false
    }
}


# Function to set console theme
function Set-ConsoleTheme {
    param([string]$Theme)

    switch ($Theme) {
        "Default" {
            $Host.UI.RawUI.BackgroundColor = "Black"
            $Host.UI.RawUI.ForegroundColor = "Green"
        }
        "Dark" {
            $Host.UI.RawUI.BackgroundColor = "DarkGray"
            $Host.UI.RawUI.ForegroundColor = "White"
        }
        "Light" {
            $Host.UI.RawUI.BackgroundColor = "White"
            $Host.UI.RawUI.ForegroundColor = "Black"
        }
    }
    Clear-Host
    $script:currentTheme = $Theme
    Write-Log "Theme changed to $Theme" "INFO"
}

# Function to display the main menu
function Show-MainMenu {
    Set-ConsoleTheme $script:currentTheme  # Apply current theme
    Clear-Host
    Write-Host "===============================================================================" -ForegroundColor Cyan
    Write-Host " DL Organizer - Advanced Development Environment" -ForegroundColor White
    Write-Host "===============================================================================" -ForegroundColor Cyan
    Write-Host " Professional Driver's License OCR Processing System" -ForegroundColor Gray
    Write-Host " Comprehensive Hybrid Web Application" -ForegroundColor Gray
    Write-Host "===============================================================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host " 🚀 QUICK START (ENHANCED - RECOMMENDED):" -ForegroundColor Green
    Write-Host " [D] Start Enhanced Development Environment (Smart Port Management)" -ForegroundColor Green
    Write-Host " [N] Start Development + Ngrok Tunnel" -ForegroundColor Green
    Write-Host ""
    Write-Host " BUILD & TEST OPTIONS:" -ForegroundColor Yellow
    Write-Host " [1] Build Project (Production)" -ForegroundColor White
    Write-Host " [2] Run Tests (All)" -ForegroundColor White
    Write-Host " [3] Run Tests (E2E Only)" -ForegroundColor White
    Write-Host " [4] Run Tests (Unit Only)" -ForegroundColor White
    Write-Host " [5] Lint & Type Check" -ForegroundColor White
    Write-Host ""
    Write-Host " DEVELOPMENT SERVERS (ENHANCED):" -ForegroundColor Yellow
    Write-Host " [6] Start Development Server (Enhanced Port Management)" -ForegroundColor White
    Write-Host " [7] Start Production Server" -ForegroundColor White
    Write-Host " [8] Start Backend Only" -ForegroundColor White
    Write-Host " [9] Start Frontend Only" -ForegroundColor White
    Write-Host " [R] Force Restart All Servers (Enhanced)" -ForegroundColor White
    Write-Host " [T] Open OCR Testing Playground" -ForegroundColor White
    Write-Host ""
    Write-Host " NGROK TUNNELING:" -ForegroundColor Yellow
    Write-Host " [10] Start Ngrok Tunnel (Development)" -ForegroundColor White
    Write-Host " [11] Start Ngrok Tunnel (Production)" -ForegroundColor White
    Write-Host " [12] Monitor Ngrok Connections" -ForegroundColor White
    Write-Host " [13] Stop Ngrok Tunnel" -ForegroundColor White
    Write-Host ""
    Write-Host " SYSTEM MANAGEMENT:" -ForegroundColor Yellow
    Write-Host " [14] Health Check" -ForegroundColor White
    Write-Host " [15] System Diagnostics" -ForegroundColor White
    Write-Host " [16] Clean Cache & Logs" -ForegroundColor White
    Write-Host " [17] Kill All Processes (Smart)" -ForegroundColor White
    Write-Host " [18] Port Conflict Resolution (Manual)" -ForegroundColor White
    Write-Host " [X] Nuclear Kill (If 17 hangs)" -ForegroundColor Red
    Write-Host " [V] Version Control Sync (Git)" -ForegroundColor White
    Write-Host " [U] Update Dependencies (Force Check)" -ForegroundColor White
    Write-Host " [M] Manage Themes" -ForegroundColor White
    Write-Host ""
    Write-Host " [Q] Quit" -ForegroundColor Red
    Write-Host ""
    Write-Host "===============================================================================" -ForegroundColor Cyan
    Write-Host ""
    # Show current status
    Show-CurrentStatus
}

# Function to show current status
function Show-CurrentStatus {
    Write-Host " CURRENT STATUS:" -ForegroundColor Magenta
    # Use health check for accurate status
    $serverStatus = Test-ServersRunningAndHealthy

    Write-Host " Frontend (Port $script:devFrontendPort or $script:prodFrontendPort): " -NoNewline -ForegroundColor White
    if ($serverStatus.FrontendHealthy) {
        Write-Host "RUNNING" -ForegroundColor Green
    } else {
        Write-Host "STOPPED" -ForegroundColor Red
    }

    Write-Host " Backend (Port $script:backendPort): " -NoNewline -ForegroundColor White
    if ($serverStatus.BackendHealthy) {
        Write-Host "RUNNING" -ForegroundColor Green
    } else {
        Write-Host "STOPPED" -ForegroundColor Red
    }

    # Check ngrok status
    $ngrokStatus = Get-NgrokStatus
    Write-Host " Ngrok Tunnel: " -NoNewline -ForegroundColor White
    if ($ngrokStatus.IsRunning) {
        Write-Host "ACTIVE" -ForegroundColor Green
        Write-Host " Public URL: " -NoNewline -ForegroundColor White
        Write-Host $ngrokStatus.PublicUrl -ForegroundColor Cyan
    } else {
        Write-Host "STOPPED" -ForegroundColor Red
    }
    Write-Host ""
}

# Function to get ngrok status
function Get-NgrokStatus {
    try {
        $response = Invoke-RestMethod -Uri $script:ngrokApiUrl -Method GET -ErrorAction Stop
        $tunnel = $response.tunnels | Where-Object { $_.config.addr -like "*$script:devFrontendPort*" -or $_.config.addr -like "*$script:prodFrontendPort*" }
        if ($tunnel) {
            return @{
                IsRunning = $true
                PublicUrl = $tunnel.public_url
                Connections = $tunnel.metrics.conns.count
            }
        }
    } catch {
        # Ngrok API not available
    }
    return @{
        IsRunning = $false
        PublicUrl = ""
        Connections = 0
    }
}

# Function to find project-specific Node.js processes (FAST VERSION)
function Get-ProjectNodeProcesses {
    $projectProcesses = @()
    $projectPath = $script:projectRoot

    try {
        # Fast method: Use CIM instead of WMI with timeout
        $timeout = 10  # 10 second timeout
        $job = Start-Job -ScriptBlock {
            param($projectPath)

            $projectProcesses = @()
            # Use Get-CimInstance which is faster than Get-WmiObject
            $nodeProcesses = Get-CimInstance -ClassName Win32_Process -Filter "Name='node.exe'" -ErrorAction SilentlyContinue

            foreach ($proc in $nodeProcesses) {
                try {
                    $commandLine = $proc.CommandLine
                    if ($commandLine) {
                        # Check if the process is running from our project directory
                        if ($commandLine -like "*$projectPath*" -or
                            $commandLine -like "*backend/server.js*" -or
                            $commandLine -like "*nodemon*backend*" -or
                            $commandLine -like "*next*dev*" -or
                            $commandLine -like "*dev:frontend*" -or
                            $commandLine -like "*next*start*") {

                            # Get the actual Process object for killing
                            $processObj = Get-Process -Id $proc.ProcessId -ErrorAction SilentlyContinue
                            if ($processObj) {
                                $projectProcesses += [PSCustomObject]@{
                                    Process = $processObj
                                    CommandLine = $commandLine
                                    Type = "Node.js"
                                }
                            }
                        }
                    }
                } catch {
                    # Skip processes we can't access
                }
            }
            return $projectProcesses
        } -ArgumentList $projectPath

        # Wait for job with timeout
        $completed = Wait-Job -Job $job -Timeout $timeout
        if ($completed) {
            $projectProcesses = Receive-Job -Job $job
        } else {
            Write-Log "Process detection timed out, using fallback method" "WARN"
            Stop-Job -Job $job
        }
        Remove-Job -Job $job -Force -ErrorAction SilentlyContinue

    } catch {
        Write-Log "CIM query failed, using fallback method" "WARN"
    }

    # Fallback: If CIM fails, use simple process name detection
    if ($projectProcesses.Count -eq 0) {
        Write-Log "Using simple fallback process detection" "INFO"
        $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
        # Just return first few node processes as project processes (safer than hanging)
        $projectProcesses = $nodeProcesses | Select-Object -First 5 | ForEach-Object {
            [PSCustomObject]@{
                Process = $_
                CommandLine = "node.exe (fallback detection)"
                Type = "Node.js"
            }
        }
    }

    return $projectProcesses
}

# Helper function to get specific Node processes quickly (no WMI timeouts)
function Get-NodeProcessesByType {
    param(
        [string]$Type  # "backend", "frontend", or "all"
    )

    try {
        # Fast port-based detection first
        $processes = @()

        if ($Type -eq "backend" -or $Type -eq "all") {
            # Find backend by port 3003
            $netstat = netstat -ano | Select-String ":$script:backendPort\s"
            if ($netstat) {
                $processIds = $netstat | ForEach-Object {
                    $parts = $_ -split '\s+'
                    $parts[$parts.Length - 1]
                } | Select-Object -Unique | Where-Object { $_ -match '^\d+$' }

                foreach ($processId in $processIds) {
                    $processObj = Get-Process -Id $processId -ErrorAction SilentlyContinue
                    if ($processObj -and $processObj.Name -eq "node") {
                        $processes += $processObj
                    }
                }
            }
        }

        if ($Type -eq "frontend" -or $Type -eq "all") {
            # Find frontend by port 3030
            $netstat = netstat -ano | Select-String ":$script:devFrontendPort\s"
            if ($netstat) {
                $processIds = $netstat | ForEach-Object {
                    $parts = $_ -split '\s+'
                    $parts[$parts.Length - 1]
                } | Select-Object -Unique | Where-Object { $_ -match '^\d+$' }

                foreach ($processId in $processIds) {
                    $processObj = Get-Process -Id $processId -ErrorAction SilentlyContinue
                    if ($processObj -and $processObj.Name -eq "node") {
                        $processes += $processObj
                    }
                }
            }
        }

        return $processes

    } catch {
        Write-Log "Fast process detection failed: $($_.Exception.Message)" "WARN"
        return @()
    }
}

# Function to show what processes would be killed (for debugging/safety)
function Show-ProjectProcesses {
    Write-Host ""
    Write-Host "=== PROJECT-SPECIFIC PROCESSES ===" -ForegroundColor Cyan

    # Use fast detection instead of slow WMI queries
    $backendProcs = Get-NodeProcessesByType -Type "backend"
    $frontendProcs = Get-NodeProcessesByType -Type "frontend"
    $allProjectProcs = $backendProcs + $frontendProcs

    if ($allProjectProcs.Count -eq 0) {
        Write-Host "No project-specific Node.js processes found" -ForegroundColor Green
    } else {
        Write-Host "Found $($allProjectProcs.Count) project-specific Node.js processes:" -ForegroundColor Yellow
        foreach ($proc in $allProjectProcs) {
            Write-Host " • PID: $($proc.Id) | Process: $($proc.Name)" -ForegroundColor Gray
        }
    }

    # Show processes on our ports
    $ports = @($script:devFrontendPort, $script:prodFrontendPort, $script:backendPort, 4040)
    foreach ($port in $ports) {
        $netstat = netstat -ano | Select-String ":$port"
        if ($netstat) {
            Write-Host "Processes using port ${port}:" -ForegroundColor Yellow
            $pids = $netstat | ForEach-Object { ($_ -split '\s+')[-1] } | Select-Object -Unique
            foreach ($procId in $pids) {
                if ($procId -match '^\d+$') {
                    $processInfo = Get-Process -Id $procId -ErrorAction SilentlyContinue
                    if ($processInfo) {
                        Write-Host " • PID: $procId | Process: $($processInfo.Name)" -ForegroundColor Gray
                    }
                }
            }
        }
    }
    # Show ngrok processes
    $ngrokProcesses = Get-Process -Name "ngrok" -ErrorAction SilentlyContinue
    if ($ngrokProcesses) {
        Write-Host "Ngrok processes:" -ForegroundColor Yellow
        foreach ($proc in $ngrokProcesses) {
            Write-Host " • PID: $($proc.Id) | Process: $($proc.Name)" -ForegroundColor Gray
        }
    }
    Write-Host ""
}

# Function to kill existing processes
function Stop-AllProcesses {
    Write-Log "Stopping project-specific processes..." "INFO"
    $killedCount = 0
    # Stop PowerShell background jobs first
    if ($script:frontendProcess) {
        Stop-Job -Job $script:frontendProcess -ErrorAction SilentlyContinue
        Remove-Job -Job $script:frontendProcess -Force -ErrorAction SilentlyContinue
        Write-Log "Stopped frontend PowerShell job" "SUCCESS"
        $script:frontendProcess = $null
        $killedCount++
    }
    if ($script:backendProcess) {
        Stop-Job -Job $script:backendProcess -ErrorAction SilentlyContinue
        Remove-Job -Job $script:backendProcess -Force -ErrorAction SilentlyContinue
        Write-Log "Stopped backend PowerShell job" "SUCCESS"
        $script:backendProcess = $null
        $killedCount++
    }
    # Method 1: Kill project-specific Node.js processes by command line detection
    $projectProcesses = Get-ProjectNodeProcesses
    foreach ($procInfo in $projectProcesses) {
        try {
            $procInfo.Process.Kill()
            Write-Log "Killed project Node.js process (PID: $($procInfo.Process.Id))" "SUCCESS"
            $killedCount++
        } catch {
            Write-Log "Failed to kill project Node.js process (PID: $($procInfo.Process.Id))" "WARN"
        }
    }
    # Method 2: Kill processes using our specific ports (fallback/additional safety)
    $ports = @($script:devFrontendPort, $script:prodFrontendPort, $script:backendPort, 4040)
    foreach ($port in $ports) {
        try {
            $netstat = netstat -ano | Select-String ":$port"
            if ($netstat) {
                $pids = $netstat | ForEach-Object { ($_ -split '\s+')[-1] } | Select-Object -Unique
                foreach ($procId2 in $pids) {
                    if ($procId2 -match '^\d+$') {
                        # Get process info before killing
                        $processInfo = Get-Process -Id $procId2 -ErrorAction SilentlyContinue
                        if ($processInfo) {
                            # Double-check this wasn't already killed by method 1
                            $alreadyKilled = $projectProcesses | Where-Object { $_.Process.Id -eq $procId2 }
                            if (-not $alreadyKilled) {
                                taskkill /PID $procId2 /F 2>$null
                                Write-Log "Killed process on port ${port}: $($processInfo.Name) (PID: $procId2)" "SUCCESS"
                                $killedCount++
                            }
                        }
                    }
                }
            }
        } catch {
            Write-Log "Error checking port $port" "WARN"
        }
    }
    # Method 3: Kill ngrok processes (they're usually safe to kill)
    $ngrokProcesses = Get-Process -Name "ngrok" -ErrorAction SilentlyContinue
    foreach ($proc in $ngrokProcesses) {
        try {
            $proc.Kill()
            Write-Log "Killed ngrok process (PID: $($proc.Id))" "SUCCESS"
            $killedCount++
        } catch {
            Write-Log "Failed to kill ngrok process: $($proc.Name)" "WARN"
        }
    }
    if ($killedCount -eq 0) {
        Write-Log "No project processes found to stop" "INFO"
    } else {
        Write-Log "Stopped $killedCount project-related processes" "SUCCESS"
    }
    Start-Sleep -Seconds 2
}

# Function to build the project
function Build-Project {
    Write-Log "Building project for production..." "INFO"
    Push-Location $script:projectRoot
    try {
        # Install dependencies if needed
        if (!(Test-Path "node_modules")) {
            Write-Log "Installing dependencies..." "INFO"
            bun install
        }
        # Build the project
        Write-Log "Running production build..." "INFO"
        bun run build
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Build completed successfully!" "SUCCESS"
        } else {
            Write-Log "Build failed!" "ERROR"
        }
    } catch {
        Write-Log "Build error: $($_.Exception.Message)" "ERROR"
    } finally {
        Pop-Location
    }
}

# Function to run tests
function Run-Tests {
    param(
        [ValidateSet("all", "e2e", "unit")]
        [string]$TestType = "all"
    )

    Write-Log "Running tests: $TestType" "INFO"
    Push-Location $script:projectRoot
    try {
        switch ($TestType) {
            "all" { bun run test }
            "e2e" { bun run test:e2e }
            "unit" { bun run test:integration }  # Note: package.json has test:integration, assuming unit
        }
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Tests completed successfully!" "SUCCESS"
        } else {
            Write-Log "Tests failed!" "ERROR"
        }
    } catch {
        Write-Log "Test error: $($_.Exception.Message)" "ERROR"
    } finally {
        Pop-Location
    }
}

# Function to run lint and type check
function Run-LintAndTypeCheck {
    Write-Log "Running lint and type check..." "INFO"
    Push-Location $script:projectRoot
    try {
        Write-Log "Running ESLint..." "INFO"
        bun run lint
        Write-Log "Running TypeScript check..." "INFO"
        bun run typecheck
        Write-Log "Lint and type check completed!" "SUCCESS"
    } catch {
        Write-Log "Lint/Type check error: $($_.Exception.Message)" "ERROR"
    } finally {
        Pop-Location
    }
}

# Enhanced Development Server Startup with Intelligent Port Management
function Start-DevelopmentServer {
    Write-Log "Starting enhanced development server with intelligent port management..." "INFO"

    Write-Host ""
    Write-Host "🚀 ENHANCED DEVELOPMENT SERVER STARTUP" -ForegroundColor Green
    Write-Host "This will:" -ForegroundColor White
    Write-Host " • Detect and resolve port conflicts intelligently" -ForegroundColor Gray
    Write-Host " • Kill only conflicting processes (not all Node processes)" -ForegroundColor Gray
    Write-Host " • Preserve route configurations by maintaining consistent ports" -ForegroundColor Gray
    Write-Host " • Start frontend and backend servers in correct order" -ForegroundColor Gray
    Write-Host " • Verify successful startup with health checks" -ForegroundColor Gray
    Write-Host " • Update environment configuration" -ForegroundColor Gray
    Write-Host ""

    # Step 1: Resolve port conflicts
    Write-Host "🔍 STEP 1: PORT CONFLICT DETECTION" -ForegroundColor Cyan
    $conflictsResolved = Resolve-PortConflicts
    if (-not $conflictsResolved) {
        Write-Log "Cannot start servers due to unresolved port conflicts" "ERROR"
        return $false
    }

    # Step 2: Update port configuration
    Write-Host ""
    Write-Host "⚙️ STEP 2: UPDATING CONFIGURATION" -ForegroundColor Cyan
    Update-PortConfiguration

    # Step 3: Check dependencies
    Write-Host ""
    Write-Host "📦 STEP 3: DEPENDENCY CHECK" -ForegroundColor Cyan
    Check-AndUpdateDependencies

    # Step 4: Start servers
    Write-Host ""
    Write-Host "🚀 STEP 4: STARTING SERVERS" -ForegroundColor Cyan

    Push-Location $script:projectRoot
    try {
        # Start backend first
        Write-Log "Starting backend server on port $script:backendPort..." "INFO"
        $script:backendProcess = Start-Job -ScriptBlock {
            Set-Location $using:script:projectRoot
            & bun run --hot backend/server.js
        }

        # Wait for backend to initialize
        Write-Host "⏳ Waiting for backend to initialize..." -ForegroundColor Yellow
        Start-Sleep -Seconds 5

        # Verify backend health
        $backendHealthy = Test-ServerHealth -Port $script:backendPort
        if (-not $backendHealthy) {
            Write-Log "Backend failed to start properly" "ERROR"
            return $false
        }
        Write-Log "Backend server started successfully on port $script:backendPort" "SUCCESS"

        # Start frontend
        Write-Log "Starting frontend server on port $script:devFrontendPort..." "INFO"
        $script:frontendProcess = Start-Job -ScriptBlock {
            Set-Location $using:script:projectRoot
            & bun run dev:frontend
        }

        # Wait for frontend to initialize
        Write-Host "⏳ Waiting for frontend to initialize..." -ForegroundColor Yellow
        Start-Sleep -Seconds 8

        # Step 5: Verify startup
        Write-Host ""
        Write-Host "✅ STEP 5: STARTUP VERIFICATION" -ForegroundColor Cyan
        $serverStatus = Test-ServersRunningAndHealthy

        if ($serverStatus.BothHealthy) {
            Write-Host ""
            Write-Host "🎉 DEVELOPMENT SERVER STARTUP COMPLETE!" -ForegroundColor Green
            Write-Host ""
            Write-Host "📊 Server Status:" -ForegroundColor White
            Write-Host "  • Frontend: " -NoNewline -ForegroundColor White
            Write-Host "http://localhost:$script:devFrontendPort" -ForegroundColor Cyan
            Write-Host "  • Backend:  " -NoNewline -ForegroundColor White
            Write-Host "http://localhost:$script:backendPort" -ForegroundColor Cyan
            Write-Host ""
            Write-Host "💡 All API routes are properly configured for these ports" -ForegroundColor Green
            Write-Host "� No manual port changes needed - everything is ready!" -ForegroundColor Green
            Write-Host ""
            Write-Host "💡 TIP: Use Ctrl+C to stop servers when done" -ForegroundColor Yellow
            Write-Log "Enhanced development server started successfully!" "SUCCESS"
            return $true
        } else {
            Write-Host ""
            Write-Host "❌ SERVER STARTUP FAILED!" -ForegroundColor Red
            Write-Host "  Frontend Health: $($serverStatus.FrontendHealthy)" -ForegroundColor Red
            Write-Host "  Backend Health: $($serverStatus.BackendHealthy)" -ForegroundColor Red
            Write-Host ""
            Write-Host "🔧 Troubleshooting options:" -ForegroundColor Yellow
            Write-Host "  • Option [14] - Health Check" -ForegroundColor Cyan
            Write-Host "  • Option [15] - System Diagnostics" -ForegroundColor Cyan
            Write-Host "  • Option [17] - Kill All Processes (Smart)" -ForegroundColor Cyan
            Write-Log "Enhanced development server failed to start properly" "ERROR"
            return $false
        }

    } catch {
        Write-Log "Error during enhanced development server startup: $($_.Exception.Message)" "ERROR"
        return $false
    } finally {
        Pop-Location
    }
}

# Enhanced Force Restart with Intelligent Port Management
function Force-RestartDevelopmentServers {
    Write-Log "Force restarting all development servers with intelligent port management..." "INFO"

    Write-Host ""
    Write-Host "🔄 FORCE RESTART - ENHANCED MODE" -ForegroundColor Yellow
    Write-Host "This will:" -ForegroundColor White
    Write-Host " • Force kill all project-related processes" -ForegroundColor Gray
    Write-Host " • Resolve any remaining port conflicts" -ForegroundColor Gray
    Write-Host " • Start servers with proper configuration" -ForegroundColor Gray
    Write-Host " • Verify successful startup" -ForegroundColor Gray
    Write-Host ""

    # Step 1: Force kill all processes
    Write-Host "🛑 STEP 1: STOPPING ALL PROCESSES" -ForegroundColor Cyan
    Stop-AllProcesses

    # Step 2: Resolve any remaining port conflicts (force mode)
    Write-Host ""
    Write-Host "🔍 STEP 2: RESOLVING REMAINING CONFLICTS" -ForegroundColor Cyan
    $conflictsResolved = Resolve-PortConflicts -ForceKill $true
    if (-not $conflictsResolved) {
        Write-Log "Cannot restart servers due to persistent port conflicts" "ERROR"
        return $false
    }

    # Step 3: Update configuration
    Write-Host ""
    Write-Host "⚙️ STEP 3: UPDATING CONFIGURATION" -ForegroundColor Cyan
    Update-PortConfiguration

    # Step 4: Start servers
    Write-Host ""
    Write-Host "🚀 STEP 4: STARTING SERVERS" -ForegroundColor Cyan

    Push-Location $script:projectRoot
    try {
        # Start backend first
        Write-Log "Starting backend server on port $script:backendPort..." "INFO"
        $script:backendProcess = Start-Job -ScriptBlock {
            Set-Location $using:script:projectRoot
            & bun run --hot backend/server.js
        }

        # Wait for backend to initialize
        Write-Host "⏳ Waiting for backend to initialize..." -ForegroundColor Yellow
        Start-Sleep -Seconds 5

        # Verify backend health
        $backendHealthy = Test-ServerHealth -Port $script:backendPort
        if (-not $backendHealthy) {
            Write-Log "Backend failed to start after force restart" "ERROR"
            return $false
        }
        Write-Log "Backend server restarted successfully on port $script:backendPort" "SUCCESS"

        # Start frontend
        Write-Log "Starting frontend server on port $script:devFrontendPort..." "INFO"
        $script:frontendProcess = Start-Job -ScriptBlock {
            Set-Location $using:script:projectRoot
            & bun run dev:frontend
        }

        # Wait for frontend to initialize
        Write-Host "⏳ Waiting for frontend to initialize..." -ForegroundColor Yellow
        Start-Sleep -Seconds 8

        # Step 5: Verify startup
        Write-Host ""
        Write-Host "✅ STEP 5: RESTART VERIFICATION" -ForegroundColor Cyan
        $serverStatus = Test-ServersRunningAndHealthy

        if ($serverStatus.BothHealthy) {
            Write-Host ""
            Write-Host "🎉 FORCE RESTART COMPLETE!" -ForegroundColor Green
            Write-Host ""
            Write-Host "📊 Server Status:" -ForegroundColor White
            Write-Host "  • Frontend: " -NoNewline -ForegroundColor White
            Write-Host "http://localhost:$script:devFrontendPort" -ForegroundColor Cyan
            Write-Host "  • Backend:  " -NoNewline -ForegroundColor White
            Write-Host "http://localhost:$script:backendPort" -ForegroundColor Cyan
            Write-Host ""
            Write-Log "All servers force restarted successfully!" "SUCCESS"
            return $true
        } else {
            Write-Host ""
            Write-Host "❌ FORCE RESTART FAILED!" -ForegroundColor Red
            Write-Host "  Frontend Health: $($serverStatus.FrontendHealthy)" -ForegroundColor Red
            Write-Host "  Backend Health: $($serverStatus.BackendHealthy)" -ForegroundColor Red
            Write-Log "Some servers failed to start after force restart!" "ERROR"
            return $false
        }
    } catch {
        Write-Log "Error during force restart: $($_.Exception.Message)" "ERROR"
        return $false
    } finally {
        Pop-Location
    }
}

# Function to start production server
function Start-ProductionServer {
    Write-Log "Starting production server..." "INFO"
    # Kill existing processes
    Stop-AllProcesses
    Push-Location $script:projectRoot
    try {
        # Build first
        Build-Project
        # Start production server
        Write-Log "Starting production server..." "INFO"
        $script:frontendProcess = Start-Job -ScriptBlock {
            Set-Location $using:script:projectRoot
            & bun run start:frontend
        }
        $script:backendProcess = Start-Job -ScriptBlock {
            Set-Location $using:script:projectRoot
            & bun run start:backend
        }
        Start-Sleep -Seconds 5
        # Verify server is running
        $health = Test-ServerHealth -Port $script:prodFrontendPort
        if ($health) {
            Write-Log "Production server started successfully!" "SUCCESS"
            Write-Log "Application: http://localhost:$script:prodFrontendPort" "INFO"
        } else {
            Write-Log "Production server failed to start!" "ERROR"
        }
    } catch {
        Write-Log "Error starting production server: $($_.Exception.Message)" "ERROR"
    } finally {
        Pop-Location
    }
}

# Function to create/update port configuration file
function Update-PortConfiguration {
    Write-Log "Updating port configuration..." "INFO"

    # Ensure data directory exists
    $dataDir = "$script:projectRoot\data"
    if (!(Test-Path $dataDir)) {
        New-Item -ItemType Directory -Path $dataDir -Force | Out-Null
    }

    # Create port configuration object
    $portConfig = @{
        ports = @{
            frontend = $script:devFrontendPort
            backend = $script:backendPort
            ngrok = 4040
        }
        environment = @{
            FRONTEND_PORT = $script:devFrontendPort.ToString()
            BACKEND_PORT = $script:backendPort.ToString()
            BACKEND_URL = "http://localhost:$script:backendPort"
            NODE_ENV = "development"
        }
        urls = @{
            frontend = "http://localhost:$script:devFrontendPort"
            backend = "http://localhost:$script:backendPort"
        }
        lastUpdated = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        updatedBy = "launcher.ps1"
    }

    # Write configuration to file
    $configPath = "$dataDir\port-config.json"
    try {
        $portConfig | ConvertTo-Json -Depth 10 | Set-Content -Path $configPath -Encoding UTF8
        Write-Log "Port configuration saved to: $configPath" "SUCCESS"
        Write-Log "Frontend: http://localhost:$script:devFrontendPort" "INFO"
        Write-Log "Backend: http://localhost:$script:backendPort" "INFO"
    } catch {
        Write-Log "Failed to save port configuration: $($_.Exception.Message)" "ERROR"
    }
}

# Function to start backend only
function Start-BackendOnly {
    # Update port configuration first
    Update-PortConfiguration

    Write-Log "Starting backend server only..." "INFO"

    # Check if backend is already running
    $backendHealthy = Test-ServerHealth -Port $script:backendPort
    if ($backendHealthy) {
        Write-Log "Backend server is already running and healthy!" "SUCCESS"
        Write-Log "Backend API: http://localhost:$script:backendPort" "INFO"
        return
    }

    Push-Location $script:projectRoot
    try {
        # Kill only backend processes if needed (using fast detection)
        $backendProcs = Get-NodeProcessesByType -Type "backend"
        foreach ($proc in $backendProcs) {
            try {
                $proc.Kill()
                Write-Log "Killed existing backend process (PID: $($proc.Id))" "INFO"
            } catch {
                Write-Log "Could not kill backend process (PID: $($proc.Id))" "WARN"
            }
        }

        Write-Log "Starting backend server..." "INFO"
        $script:backendProcess = Start-Job -ScriptBlock {
            Set-Location $using:script:projectRoot
            & bun run --hot backend/server.js
        }
        Start-Sleep -Seconds 5
        $health = Test-ServerHealth -Port $script:backendPort
        if ($health) {
            Write-Log "Backend server started successfully!" "SUCCESS"
            Write-Log "Backend API: http://localhost:$script:backendPort" "INFO"
        } else {
            Write-Log "Backend server failed to start!" "ERROR"
        }
    } catch {
        Write-Log "Error starting backend server: $($_.Exception.Message)" "ERROR"
    } finally {
        Pop-Location
    }
}

# Function to start frontend only
function Start-FrontendOnly {
    Write-Log "Starting frontend server only..." "INFO"

    # Check if frontend is already running
    $frontendHealthy = Test-ServerHealth -Port $script:devFrontendPort
    if ($frontendHealthy) {
        Write-Log "Frontend server is already running and healthy!" "SUCCESS"
        Write-Log "Frontend: http://localhost:$script:devFrontendPort" "INFO"
        return
    }

    Push-Location $script:projectRoot
    try {
        # Kill only frontend processes if needed (using fast detection)
        $frontendProcs = Get-NodeProcessesByType -Type "frontend"
        foreach ($proc in $frontendProcs) {
            try {
                $proc.Kill()
                Write-Log "Killed existing frontend process (PID: $($proc.Id))" "INFO"
            } catch {
                Write-Log "Could not kill frontend process (PID: $($proc.Id))" "WARN"
            }
        }

        Write-Log "Starting frontend server..." "INFO"
        $script:frontendProcess = Start-Job -ScriptBlock {
            Set-Location $using:script:projectRoot
            & bun run dev:frontend
        }
        Start-Sleep -Seconds 8
        $health = Test-ServerHealth -Port $script:devFrontendPort
        if ($health) {
            Write-Log "Frontend server started successfully!" "SUCCESS"
            Write-Log "Frontend: http://localhost:$script:devFrontendPort" "INFO"
        } else {
            Write-Log "Frontend server failed to start!" "ERROR"
        }
    } catch {
        Write-Log "Error starting frontend server: $($_.Exception.Message)" "ERROR"
    } finally {
        Pop-Location
    }
}

# Function to start OCR Testing Playground
function Start-OCRTestingPlayground {
    Write-Log "Starting OCR Testing Playground..." "INFO"
    Write-Host ""
    Write-Host "🧪 STARTING OCR TESTING PLAYGROUND" -ForegroundColor Green
    Write-Host "This will:" -ForegroundColor White
    Write-Host " • Start frontend server (if not running)" -ForegroundColor Gray
    Write-Host " • Start backend server (if not running)" -ForegroundColor Gray
    Write-Host " • Open OCR Testing Playground in your browser" -ForegroundColor Gray
    Write-Host ""
    # Check if servers are running using smart detection
    $serverStatus = Test-ServersRunningAndHealthy
    if (-not $serverStatus.BothHealthy) {
        Write-Host "🚀 Starting development servers..." -ForegroundColor Yellow
        Start-DevelopmentServer
        # Wait for servers to fully start
        Write-Host "⏳ Waiting for servers to initialize..." -ForegroundColor Yellow
        Start-Sleep -Seconds 8
        # Re-check health
        $serverStatus = Test-ServersRunningAndHealthy
    }
    if ($serverStatus.BothHealthy) {
        Write-Host "✅ Servers are running!" -ForegroundColor Green
        Write-Host " Frontend: http://localhost:$script:devFrontendPort" -ForegroundColor Cyan
        Write-Host " Backend: http://localhost:$script:backendPort" -ForegroundColor Cyan
        Write-Host ""
        # Open OCR Testing Playground in browser
        Write-Host "🌐 Opening OCR Testing Playground..." -ForegroundColor Yellow
        $ocrTestingUrl = "http://localhost:$script:devFrontendPort/ocr-testing"
        try {
            Start-Process $ocrTestingUrl
            Write-Log "OCR Testing Playground opened: $ocrTestingUrl" "SUCCESS"
            Write-Host "✅ OCR Testing Playground Started!" -ForegroundColor Green
            Write-Host " URL: $ocrTestingUrl" -ForegroundColor Cyan
            Write-Host ""
            Write-Host "💡 TIP: Use this playground to test multiple AI models on driver's license images" -ForegroundColor Yellow
            Write-Host "📊 Compare accuracy, speed, and cost across different OCR models" -ForegroundColor Yellow
        } catch {
            Write-Log "Failed to open browser: $($_.Exception.Message)" "WARN"
            Write-Host "❌ Could not open browser automatically" -ForegroundColor Red
            Write-Host " Please open: $ocrTestingUrl" -ForegroundColor Cyan
        }
    } else {
        Write-Host "❌ Servers failed to start!" -ForegroundColor Red
        Write-Host " Frontend Health: $($serverStatus.FrontendHealthy)" -ForegroundColor Red
        Write-Host " Backend Health: $($serverStatus.BackendHealthy)" -ForegroundColor Red
        Write-Host ""
        Write-Host "💡 TIP: Try running option [14] for health check or [15] for diagnostics" -ForegroundColor Yellow
    }
}

# Function to start ngrok tunnel
function Start-NgrokTunnel {
    param(
        [ValidateSet("development", "production")]
        [string]$Environment = "development"
    )

    Write-Log "Starting ngrok tunnel for $Environment..." "INFO"
    # Check if ngrok is installed
    try {
        $null = Get-Command ngrok -ErrorAction Stop
    } catch {
        Write-Log "Ngrok is not installed! Please install from https://ngrok.com/" "ERROR"
        return
    }
    # Kill existing ngrok processes
    Get-Process -Name "ngrok" -ErrorAction SilentlyContinue | Stop-Process -Force
    $port = if ($Environment -eq "development") { $script:devFrontendPort } else { $script:prodFrontendPort }
    try {
        # Start ngrok tunnel
        Write-Log "Starting ngrok tunnel on port $port..." "INFO"
        $script:ngrokProcess = Start-Process -FilePath "ngrok" -ArgumentList "http", $port -WindowStyle Minimized -PassThru
        Start-Sleep -Seconds 5
        # Get tunnel info
        $ngrokStatus = Get-NgrokStatus
        if ($ngrokStatus.IsRunning) {
            Write-Log "Ngrok tunnel started successfully!" "SUCCESS"
            Write-Log "Public URL: $($ngrokStatus.PublicUrl)" "INFO"
            Write-Log "Local URL: http://localhost:$port" "INFO"
            # Start connection monitoring
            Start-ConnectionMonitoring
        } else {
            Write-Log "Failed to start ngrok tunnel!" "ERROR"
        }
    } catch {
        Write-Log "Error starting ngrok tunnel: $($_.Exception.Message)" "ERROR"
    }
}

# Function to stop ngrok tunnel
function Stop-NgrokTunnel {
    Write-Log "Stopping ngrok tunnel..." "INFO"
    Get-Process -Name "ngrok" -ErrorAction SilentlyContinue | Stop-Process -Force
    $script:ngrokProcess = $null
    Write-Log "Ngrok tunnel stopped!" "SUCCESS"
}

# Function to start connection monitoring
function Start-ConnectionMonitoring {
    Write-Log "Starting connection monitoring..." "INFO"
    $job = Start-Job -ScriptBlock {
        param($apiUrl, $logFile)
        while ($true) {
            try {
                $response = Invoke-RestMethod -Uri $apiUrl -Method GET -ErrorAction Stop
                $tunnel = $response.tunnels | Where-Object { $_.config.addr -like "*$script:devFrontendPort*" -or $_.config.addr -like "*$script:prodFrontendPort*" }
                if ($tunnel) {
                    $connections = $tunnel.metrics.conns.count
                    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
                    Add-Content -Path $logFile -Value "[$timestamp] [MONITOR] Active connections: $connections"
                }
            } catch {
                # Connection monitoring error
            }
            Start-Sleep -Seconds 30
        }
    } -ArgumentList $script:ngrokApiUrl, $script:logFile
    Write-Log "Connection monitoring started (Background job: $($job.Id))" "SUCCESS"
}

# Function to monitor ngrok connections
function Monitor-NgrokConnections {
    Write-Log "Monitoring ngrok connections..." "INFO"
    try {
        $response = Invoke-RestMethod -Uri $script:ngrokApiUrl -Method GET -ErrorAction Stop
        Write-Host ""
        Write-Host "===============================================================================" -ForegroundColor Cyan
        Write-Host " NGROK TUNNEL STATUS" -ForegroundColor White
        Write-Host "===============================================================================" -ForegroundColor Cyan
        foreach ($tunnel in $response.tunnels) {
            Write-Host " Tunnel: $($tunnel.name)" -ForegroundColor Yellow
            Write-Host " Public URL: $($tunnel.public_url)" -ForegroundColor Green
            Write-Host " Local URL: $($tunnel.config.addr)" -ForegroundColor Cyan
            Write-Host " Protocol: $($tunnel.proto)" -ForegroundColor White
            Write-Host " Connections: $($tunnel.metrics.conns.count)" -ForegroundColor Magenta
            Write-Host " Requests: $($tunnel.metrics.http.count)" -ForegroundColor Magenta
            Write-Host ""
        }
        Write-Host "===============================================================================" -ForegroundColor Cyan
        Write-Host ""
    } catch {
        Write-Log "Could not connect to ngrok API. Is ngrok running?" "ERROR"
    }
}

# Function to test server health
function Test-ServerHealth {
    param([int]$Port)
    try {
        # Use appropriate health endpoint for backend (port 3003) vs frontend
        if ($Port -eq $script:backendPort) {
            $healthUrl = "http://localhost:$Port/api/health"
        } else {
            $healthUrl = "http://localhost:$Port"
        }

        $response = Invoke-WebRequest -Uri $healthUrl -Method GET -TimeoutSec 10 -ErrorAction Stop
        return $response.StatusCode -eq 200
    } catch {
        return $false
    }
}

# Function to check if servers are already running and healthy
function Test-ServersRunningAndHealthy {
    $frontendHealthy = Test-ServerHealth -Port $script:devFrontendPort
    $backendHealthy = Test-ServerHealth -Port $script:backendPort

    return @{
        FrontendHealthy = $frontendHealthy
        BackendHealthy = $backendHealthy
        BothHealthy = ($frontendHealthy -and $backendHealthy)
        AnyHealthy = ($frontendHealthy -or $backendHealthy)
    }
}

# Function to start only missing servers (smart server management)
function Start-MissingServers {
    Write-Log "Checking server status before starting..." "INFO"
    $serverStatus = Test-ServersRunningAndHealthy

    if ($serverStatus.BothHealthy) {
        Write-Log "Both servers are already running and healthy!" "SUCCESS"
        Write-Log "Frontend: http://localhost:$script:devFrontendPort" "INFO"
        Write-Log "Backend: http://localhost:$script:backendPort" "INFO"
        return $true
    }

    Write-Log "Server Status - Frontend: $($serverStatus.FrontendHealthy), Backend: $($serverStatus.BackendHealthy)" "INFO"

    # Only start servers that aren't running
    if (-not $serverStatus.BackendHealthy) {
        Write-Log "Starting backend server..." "INFO"
        # Kill only backend processes if needed (using fast detection)
        $backendProcs = Get-NodeProcessesByType -Type "backend"
        foreach ($proc in $backendProcs) {
            try {
                $proc.Kill()
                Write-Log "Killed existing backend process (PID: $($proc.Id))" "INFO"
            } catch {
                Write-Log "Could not kill backend process (PID: $($proc.Id))" "WARN"
            }
        }

        $script:backendProcess = Start-Job -ScriptBlock {
            Set-Location $using:projectRoot
            & bun run --hot backend/server.js
        }
        Start-Sleep -Seconds 5
    }

    if (-not $serverStatus.FrontendHealthy) {
        Write-Log "Starting frontend server..." "INFO"
        # Kill only frontend processes if needed (using fast detection)
        $frontendProcs = Get-NodeProcessesByType -Type "frontend"
        foreach ($proc in $frontendProcs) {
            try {
                $proc.Kill()
                Write-Log "Killed existing frontend process (PID: $($proc.Id))" "INFO"
            } catch {
                Write-Log "Could not kill frontend process (PID: $($proc.Id))" "WARN"
            }
        }

        $script:frontendProcess = Start-Job -ScriptBlock {
            Set-Location $using:projectRoot
            & bun run dev:frontend
        }
        Start-Sleep -Seconds 8
    }

    # Re-verify servers are running
    Start-Sleep -Seconds 2
    $finalStatus = Test-ServersRunningAndHealthy
    if ($finalStatus.BothHealthy) {
        Write-Log "All servers started successfully!" "SUCCESS"
        Write-Log "Frontend: http://localhost:$script:devFrontendPort" "INFO"
        Write-Log "Backend: http://localhost:$script:backendPort" "INFO"
        return $true
    } else {
        Write-Log "Some servers failed to start!" "ERROR"
        Write-Log "Frontend Health: $($finalStatus.FrontendHealthy) | Backend Health: $($finalStatus.BackendHealthy)" "ERROR"
        return $false
    }
}

# Enhanced Health Check with API ping, resource monitoring, logging rotation, and alerts
function Run-HealthCheck {
    Write-Log "Running enhanced system health check..." "INFO"
    Write-Host ""
    Write-Host "===============================================================================" -ForegroundColor Cyan
    Write-Host " ENHANCED SYSTEM HEALTH CHECK" -ForegroundColor White
    Write-Host "===============================================================================" -ForegroundColor Cyan

    # Original checks
    try {
        $nodeVersion = node --version
        Write-Host " Node.js: " -NoNewline -ForegroundColor White
        Write-Host "OK ($nodeVersion)" -ForegroundColor Green
    } catch {
        Write-Host " Node.js: " -NoNewline -ForegroundColor White
        Write-Host "ERROR" -ForegroundColor Red
    }
    try {
        $bunVersion = bun --version
        Write-Host " bun: " -NoNewline -ForegroundColor White
        Write-Host "OK ($bunVersion)" -ForegroundColor Green
    } catch {
        Write-Host " bun: " -NoNewline -ForegroundColor White
        Write-Host "ERROR" -ForegroundColor Red
    }
    if (Test-Path "$script:projectRoot\node_modules") {
        Write-Host " Dependencies: " -NoNewline -ForegroundColor White
        Write-Host "INSTALLED" -ForegroundColor Green
    } else {
        Write-Host " Dependencies: " -NoNewline -ForegroundColor White
        Write-Host "NOT INSTALLED" -ForegroundColor Yellow
        Write-Host " → Run 'bun install' to install dependencies" -ForegroundColor Cyan
    }
    $frontendHealth = Test-ServerHealth -Port $script:devFrontendPort
    Write-Host " Frontend Server: " -NoNewline -ForegroundColor White
    if ($frontendHealth) {
        Write-Host "RUNNING" -ForegroundColor Green
    } else {
        Write-Host "STOPPED" -ForegroundColor Red
    }
    $backendHealth = Test-ServerHealth -Port $script:backendPort
    Write-Host " Backend Server: " -NoNewline -ForegroundColor White
    if ($backendHealth) {
        Write-Host "RUNNING" -ForegroundColor Green
    } else {
        Write-Host "STOPPED" -ForegroundColor Red
    }
    $ngrokStatus = Get-NgrokStatus
    Write-Host " Ngrok Tunnel: " -NoNewline -ForegroundColor White
    if ($ngrokStatus.IsRunning) {
        Write-Host "ACTIVE" -ForegroundColor Green
    } else {
        Write-Host "INACTIVE" -ForegroundColor Yellow
    }

    # New: Backend API ping to /api/health
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$script:backendPort/api/health" -Method Get -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Write-Host " Backend API Health: " -NoNewline -ForegroundColor White
            Write-Host "OK" -ForegroundColor Green
        } else {
            Write-Host " Backend API Health: " -NoNewline -ForegroundColor White
            Write-Host "FAILED (Status: $($response.StatusCode))" -ForegroundColor Red
        }
    } catch {
        Write-Host " Backend API Health: " -NoNewline -ForegroundColor White
        Write-Host "ERROR ($_)" -ForegroundColor Red
    }

    # New: Resource monitoring
    $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
    if ($nodeProcesses) {
        $processCount = $nodeProcesses.Count
        $totalMemory = ($nodeProcesses | Measure-Object -Property WorkingSet -Sum).Sum / 1MB
        $totalCpuTime = ($nodeProcesses | Measure-Object -Property CPU -Sum).Sum

        Write-Host " Node Processes: " -NoNewline -ForegroundColor White
        Write-Host "$processCount running" -ForegroundColor Green
        Write-Host " Node Processes Memory Usage: " -NoNewline -ForegroundColor White
        Write-Host "$([math]::Round($totalMemory, 1)) MB" -ForegroundColor Green
        Write-Host " Node Processes Total CPU Time: " -NoNewline -ForegroundColor White
        Write-Host "$([math]::Round($totalCpuTime, 1)) seconds" -ForegroundColor Green

        # Alert for high memory usage only (CPU time is cumulative, not current usage)
        if ($totalMemory -gt 1024) { Write-Log "High memory usage detected (>1GB)" "WARN" }
    } else {
        Write-Host " No Node processes running" -ForegroundColor Yellow
    }

    # New: Logging rotation (delete logs older than 7 days)
    $oldLogs = Get-ChildItem -Path $logsDir -Filter "*.log" | Where-Object { $_.LastWriteTime -lt (Get-Date).AddDays(-7) }
    foreach ($log in $oldLogs) {
        Remove-Item $log.FullName -Force
        Write-Log "Rotated old log: $($log.Name)" "INFO"
    }

    Write-Host ""
    Write-Host "===============================================================================" -ForegroundColor Cyan
    Write-Host ""
}

# Function to run system diagnostics
function Run-SystemDiagnostics {
    Write-Log "Running system diagnostics..." "INFO"
    Write-Host ""
    Write-Host "===============================================================================" -ForegroundColor Cyan
    Write-Host " SYSTEM DIAGNOSTICS" -ForegroundColor White
    Write-Host "===============================================================================" -ForegroundColor Cyan
    # System information
    Write-Host " SYSTEM INFORMATION:" -ForegroundColor Yellow
    Write-Host " OS: $([System.Environment]::OSVersion.VersionString)" -ForegroundColor White
    Write-Host " PowerShell: $($PSVersionTable.PSVersion)" -ForegroundColor White
    Write-Host " CPU Cores: $([System.Environment]::ProcessorCount)" -ForegroundColor White
    Write-Host " Memory: $([math]::Round((Get-CimInstance -ClassName Win32_ComputerSystem).TotalPhysicalMemory / 1GB, 2)) GB" -ForegroundColor White
    Write-Host ""
    # Port status
    Write-Host " PORT STATUS:" -ForegroundColor Yellow
    $ports = @($script:devFrontendPort, $script:prodFrontendPort, $script:backendPort, 4040)
    foreach ($port in $ports) {
        $connection = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
        if ($connection) {
            Write-Host " Port ${port}: " -NoNewline -ForegroundColor White
            Write-Host "IN USE" -ForegroundColor Green
        } else {
            Write-Host " Port ${port}: " -NoNewline -ForegroundColor White
            Write-Host "AVAILABLE" -ForegroundColor Yellow
        }
    }
    Write-Host ""
    # Show detailed process information
    Show-ProjectProcesses
    Write-Host "===============================================================================" -ForegroundColor Cyan
    Write-Host ""
}

# Function to clean cache and logs
function Clean-CacheAndLogs {
    Write-Log "Cleaning cache and logs..." "INFO"
    $cacheDirs = @(
        "$script:projectRoot\data\cache",
        "$script:projectRoot\data\thumbnails",
        "$script:projectRoot\data\previews",
        "$script:projectRoot\data\temp",
        "$script:projectRoot\.next",
        "$script:projectRoot\node_modules\.cache"
    )
    $logDirs = @(
        "$script:projectRoot\data\logs"
    )
    # Clean cache directories
    foreach ($dir in $cacheDirs) {
        if (Test-Path $dir) {
            try {
                Remove-Item -Path "$dir\*" -Recurse -Force
                Write-Log "Cleaned cache: $dir" "SUCCESS"
            } catch {
                Write-Log "Failed to clean cache: $dir" "WARN"
            }
        }
    }
    # Clean old log files (keep last 10 days)
    foreach ($dir in $logDirs) {
        if (Test-Path $dir) {
            $oldLogs = Get-ChildItem -Path $dir -Filter "*.log" | Where-Object { $_.LastWriteTime -lt (Get-Date).AddDays(-10) }
            foreach ($log in $oldLogs) {
                try {
                    Remove-Item -Path $log.FullName -Force
                    Write-Log "Removed old log: $($log.Name)" "SUCCESS"
                } catch {
                    Write-Log "Failed to remove log: $($log.Name)" "WARN"
                }
            }
        }
    }
    Write-Log "Cache and logs cleaned successfully!" "SUCCESS"
}

# QUICK START FUNCTIONS
# Function to start full development environment
function Start-FullDevelopmentEnvironment {
    Write-Log "Starting Full Development Environment..." "INFO"
    Write-Host ""
    Write-Host "🚀 STARTING FULL DEVELOPMENT ENVIRONMENT (SMART)" -ForegroundColor Green
    Write-Host "This will:" -ForegroundColor White
    Write-Host " • Automatically resolve port conflicts" -ForegroundColor Gray
    Write-Host " • Start Frontend (Next.js) and Backend (Express)" -ForegroundColor Gray
    Write-Host " • Initialize database and enable hot reload" -ForegroundColor Gray
    Write-Host " • Display actual ports after resolution" -ForegroundColor Gray
    Write-Host ""
    # Run health check first
    Write-Host "🔍 Running health check..." -ForegroundColor Yellow
    Run-HealthCheck
    # Start development server
    Write-Host "🚀 Starting development servers..." -ForegroundColor Yellow
    $serverStartSuccess = Start-DevelopmentServer
    Write-Host ""

    if ($serverStartSuccess) {
        Write-Host "✅ Full Development Environment Started!" -ForegroundColor Green
        Write-Host ""
        Write-Host "📊 Server URLs are displayed above (check port resolution output)" -ForegroundColor Cyan
        Write-Host "💡 TIP: Ports are automatically resolved to avoid conflicts" -ForegroundColor Yellow
        Write-Host "🔍 Look for the exact URLs in the output above" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "💡 TIP: Use Ctrl+C to stop servers when done" -ForegroundColor Yellow
    } else {
        Write-Host "❌ Full Development Environment Failed to Start!" -ForegroundColor Red
        Write-Host "Please check the error messages above and try:" -ForegroundColor Yellow
        Write-Host " • Option [14] - Health Check" -ForegroundColor Cyan
        Write-Host " • Option [15] - System Diagnostics" -ForegroundColor Cyan
        Write-Host " • Option [17] - Kill All Processes and retry" -ForegroundColor Cyan
        Write-Host ""
    }
}

# Function to start development with ngrok tunnel
function Start-DevelopmentWithNgrok {
    Write-Log "Starting Development Environment with Ngrok Tunnel..." "INFO"
    Write-Host ""
    Write-Host "🌐 STARTING DEVELOPMENT + NGROK TUNNEL" -ForegroundColor Green
    Write-Host "This will start:" -ForegroundColor White
    Write-Host " • Frontend (Next.js) on http://localhost:$script:devFrontendPort" -ForegroundColor Gray
    Write-Host " • Backend (Express) on http://localhost:$script:backendPort" -ForegroundColor Gray
    Write-Host " • Public Ngrok tunnel for external access" -ForegroundColor Gray
    Write-Host " • Database initialization" -ForegroundColor Gray
    Write-Host ""
    # Run health check first
    Write-Host "🔍 Running health check..." -ForegroundColor Yellow
    Run-HealthCheck
    # Start development server
    Write-Host "🚀 Starting development servers..." -ForegroundColor Yellow
    $serverStartSuccess = Start-DevelopmentServer

    if ($serverStartSuccess) {
        # Wait a moment for servers to stabilize
        Write-Host "⏳ Servers started successfully, initializing Ngrok..." -ForegroundColor Yellow
        Start-Sleep -Seconds 2
        # Start ngrok tunnel
        Write-Host "🌐 Starting Ngrok tunnel..." -ForegroundColor Yellow
        Start-NgrokTunnel -Environment "development"
        Write-Host ""
        Write-Host "✅ Development Environment + Ngrok Started!" -ForegroundColor Green
        Write-Host " Local Frontend: http://localhost:$script:devFrontendPort" -ForegroundColor Cyan
        Write-Host " Local Backend: http://localhost:$script:backendPort" -ForegroundColor Cyan
        Write-Host " Public Tunnel: Check tunnel status above" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "💡 TIP: Use option [12] to monitor tunnel, [13] to stop tunnel" -ForegroundColor Yellow
    } else {
        Write-Host "❌ Development Environment Failed to Start!" -ForegroundColor Red
        Write-Host "Cannot start Ngrok tunnel without working servers." -ForegroundColor Yellow
        Write-Host "Please check the error messages above and try:" -ForegroundColor Yellow
        Write-Host " • Option [14] - Health Check" -ForegroundColor Cyan
        Write-Host " • Option [17] - Kill All Processes and retry" -ForegroundColor Cyan
        Write-Host ""
    }
}

# Version Control Sync (Git pull/push with backup and PAT handling)
function Git-Sync {
    Write-Log "Git Sync started" "INFO"
    # Check and set GITHUB_PAT
    if (-not $env:GITHUB_PAT) {
        $pat = Read-Host "Enter your GitHub PAT (stored as GITHUB_PAT env var)"
        [Environment]::SetEnvironmentVariable("GITHUB_PAT", $pat, "User")
        $env:GITHUB_PAT = $pat
        Write-Log "GitHub PAT set" "SUCCESS"
    }

    # Automatic backup (stash changes)
    git stash push -m "Auto-backup before sync" | Out-Null
    Write-Log "Changes stashed as backup" "INFO"

    # Pull
    git pull origin main --auth $env:GITHUB_PAT | Out-Null
    Write-Log "Git pull completed" "SUCCESS"

    # Push (prompt for commit message)
    $commitMsg = Read-Host "Enter commit message for push (or empty to skip)"
    if ($commitMsg) {
        git add .
        git commit -m $commitMsg
        git push origin main --auth $env:GITHUB_PAT | Out-Null
        Write-Log "Git push completed" "SUCCESS"
    }
}

# Smart Dependency Management with Caching
$script:configFile = "$script:projectRoot\data\launcher-config.json"
$script:lastDependencyCheck = $null
$script:dependencyCheckInterval = 24 # hours

# Load launcher configuration
function Load-LauncherConfig {
    if (Test-Path $script:configFile) {
        try {
            $config = Get-Content $script:configFile | ConvertFrom-Json
            if ($config.lastDependencyCheck) {
                $script:lastDependencyCheck = [datetime]$config.lastDependencyCheck
            }
            if ($config.dependencyCheckInterval) {
                $script:dependencyCheckInterval = $config.dependencyCheckInterval
            }
        } catch {
            Write-Log "Could not load launcher config: $($_.Exception.Message)" "WARN"
        }
    }
}

# Save launcher configuration
function Save-LauncherConfig {
    try {
        $config = @{
            lastDependencyCheck = $script:lastDependencyCheck
            dependencyCheckInterval = $script:dependencyCheckInterval
            version = "1.0"
        }
        $config | ConvertTo-Json | Set-Content $script:configFile
    } catch {
        Write-Log "Could not save launcher config: $($_.Exception.Message)" "WARN"
    }
}

function Check-AndUpdateDependencies {
    param(
        [switch]$Force,
        [switch]$SkipCache
    )

    # Skip if recently checked (unless forced)
    if (!$Force -and !$SkipCache -and $script:lastDependencyCheck) {
        $hoursSinceCheck = ((Get-Date) - $script:lastDependencyCheck).TotalHours
        if ($hoursSinceCheck -lt $script:dependencyCheckInterval) {
            Write-Log "Dependencies checked $([math]::Round($hoursSinceCheck, 1)) hours ago - skipping (use [U] to force)" "INFO"
            return
        }
    } elseif (!$Force -and !$SkipCache -and !$script:lastDependencyCheck) {
        Write-Log "First run - checking dependencies..." "INFO"
    }

    Write-Log "Checking dependencies..." "INFO"
    if (!(Test-Path "node_modules")) {
        Write-Log "Installing dependencies..." "INFO"
        bun install
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Dependencies installed (bun install)" "SUCCESS"
        } else {
            Write-Log "Failed to install dependencies" "ERROR"
        }
    } else {
        try {
            # Check for outdated dependencies with bun
            $outdatedJson = bun outdated --json 2>$null
            if ($LASTEXITCODE -ne 0 -or [string]::IsNullOrEmpty($outdatedJson)) {
                Write-Log "Dependencies appear to be up to date (or bun outdated failed)" "INFO"
                $script:lastDependencyCheck = Get-Date
                return
            }

            $outdated = $outdatedJson | ConvertFrom-Json -ErrorAction Stop
            if ($outdated -and $outdated.PSObject.Properties.Count -gt 0) {
                Write-Host "Outdated dependencies found:" -ForegroundColor Yellow
                $outdated.PSObject.Properties | ForEach-Object {
                    $dep = $_.Value
                    Write-Host " $($_.Name): $($dep.current) -> $($dep.latest)" -ForegroundColor Cyan
                }

                $update = Read-Host "Update now? (y/n)"
                if ($update.ToLower() -eq 'y') {
                    Write-Log "Updating dependencies to latest versions..." "INFO"

                    # Use bun update for latest version updates
                    Write-Log "Running bun update..." "INFO"
                    bun update

                    # Verify the update worked
                    if ($LASTEXITCODE -eq 0) {
                        Write-Log "Dependencies updated successfully - verifying..." "SUCCESS"

                        # Quick verification
                        $verifyJson = bun outdated --json 2>$null
                        if ([string]::IsNullOrEmpty($verifyJson)) {
                            Write-Log "✅ All dependencies are now up to date" "SUCCESS"
                        } else {
                            $stillOutdated = $verifyJson | ConvertFrom-Json -ErrorAction SilentlyContinue
                            if ($stillOutdated -and $stillOutdated.PSObject.Properties.Count -gt 0) {
                                Write-Log "⚠️ Some dependencies still outdated (may require manual intervention)" "WARN"
                            } else {
                                Write-Log "✅ All dependencies successfully updated" "SUCCESS"
                            }
                        }
                    } else {
                        Write-Log "❌ Dependency update failed" "ERROR"
                    }
                } else {
                    Write-Log "Dependency update skipped by user" "INFO"
                }
            } else {
                Write-Log "Dependencies are up to date" "INFO"
            }
        } catch {
            Write-Log "Error checking dependencies: $($_.Exception.Message)" "WARN"
            Write-Log "Continuing without dependency check..." "INFO"
        }
    }

    $script:lastDependencyCheck = Get-Date
    Save-LauncherConfig
}

# Node.js Compatibility Check and Advisory
function Check-NodeJSCompatibility {
    try {
        $nodeVersion = node --version
        $versionNumber = [version]($nodeVersion -replace 'v', '')

        Write-Host ""
        Write-Host "🔍 NODE.JS COMPATIBILITY CHECK" -ForegroundColor Cyan
        Write-Host "Current Node.js version: $nodeVersion" -ForegroundColor White

        # Check for compatibility issues
        if ($versionNumber.Major -eq 23) {
            Write-Host ""
            Write-Host "⚠️  COMPATIBILITY WARNING" -ForegroundColor Yellow
            Write-Host "Node.js v23.x is bleeding-edge and unsupported by many packages" -ForegroundColor Yellow
            Write-Host "This may cause compatibility warnings with some packages" -ForegroundColor Gray
            Write-Host ""
            Write-Host "🔧 RECOMMENDED SOLUTIONS:" -ForegroundColor Green
            Write-Host "1. Use Node.js LTS (v20.x or v22.x) for maximum compatibility" -ForegroundColor Cyan
            Write-Host "2. Install nvm to manage multiple Node versions:" -ForegroundColor Cyan
            Write-Host "   winget install CoreyButler.NVMforWindows" -ForegroundColor Gray
            Write-Host "3. Or continue with warnings (functionality usually works)" -ForegroundColor Cyan
            Write-Host ""
            $choice = Read-Host "Continue anyway? (y/n) [y]"
            if ($choice.ToLower() -eq 'n') {
                Write-Host "❌ Dependency update cancelled" -ForegroundColor Red
                return $false
            }
        } elseif ($versionNumber.Major -lt 18) {
            Write-Host ""
            Write-Host "❌ NODE.JS TOO OLD" -ForegroundColor Red
            Write-Host "Node.js $nodeVersion is outdated and unsupported" -ForegroundColor Red
            Write-Host "Please upgrade to Node.js 18+ or LTS version" -ForegroundColor Yellow
            return $false
        } else {
            Write-Host "✅ Node.js version is compatible" -ForegroundColor Green
        }

        # Bun has built-in dependency management
        Write-Host ""
        Write-Host "💡 TIP: Bun has built-in update capabilities:" -ForegroundColor Yellow
        Write-Host "Use 'bun update' to update all dependencies" -ForegroundColor Cyan
        Write-Host "✅ Bun dependency management is built-in" -ForegroundColor Green

        return $true

    } catch {
        Write-Log "Error checking Node.js compatibility: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Function to execute launcher options programmatically
function Execute-LauncherOption {
    param([string]$Option)

    switch ($Option.ToUpper()) {
        "D" { Start-FullDevelopmentEnvironment }
        "N" { Start-DevelopmentWithNgrok }
        "1" { Build-Project }
        "2" { Run-Tests -TestType "all" }
        "3" { Run-Tests -TestType "e2e" }
        "4" { Run-Tests -TestType "unit" }
        "5" { Run-LintAndTypeCheck }
        "6" { Start-DevelopmentServer }
        "7" { Start-ProductionServer }
        "8" { Start-BackendOnly }
        "9" { Start-FrontendOnly }
        "R" { Force-RestartDevelopmentServers }
        "10" { Start-NgrokTunnel -Environment "development" }
        "11" { Start-NgrokTunnel -Environment "production" }
        "12" { Monitor-NgrokConnections }
        "13" { Stop-NgrokTunnel }
        "14" { Run-HealthCheck }
        "15" { Run-SystemDiagnostics }
        "16" { Clean-CacheAndLogs }
        "17" { Stop-AllProcesses }
        "X" {
            Write-Host "💥 LAUNCHING NUCLEAR KILL OPTION..." -ForegroundColor Red
            powershell -ExecutionPolicy Bypass -File "$script:projectRoot\nuclear-kill-processes.ps1"
        }
        "T" { Start-OCRTestingPlayground }
        "V" { Git-Sync }
        "U" {
            Write-Host ""
            Write-Host "🔄 FORCE DEPENDENCY UPDATE" -ForegroundColor Green
            Check-NodeJSCompatibility
            Check-AndUpdateDependencies -Force -SkipCache
        }
        "M" {
            $theme = Read-Host "Choose theme: Default, Dark, Light"
            Set-ConsoleTheme $theme
        }
        "G" { Git-Sync }
        default {
            Write-Log "Invalid option: $Option" "ERROR"
            Write-Host "Available options: D, N, 1-17, R, T, V, U, M, G, X, Q" -ForegroundColor Yellow
        }
    }
}

# Main script execution
function Main {
    param([string]$AutoOption)

    Write-Log "DL Organizer Advanced Launcher started" "INFO"

    # Load launcher configuration
    Load-LauncherConfig

    # Check if we're in the correct directory
    if (!(Test-Path "$script:projectRoot\package.json")) {
        Write-Log "Error: Not in DL Organizer project directory!" "ERROR"
        Write-Log "Please run this script from the project root directory." "ERROR"
        Read-Host "Press Enter to exit"
        return
    }

    # Handle automatic option if provided
    if (![string]::IsNullOrEmpty($AutoOption)) {
        Write-Log "Running automatic option: $AutoOption" "INFO"
        Execute-LauncherOption -Option $AutoOption
        return
    }

    # Main menu loop
    do {
        Show-MainMenu
        $choice = Read-Host " Select an option"
        if ([string]::IsNullOrEmpty($choice)) {
            $choice = "Q"
        }

        switch ($choice.ToUpper()) {
            "D" { Start-FullDevelopmentEnvironment }
            "N" { Start-DevelopmentWithNgrok }
            "1" { Build-Project }
            "2" { Run-Tests -TestType "all" }
            "3" { Run-Tests -TestType "e2e" }
            "4" { Run-Tests -TestType "unit" }
            "5" { Run-LintAndTypeCheck }
            "6" { Start-DevelopmentServer }
            "7" { Start-ProductionServer }
            "8" { Start-BackendOnly }
            "9" { Start-FrontendOnly }
            "R" { Force-RestartDevelopmentServers }
            "10" { Start-NgrokTunnel -Environment "development" }
            "11" { Start-NgrokTunnel -Environment "production" }
            "12" { Monitor-NgrokConnections }
            "13" { Stop-NgrokTunnel }
            "14" { Run-HealthCheck }
            "15" { Run-SystemDiagnostics }
            "16" { Clean-CacheAndLogs }
            "17" { Stop-AllProcesses }
            "18" {
                Write-Host ""
                Write-Host "🔍 MANUAL PORT CONFLICT RESOLUTION" -ForegroundColor Cyan
                $resolved = Resolve-PortConflicts
                if ($resolved) {
                    Write-Host "✅ Port conflicts resolved successfully!" -ForegroundColor Green
                } else {
                    Write-Host "❌ Port conflicts could not be resolved" -ForegroundColor Red
                }
                Write-Host ""
                Read-Host "Press Enter to continue"
            }
            "X" {
                Write-Host "💥 LAUNCHING NUCLEAR KILL OPTION..." -ForegroundColor Red
                powershell -ExecutionPolicy Bypass -File "$script:projectRoot\nuclear-kill-processes.ps1"
            }
            "T" { Start-OCRTestingPlayground }
            "V" { Git-Sync }  # Version Control Sync
            "U" {
                Write-Host ""
                Write-Host "🔄 FORCE DEPENDENCY UPDATE" -ForegroundColor Green
                Check-NodeJSCompatibility
                Check-AndUpdateDependencies -Force -SkipCache
            }  # Force dependency update
            "M" {
                $theme = Read-Host "Choose theme: Default, Dark, Light"
                Set-ConsoleTheme $theme
            }  # Manage Themes
            "G" { Git-Sync }  # Gesture shortcut for Git
            "Q" { break }
            default { Write-Log "Invalid selection. Please try again." "WARN" }
        }
        if (![string]::IsNullOrEmpty($choice) -and $choice.ToUpper() -ne "Q" -and $choice.ToUpper() -ne "T") {
            Write-Host ""
            Read-Host "Press Enter to continue"
        }
    } while (![string]::IsNullOrEmpty($choice) -and $choice.ToUpper() -ne "Q")
    Write-Log "DL Organizer Advanced Launcher exiting" "INFO"
}

# Cleanup function
function Cleanup {
    Write-Log "Cleaning up processes..." "INFO"
    # Stop our specific background jobs
    if ($script:frontendProcess) {
        Stop-Job -Job $script:frontendProcess -ErrorAction SilentlyContinue
        Remove-Job -Job $script:frontendProcess -Force -ErrorAction SilentlyContinue
        $script:frontendProcess = $null
    }
    if ($script:backendProcess) {
        Stop-Job -Job $script:backendProcess -ErrorAction SilentlyContinue
        Remove-Job -Job $script:backendProcess -Force -ErrorAction SilentlyContinue
        $script:backendProcess = $null
    }
    # Stop any other background jobs
    Get-Job | Stop-Job -ErrorAction SilentlyContinue
    Get-Job | Remove-Job -Force -ErrorAction SilentlyContinue
    Write-Log "Cleanup completed" "SUCCESS"
}

# Register cleanup on exit
Register-EngineEvent -SourceIdentifier PowerShell.Exiting -Action { Cleanup }

# Start the main function with optional argument
if (![string]::IsNullOrEmpty($Option)) {
    Main -AutoOption $Option
} else {
    Main
}```