import { test, expect } from '@playwright/test';

// Test the folder picker fixes for drive expansion and project scanning
test.describe('Folder Picker Validation Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app
    await page.goto('http://localhost:3001');
    // Wait for the page to load completely
    await page.waitForLoadState('networkidle');
  });

  test('should fix drives endpoint path format', async ({ page }) => {
    // Test the backend drives endpoint directly
    const response = await page.request.get('http://localhost:3003/api/filesystem/drives');
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.drives).toBeDefined();
    expect(data.drives.length).toBeGreaterThan(0);
    
    // Should have C: drive with correct path format
    const cDrive = data.drives.find((drive: any) => drive.path === 'C:\\');
    expect(cDrive).toBeDefined();
    expect(cDrive.name).toContain('C:');
    expect(cDrive.type).toBe('drive');
  });

  test('should fix folders endpoint performance', async ({ page }) => {
    // Test the backend folders endpoint directly with performance monitoring
    const startTime = Date.now();
    
    const response = await page.request.post('http://localhost:3003/api/filesystem/folders', {
      data: {
        path: 'C:/',
        includeStats: false, // Performance optimization for drive roots
        maxDepth: 1
      }
    });
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.folders).toBeDefined();
    expect(data.folders.length).toBeGreaterThan(0);
    
    // Should respond quickly (under 5 seconds, much better than previous timeout)
    expect(responseTime).toBeLessThan(5000);
    
    // Should have common folders like Users, Program Files
    const folderNames = data.folders.map((folder: any) => folder.name);
    expect(folderNames).toContain('Users');
  });

  test('should load folder picker dialog without errors', async ({ page }) => {
    // Click "New Project" button
    await page.getByRole('button', { name: /new project/i }).click();

    // Wait for the project creation form to appear
    await expect(page.getByText('Create New Project')).toBeVisible();

    // Find the folder picker browse button
    const browseButton = page.getByRole('button', { name: /browse/i });
    await expect(browseButton).toBeVisible();

    // Click the browse button to open the folder picker
    await browseButton.click();

    // Wait for the folder picker dialog to open
    await expect(page.getByRole('dialog')).toBeVisible();
    await expect(page.getByRole('heading', { name: /select folder/i })).toBeVisible();

    // Check if drives are loading (should not show "failed to load drives")
    await page.waitForTimeout(3000); // Wait for drives to load
    
    // Look for drive entries (should see C: Drive, etc.)
    await expect(page.getByText(/C:.*Drive/i)).toBeVisible();
    
    // Should not see any error messages
    await expect(page.getByText(/failed to load drives/i)).not.toBeVisible();
    await expect(page.getByText(/failed to scan project folders/i)).not.toBeVisible();
  });

  test('should expand drives without spinning forever', async ({ page }) => {
    // Click "New Project" button
    await page.getByRole('button', { name: /new project/i }).click();

    // Click the browse button to open the folder picker
    await page.getByRole('button', { name: /browse/i }).click();

    // Wait for the folder picker dialog to open
    await expect(page.getByRole('dialog')).toBeVisible();

    // Wait for drives to load
    await page.waitForTimeout(3000);

    // Find the C: Drive expand button (chevron)
    const cDriveRow = page.locator('div:has-text("C: Drive")').first();
    await expect(cDriveRow).toBeVisible();

    // Look for the chevron expand button within the drive row
    const expandButton = cDriveRow.locator('svg').first();
    await expect(expandButton).toBeVisible();

    // Click to expand the drive
    await expandButton.click();

    // Should not show spinning icon forever - wait max 15 seconds (timeout from code)
    // The fix should make this much faster
    await page.waitForTimeout(5000);

    // Check that expansion worked by looking for common folders
    // Note: We're not being too strict about specific folder names since they vary by system
    const folderElements = page.locator('div').filter({ hasText: /Users|Program Files|Windows|claude/i });
    await expect(folderElements.first()).toBeVisible({ timeout: 10000 });

    // Should not show spinning icon anymore
    await expect(page.locator('.animate-spin')).not.toBeVisible();
  });

  test('should test project creation workflow without "Failed to scan project folders"', async ({ page }) => {
    // Test the specific error that was reported
    // Click "New Project" button
    await page.getByRole('button', { name: /new project/i }).click();

    // Wait for the project creation form
    await expect(page.getByText('Create New Project')).toBeVisible();

    // Type a project name
    const projectNameInput = page.locator('input[placeholder*="project name"], input[placeholder*="Project Name"]');
    await projectNameInput.fill('Test Project');

    // Type a folder path directly (simulating what might cause the error)
    const folderInput = page.locator('input[placeholder*="folder"], input[placeholder*="directory"]');
    await folderInput.fill('C:\\claude\\dl-organizer\\test-project');

    // Wait a moment for any potential validation
    await page.waitForTimeout(1000);

    // Should not see the "Failed to scan project folders" error
    await expect(page.getByText(/failed to scan project folders/i)).not.toBeVisible();

    // Should not see any error messages related to path validation
    await expect(page.getByText(/invalid path/i)).not.toBeVisible();
    await expect(page.getByText(/path validation failed/i)).not.toBeVisible();
  });

  test('should verify no console errors during folder operations', async ({ page }) => {
    const consoleErrors: string[] = [];
    
    // Listen for console errors
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    // Click "New Project" button
    await page.getByRole('button', { name: /new project/i }).click();

    // Open folder picker
    await page.getByRole('button', { name: /browse/i }).click();
    await expect(page.getByRole('dialog')).toBeVisible();

    // Wait for drives to load
    await page.waitForTimeout(3000);

    // Try to expand a drive
    const cDriveRow = page.locator('div:has-text("C: Drive")').first();
    if (await cDriveRow.isVisible()) {
      const expandButton = cDriveRow.locator('svg').first();
      await expandButton.click();
      await page.waitForTimeout(3000);
    }

    // Check for console errors
    expect(consoleErrors).toEqual([]);
  });
});