@echo off

setlocal enabledelayedexpansion

REM =============================================================================
REM DL Organizer - Simplified Launcher
REM =============================================================================
REM Launches the PowerShell menu for core development and production tasks.
REM =============================================================================

title DL Organizer - Launcher

REM Check if PowerShell is available
powershell -Command "exit 0" >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo [ERROR] PowerShell is required but not available!
    echo Please install PowerShell and try again.
    echo.
    pause
    exit /b 1
)

REM Check if Node.js is available
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo [ERROR] Node.js is required but not installed!
    echo Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

REM Set console colors
color 0A
echo.
echo ===============================================================================
echo DL Organizer - Simplified Development Environment
echo ===============================================================================
echo Professional Driver's License OCR Processing System
echo ===============================================================================
echo.

REM Launch the simplified PowerShell launcher
echo Starting Simplified PowerShell Launcher...
echo.

powershell -NoProfile -ExecutionPolicy Bypass -Command "& '%~dp0launcher.ps1'"

REM Check if PowerShell script executed successfully
if %errorlevel% neq 0 (
    echo.
    echo [ERROR] PowerShell launcher failed to execute!
    echo Please check the error messages above.
    echo.
    pause
    exit /b 1
)

echo.
echo Launcher completed successfully.
pause
