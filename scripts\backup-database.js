const fs = require('fs').promises;
const path = require('path');
const DatabaseManager = require('../backend/config/database');

class DatabaseBackup {
  constructor() {
    this.backupDir = path.join(__dirname, '..', 'data', 'backups');
    this.maxBackups = 30; // Keep 30 days of backups
  }
  
  async createBackup() {
    try {
      // Ensure backup directory exists
      await fs.mkdir(this.backupDir, { recursive: true });
      
      // Create timestamped backup filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = path.join(this.backupDir, `dl-organizer-${timestamp}.db`);
      
      // Initialize database and create backup
      const dbManager = new DatabaseManager();
      await dbManager.initialize();
      
      console.log('Creating database backup...');
      await dbManager.backup(backupPath);
      
      // Get backup file size
      const stats = await fs.stat(backupPath);
      const sizeMB = (stats.size / (1024 * 1024)).toFixed(2);
      
      console.log(`✅ Backup created: ${path.basename(backupPath)} (${sizeMB} MB)`);
      
      await dbManager.close();
      
      // Cleanup old backups
      await this.cleanupOldBackups();
      
      return backupPath;
    } catch (error) {
      console.error('❌ Backup failed:', error.message);
      throw error;
    }
  }
  
  async cleanupOldBackups() {
    try {
      const files = await fs.readdir(this.backupDir);
      const backupFiles = files
        .filter(file => file.startsWith('dl-organizer-') && file.endsWith('.db'))
        .map(file => ({
          name: file,
          path: path.join(this.backupDir, file)
        }));
      
      if (backupFiles.length <= this.maxBackups) {
        return;
      }
      
      // Sort by creation time (oldest first)
      const filesWithStats = await Promise.all(
        backupFiles.map(async (file) => {
          const stats = await fs.stat(file.path);
          return { ...file, mtime: stats.mtime };
        })
      );
      
      filesWithStats.sort((a, b) => a.mtime - b.mtime);
      
      // Remove oldest files
      const filesToRemove = filesWithStats.slice(0, filesWithStats.length - this.maxBackups);
      
      for (const file of filesToRemove) {
        await fs.unlink(file.path);
        console.log(`🗑️  Removed old backup: ${file.name}`);
      }
      
      console.log(`✅ Cleanup complete. Keeping ${this.maxBackups} most recent backups.`);
    } catch (error) {
      console.warn('⚠️  Backup cleanup failed:', error.message);
    }
  }
  
  async listBackups() {
    try {
      const files = await fs.readdir(this.backupDir);
      const backupFiles = files
        .filter(file => file.startsWith('dl-organizer-') && file.endsWith('.db'))
        .sort()
        .reverse();
      
      console.log('\n📋 Available backups:');
      
      for (const file of backupFiles) {
        const filePath = path.join(this.backupDir, file);
        const stats = await fs.stat(filePath);
        const sizeMB = (stats.size / (1024 * 1024)).toFixed(2);
        const date = stats.mtime.toLocaleString();
        
        console.log(`  ${file} - ${sizeMB} MB - ${date}`);
      }
      
      return backupFiles;
    } catch (error) {
      console.error('❌ Failed to list backups:', error.message);
      return [];
    }
  }
  
  async restoreBackup(backupFilename) {
    try {
      const backupPath = path.join(this.backupDir, backupFilename);
      const dbPath = path.join(__dirname, '..', 'data', 'dl-organizer.db');
      
      // Verify backup file exists
      await fs.access(backupPath);
      
      // Create backup of current database before restore
      const currentBackupPath = path.join(this.backupDir, `pre-restore-${Date.now()}.db`);
      try {
        await fs.copyFile(dbPath, currentBackupPath);
        console.log(`✅ Current database backed up to: ${path.basename(currentBackupPath)}`);
      } catch (error) {
        console.log('ℹ️  No existing database to backup');
      }
      
      // Restore from backup
      await fs.copyFile(backupPath, dbPath);
      
      console.log(`✅ Database restored from: ${backupFilename}`);
      
      // Verify restored database
      const dbManager = new DatabaseManager();
      await dbManager.initialize();
      const health = await dbManager.healthCheck();
      await dbManager.close();
      
      if (health.status === 'healthy') {
        console.log('✅ Restored database is healthy');
      } else {
        throw new Error('Restored database failed health check');
      }
      
    } catch (error) {
      console.error('❌ Restore failed:', error.message);
      throw error;
    }
  }
}

// CLI interface
async function main() {
  const backup = new DatabaseBackup();
  const command = process.argv[2];
  
  switch (command) {
    case 'create':
      await backup.createBackup();
      break;
      
    case 'list':
      await backup.listBackups();
      break;
      
    case 'restore':
      const filename = process.argv[3];
      if (!filename) {
        console.error('❌ Please specify backup filename');
        console.log('Usage: node backup-database.js restore <filename>');
        process.exit(1);
      }
      await backup.restoreBackup(filename);
      break;
      
    default:
      console.log('DL Organizer Database Backup Tool');
      console.log('');
      console.log('Usage:');
      console.log('  node backup-database.js create   - Create new backup');
      console.log('  node backup-database.js list     - List available backups');
      console.log('  node backup-database.js restore <filename> - Restore from backup');
      break;
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = DatabaseBackup;