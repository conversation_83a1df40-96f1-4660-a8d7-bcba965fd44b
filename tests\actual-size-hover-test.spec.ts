import { test, expect } from '@playwright/test';

// Test actual size hover preview functionality
test.describe('Actual Size Hover Preview Test', () => {
  test('should show actual image size in OCR Testing Playground', async ({ page }) => {
    await page.goto('http://localhost:3001/ocr-testing');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Find the first image thumbnail
    const firstThumbnail = page.locator('img[width="48"]').first();
    await expect(firstThumbnail).toBeVisible();
    
    console.log('✅ Found thumbnail image (48x48)');
    
    // Get the parent group container
    const groupContainer = firstThumbnail.locator('..').locator('..');
    
    // Hover over the group container
    await groupContainer.hover();
    console.log('✅ Hovered over group container');
    
    // Wait for hover effect
    await page.waitForTimeout(1000);
    
    // Find the hover preview image - it should now be using actual size
    const hoverPreview = page.locator('div.absolute img[sizes="100vw"]').first();
    await expect(hoverPreview).toBeVisible();
    
    console.log('✅ Found hover preview image with actual size');
    
    // Check the image's natural dimensions
    const naturalWidth = await hoverPreview.evaluate((img: HTMLImageElement) => img.naturalWidth);
    const naturalHeight = await hoverPreview.evaluate((img: HTMLImageElement) => img.naturalHeight);
    
    console.log(`Preview image natural dimensions: ${naturalWidth}x${naturalHeight}`);
    
    // Check that the image is displaying at its natural size (or constrained by max-height)
    const displayWidth = await hoverPreview.evaluate((img: HTMLImageElement) => img.offsetWidth);
    const displayHeight = await hoverPreview.evaluate((img: HTMLImageElement) => img.offsetHeight);
    
    console.log(`Preview image display dimensions: ${displayWidth}x${displayHeight}`);
    
    // Verify the image is much larger than the previous 96x96 preview
    expect(displayWidth).toBeGreaterThan(96);
    expect(displayHeight).toBeGreaterThan(96);
    
    // Verify the image loaded successfully
    if (naturalWidth > 0 && naturalHeight > 0) {
      console.log('✅ Preview image loaded successfully at actual size');
    } else {
      console.log('❌ Preview image failed to load');
    }
    
    // Check the container styling
    const previewContainer = hoverPreview.locator('..').locator('..');
    const containerClasses = await previewContainer.getAttribute('class');
    console.log('Preview container classes:', containerClasses);
    
    // Should have responsive sizing classes
    expect(containerClasses).toContain('max-w-screen-md');
    expect(containerClasses).toContain('max-h-screen-md');
    expect(containerClasses).toContain('overflow-hidden');
    
    // Take screenshot with actual size hover preview
    await page.screenshot({ path: 'actual-size-ocr-testing-hover.png' });
    
    // Move mouse away to test hover out
    await page.mouse.move(0, 0);
    await page.waitForTimeout(1000);
    
    // Verify hover preview is hidden
    await expect(hoverPreview).toBeHidden();
    console.log('✅ Hover preview correctly hidden after hover out');
  });
  
  test('should show actual image size in main app image grid', async ({ page }) => {
    // Navigate to main app
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
    
    // Check if there's an existing project or create one
    const projectOverviewHeading = page.locator('text=DL Organizer Projects');
    if (await projectOverviewHeading.isVisible()) {
      console.log('On project overview page, attempting to create project...');
      
      // Try to create a project
      const createProjectButton = page.getByRole('button', { name: /create.*project/i });
      if (await createProjectButton.isVisible() && !await createProjectButton.isDisabled()) {
        console.log('Creating new project...');
        await createProjectButton.click();
        
        // Wait for project creation
        await page.waitForTimeout(2000);
        
        // Check if we're now in a project
        const ocrTestingLink = page.locator('text=OCR Testing');
        if (await ocrTestingLink.isVisible()) {
          console.log('✅ Project created, now in project view');
        }
      } else {
        console.log('❌ Cannot create project, skipping main app test');
        return;
      }
    }
    
    // Try to find folder tree and select a folder
    const folderTree = page.locator('[class*="folder"], [class*="tree"]');
    if (await folderTree.count() > 0) {
      console.log('Found folder tree, selecting first folder...');
      await folderTree.first().click();
      await page.waitForTimeout(2000);
    }
    
    // Look for image grid cards
    const imageGridCards = page.locator('[class*="card"]').filter({ hasText: /\.jpg|\.jpeg|\.png/ });
    const cardCount = await imageGridCards.count();
    
    console.log(`Found ${cardCount} image cards in main app`);
    
    if (cardCount > 0) {
      const firstCard = imageGridCards.first();
      
      // Hover over the card to trigger preview (2 second delay)
      await firstCard.hover();
      console.log('✅ Hovered over image card, waiting for 2 second delay...');
      
      // Wait for hover preview (has 2 second delay)
      await page.waitForTimeout(3000);
      
      // Look for hover preview with actual size
      const hoverPreview = page.locator('div[class*="fixed"][class*="bg-background"] img[sizes="100vw"]');
      if (await hoverPreview.isVisible()) {
        console.log('✅ Found hover preview popup with actual size');
        
        // Check image dimensions
        const naturalWidth = await hoverPreview.evaluate((img: HTMLImageElement) => img.naturalWidth);
        const naturalHeight = await hoverPreview.evaluate((img: HTMLImageElement) => img.naturalHeight);
        
        console.log(`Main app preview natural dimensions: ${naturalWidth}x${naturalHeight}`);
        
        const displayWidth = await hoverPreview.evaluate((img: HTMLImageElement) => img.offsetWidth);
        const displayHeight = await hoverPreview.evaluate((img: HTMLImageElement) => img.offsetHeight);
        
        console.log(`Main app preview display dimensions: ${displayWidth}x${displayHeight}`);
        
        // Verify the image is showing at actual size (or constrained by viewport)
        if (naturalWidth > 0 && naturalHeight > 0) {
          console.log('✅ Main app hover preview loaded successfully at actual size');
          
          // Check if the display size respects viewport constraints
          const viewportWidth = await page.evaluate(() => window.innerWidth);
          const viewportHeight = await page.evaluate(() => window.innerHeight);
          
          console.log(`Viewport dimensions: ${viewportWidth}x${viewportHeight}`);
          
          // Display size should be constrained by viewport (max 80% of viewport)
          const maxDisplayWidth = viewportWidth * 0.8;
          const maxDisplayHeight = viewportHeight * 0.8;
          
          expect(displayWidth).toBeLessThanOrEqual(maxDisplayWidth + 10); // +10 for padding
          expect(displayHeight).toBeLessThanOrEqual(maxDisplayHeight + 10); // +10 for padding
          
          console.log('✅ Main app hover preview respects viewport constraints');
        } else {
          console.log('❌ Main app preview image failed to load');
        }
        
        // Take screenshot
        await page.screenshot({ path: 'actual-size-main-app-hover.png' });
      } else {
        console.log('❌ No hover preview found in main app');
      }
    } else {
      console.log('❌ No image cards found in main app');
    }
  });
  
  test('should verify actual size image loading performance', async ({ page }) => {
    await page.goto('http://localhost:3001/ocr-testing');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Test multiple hover previews to check performance
    const thumbnails = page.locator('img[width="48"]');
    const thumbnailCount = await thumbnails.count();
    
    console.log(`Testing performance with ${thumbnailCount} actual size hover previews`);
    
    // Test first 5 thumbnails for performance
    for (let i = 0; i < Math.min(thumbnailCount, 5); i++) {
      console.log(`Testing hover performance ${i + 1}/${Math.min(thumbnailCount, 5)}...`);
      
      const thumbnail = thumbnails.nth(i);
      const groupContainer = thumbnail.locator('..').locator('..');
      
      // Measure hover response time
      const startTime = Date.now();
      
      await groupContainer.hover();
      
      // Wait for the preview to appear
      const hoverPreview = page.locator('div.absolute img[sizes="100vw"]').nth(i);
      await expect(hoverPreview).toBeVisible();
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      console.log(`Hover preview ${i} response time: ${responseTime}ms`);
      
      // Check if response time is reasonable (less than 2 seconds)
      expect(responseTime).toBeLessThan(2000);
      
      // Check image dimensions
      const naturalWidth = await hoverPreview.evaluate((img: HTMLImageElement) => img.naturalWidth);
      const naturalHeight = await hoverPreview.evaluate((img: HTMLImageElement) => img.naturalHeight);
      
      console.log(`Preview ${i} actual size: ${naturalWidth}x${naturalHeight}`);
      
      // Move mouse away
      await page.mouse.move(0, 0);
      await page.waitForTimeout(500);
    }
    
    console.log('✅ All hover previews loaded actual size images within reasonable time');
  });
});