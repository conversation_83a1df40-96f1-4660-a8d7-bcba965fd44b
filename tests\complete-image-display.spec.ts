import { test, expect } from '@playwright/test';

// Helper functions for testing
async function testImageDisplay(page: any) {
  console.log('Testing image display...');
  
  // Look for the test images section
  const selectImagesSection = page.locator('text=Select Test Images');
  if (await selectImagesSection.isVisible()) {
    console.log('Found Select Test Images section');
    
    // Wait for images to load
    await page.waitForTimeout(3000);
    
    // Check for image elements
    const images = page.locator('img');
    const imageCount = await images.count();
    console.log(`Found ${imageCount} images on the page`);
    
    // Check if images are actually loading
    for (let i = 0; i < Math.min(imageCount, 5); i++) {
      const image = images.nth(i);
      await expect(image).toBeVisible();
      
      const src = await image.getAttribute('src');
      console.log(`Image ${i} src: ${src}`);
      
      // Check if the image has loaded properly
      try {
        const naturalWidth = await image.evaluate((img: HTMLImageElement) => img.naturalWidth);
        const naturalHeight = await image.evaluate((img: HTMLImageElement) => img.naturalHeight);
        console.log(`Image ${i}: ${naturalWidth}x${naturalHeight}`);
        
        if (naturalWidth > 0 && naturalHeight > 0) {
          console.log(`✓ Image ${i} loaded successfully`);
        } else {
          console.log(`✗ Image ${i} failed to load (${naturalWidth}x${naturalHeight})`);
        }
      } catch (error) {
        console.log(`✗ Image ${i} evaluation failed:`, (error as Error).message);
      }
    }
  } else {
    console.log('Select Test Images section not found');
  }
}

async function testHoverPreview(page: any) {
  console.log('Testing hover preview...');
  
  const testImagesArea = page.locator('text=Select Test Images').locator('..').locator('..');
  const imageContainers = testImagesArea.locator('label');
  
  if (await imageContainers.count() > 0) {
    const firstImageContainer = imageContainers.first();
    
    // Hover over the first image
    await firstImageContainer.hover();
    await page.waitForTimeout(1000);
    
    // Check for hover preview
    const previewElements = page.locator('div.absolute, .hover-preview, [class*="preview"]');
    const previewCount = await previewElements.count();
    console.log(`Found ${previewCount} potential preview elements`);
    
    if (previewCount > 0) {
      console.log('✓ Hover preview functionality appears to be working');
    } else {
      console.log('✗ No hover preview elements found');
    }
  }
}

async function testImageUrls(page: any) {
  console.log('Testing image URLs...');
  
  const images = page.locator('img');
  const imageCount = await images.count();
  
  for (let i = 0; i < Math.min(imageCount, 5); i++) {
    const image = images.nth(i);
    const src = await image.getAttribute('src');
    
    if (src) {
      console.log(`Testing URL: ${src}`);
      
      // Test the URL directly
      try {
        const response = await page.request.get(src);
        console.log(`URL ${src}: ${response.status()}`);
        
        if (response.status() === 200) {
          console.log(`✓ Image URL ${i} is accessible`);
        } else {
          console.log(`✗ Image URL ${i} returned ${response.status()}`);
        }
      } catch (error) {
        console.log(`✗ Image URL ${i} request failed:`, (error as Error).message);
      }
    }
  }
}

async function testMainAppImageGrid(page: any) {
  console.log('Testing main app image grid...');
  
  // Look for folder tree or folder selection
  const folderTree = page.locator('[class*="folder"], [class*="tree"]');
  const folderCount = await folderTree.count();
  console.log(`Found ${folderCount} potential folder elements`);
  
  if (folderCount > 0) {
    // Try to click on a folder
    const firstFolder = folderTree.first();
    await firstFolder.click();
    await page.waitForTimeout(2000);
    
    // Check for image grid
    const imageGrid = page.locator('[class*="grid"], [class*="image"]');
    const gridCount = await imageGrid.count();
    console.log(`Found ${gridCount} potential image grid elements`);
    
    // Check for images in the main app
    const mainAppImages = page.locator('img');
    const mainImageCount = await mainAppImages.count();
    console.log(`Found ${mainImageCount} images in main app`);
    
    for (let i = 0; i < Math.min(mainImageCount, 3); i++) {
      const image = mainAppImages.nth(i);
      const src = await image.getAttribute('src');
      console.log(`Main app image ${i}: ${src}`);
    }
  }
}

// Complete image display test with proper project setup
test.describe('Complete Image Display Test', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
  });

  test('should create project and test image display functionality', async ({ page }) => {
    // First, check if we're on the project overview page
    const projectOverviewHeading = page.locator('text=DL Organizer Projects');
    await expect(projectOverviewHeading).toBeVisible();

    // Look for "Create First Project" button
    const createProjectButton = page.getByRole('button', { name: /create.*project/i });
    
    if (await createProjectButton.isVisible()) {
      console.log('Creating new project...');
      await createProjectButton.click();
      
      // Wait for project creation dialog/form
      await page.waitForTimeout(1000);
      
      // Fill in project details (assuming there's a form)
      const projectNameInput = page.locator('input[name="name"], input[placeholder*="name"]').first();
      if (await projectNameInput.isVisible()) {
        await projectNameInput.fill('Test Project');
      }
      
      const projectPathInput = page.locator('input[name="path"], input[name="rootPath"], input[placeholder*="path"]').first();
      if (await projectPathInput.isVisible()) {
        await projectPathInput.fill('C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\100x us dl w selfie\\100x us dl w selfie\\combined_to_do\\100\\i100');
      }
      
      // Submit the form
      const submitButton = page.getByRole('button', { name: /create|save|submit/i });
      if (await submitButton.isVisible()) {
        await submitButton.click();
        await page.waitForTimeout(2000);
      }
    }

    // Now we should be in a project context - check for OCR Testing link
    const ocrTestingLink = page.locator('text=OCR Testing');
    console.log('OCR Testing link visible after project creation:', await ocrTestingLink.isVisible());
    
    if (await ocrTestingLink.isVisible()) {
      console.log('Clicking OCR Testing link...');
      await ocrTestingLink.click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);

      // Verify we're on the OCR Testing page
      const ocrTestingHeading = page.locator('h1').filter({ hasText: /OCR.*Testing/i });
      await expect(ocrTestingHeading).toBeVisible();

      // Test image display functionality
      await testImageDisplay(page);
      await testHoverPreview(page);
      await testImageUrls(page);
    } else {
      console.log('OCR Testing link not found, testing main app image grid...');
      await testMainAppImageGrid(page);
    }
  });

  test('should test backend thumbnail generation', async ({ page }) => {
    console.log('Testing backend thumbnail generation...');
    
    // Test known image paths
    const testPaths = [
      'C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\100x us dl w selfie\\100x us dl w selfie\\combined_to_do\\100\\i100\\US DL 1535.jpeg',
      'C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\100x us dl w selfie\\100x us dl w selfie\\combined_to_do\\100\\i100\\@huaquan (81).jpg'
    ];

    for (const testPath of testPaths) {
      console.log(`Testing path: ${testPath}`);
      
      // Test the backend image scanning endpoint
      try {
        const response = await page.request.post('http://localhost:3003/api/filesystem/images', {
          data: {
            folderPath: 'C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\100x us dl w selfie\\100x us dl w selfie\\combined_to_do\\100\\i100',
            generateThumbnails: true
          }
        });
        
        console.log(`Image scan response: ${response.status()}`);
        
        if (response.ok()) {
          const data = await response.json();
          console.log(`Found ${data.images?.length || 0} images`);
          
          if (data.images && data.images.length > 0) {
            const firstImage = data.images[0];
            console.log(`First image:`, firstImage);
            
            // Test thumbnail URL
            if (firstImage.thumbnailPath) {
              const thumbnailUrl = `http://localhost:3003${firstImage.thumbnailPath}`;
              const thumbnailResponse = await page.request.get(thumbnailUrl);
              console.log(`Thumbnail ${thumbnailUrl}: ${thumbnailResponse.status()}`);
            }
          }
        }
      } catch (error) {
        console.log(`Backend test failed:`, (error as Error).message);
      }
    }
  });
});