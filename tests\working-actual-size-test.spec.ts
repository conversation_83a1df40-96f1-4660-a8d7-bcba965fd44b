import { test, expect } from '@playwright/test';

// Working test for actual size hover preview 
test.describe('Working Actual Size Hover Test', () => {
  test('should show actual image size in OCR Testing Playground', async ({ page }) => {
    await page.goto('http://localhost:3001/ocr-testing');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Find the first image thumbnail
    const firstThumbnail = page.locator('img[width="48"]').first();
    await expect(firstThumbnail).toBeVisible();
    
    console.log('✅ Found thumbnail image (48x48)');
    
    // Get the parent group container
    const groupContainer = firstThumbnail.locator('..').locator('..');
    
    // Hover over the group container
    await groupContainer.hover();
    console.log('✅ Hovered over group container');
    
    // Wait for hover effect
    await page.waitForTimeout(1000);
    
    // Find the hover preview image using correct selector
    const hoverPreview = page.locator('div.absolute img[width="0"]').first();
    await expect(hoverPreview).toBeVisible();
    
    console.log('✅ Found hover preview image with actual size settings');
    
    // Check the image's natural dimensions
    const naturalWidth = await hoverPreview.evaluate((img: HTMLImageElement) => img.naturalWidth);
    const naturalHeight = await hoverPreview.evaluate((img: HTMLImageElement) => img.naturalHeight);
    
    console.log(`Preview image natural dimensions: ${naturalWidth}x${naturalHeight}`);
    
    // Check that the image is displaying at its constrained size
    const displayWidth = await hoverPreview.evaluate((img: HTMLImageElement) => img.offsetWidth);
    const displayHeight = await hoverPreview.evaluate((img: HTMLImageElement) => img.offsetHeight);
    
    console.log(`Preview image display dimensions: ${displayWidth}x${displayHeight}`);
    
    // Verify the image is much larger than the thumbnail (48x48)
    expect(displayWidth).toBeGreaterThan(48);
    expect(displayHeight).toBeGreaterThan(48);
    
    // Verify the image loaded successfully
    if (naturalWidth > 0 && naturalHeight > 0) {
      console.log('✅ Preview image loaded successfully at actual size');
      
      // Check if the image is showing at actual size or constrained
      const isActualSize = (displayWidth === naturalWidth && displayHeight === naturalHeight);
      const isConstrainedByViewport = (displayWidth < naturalWidth || displayHeight < naturalHeight);
      
      if (isActualSize) {
        console.log('✅ Image is displaying at actual size');
      } else if (isConstrainedByViewport) {
        console.log('✅ Image is displaying at constrained size (respecting viewport)');
      }
      
      // Check CSS classes for actual size rendering
      const imageClasses = await hoverPreview.getAttribute('class');
      console.log('Image classes:', imageClasses);
      
      expect(imageClasses).toContain('object-contain');
      expect(imageClasses).toContain('w-auto');
      expect(imageClasses).toContain('h-auto');
      expect(imageClasses).toContain('max-w-full');
      expect(imageClasses).toContain('max-h-[80vh]');
      
      console.log('✅ Image has correct classes for actual size rendering');
    } else {
      console.log('❌ Preview image failed to load');
    }
    
    // Check the container styling
    const previewContainer = hoverPreview.locator('..').locator('..');
    const containerClasses = await previewContainer.getAttribute('class');
    console.log('Preview container classes:', containerClasses);
    
    // Should have responsive sizing classes
    expect(containerClasses).toContain('max-w-screen-md');
    expect(containerClasses).toContain('max-h-screen-md');
    expect(containerClasses).toContain('overflow-hidden');
    
    // Take screenshot with actual size hover preview
    await page.screenshot({ path: 'working-actual-size-hover.png' });
    
    // Move mouse away to test hover out
    await page.mouse.move(0, 0);
    await page.waitForTimeout(1000);
    
    // Verify hover preview is hidden
    await expect(hoverPreview).toBeHidden();
    console.log('✅ Hover preview correctly hidden after hover out');
  });
  
  test('should verify image size comparison', async ({ page }) => {
    await page.goto('http://localhost:3001/ocr-testing');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Get thumbnail image dimensions
    const thumbnail = page.locator('img[width="48"]').first();
    const thumbnailWidth = await thumbnail.evaluate((img: HTMLImageElement) => img.offsetWidth);
    const thumbnailHeight = await thumbnail.evaluate((img: HTMLImageElement) => img.offsetHeight);
    
    console.log(`Thumbnail display size: ${thumbnailWidth}x${thumbnailHeight}`);
    
    // Hover to show preview
    const groupContainer = thumbnail.locator('..').locator('..');
    await groupContainer.hover();
    await page.waitForTimeout(1000);
    
    // Get hover preview dimensions
    const hoverPreview = page.locator('div.absolute img[width="0"]').first();
    const previewWidth = await hoverPreview.evaluate((img: HTMLImageElement) => img.offsetWidth);
    const previewHeight = await hoverPreview.evaluate((img: HTMLImageElement) => img.offsetHeight);
    
    console.log(`Preview display size: ${previewWidth}x${previewHeight}`);
    
    // Calculate size difference
    const widthRatio = previewWidth / thumbnailWidth;
    const heightRatio = previewHeight / thumbnailHeight;
    
    console.log(`Size ratio - Width: ${widthRatio.toFixed(2)}x, Height: ${heightRatio.toFixed(2)}x`);
    
    // Verify the preview is significantly larger than thumbnail
    expect(widthRatio).toBeGreaterThan(2); // Should be much more than 200%
    expect(heightRatio).toBeGreaterThan(2); // Should be much more than 200%
    
    console.log('✅ Hover preview is significantly larger than thumbnail');
    
    // Check natural size
    const naturalWidth = await hoverPreview.evaluate((img: HTMLImageElement) => img.naturalWidth);
    const naturalHeight = await hoverPreview.evaluate((img: HTMLImageElement) => img.naturalHeight);
    
    console.log(`Natural image size: ${naturalWidth}x${naturalHeight}`);
    
    // The preview should be constrained by CSS but showing actual image data
    if (naturalWidth > 0 && naturalHeight > 0) {
      const naturalRatio = naturalWidth / naturalHeight;
      const displayRatio = previewWidth / previewHeight;
      
      console.log(`Natural aspect ratio: ${naturalRatio.toFixed(2)}`);
      console.log(`Display aspect ratio: ${displayRatio.toFixed(2)}`);
      
      // Aspect ratios should be similar (within 0.1 tolerance)
      expect(Math.abs(naturalRatio - displayRatio)).toBeLessThan(0.1);
      
      console.log('✅ Aspect ratio preserved correctly');
    }
  });
  
  test('should test multiple actual size hover previews', async ({ page }) => {
    await page.goto('http://localhost:3001/ocr-testing');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Test multiple hover previews
    const thumbnails = page.locator('img[width="48"]');
    const thumbnailCount = await thumbnails.count();
    
    console.log(`Testing ${Math.min(thumbnailCount, 3)} actual size hover previews`);
    
    for (let i = 0; i < Math.min(thumbnailCount, 3); i++) {
      console.log(`Testing hover preview ${i + 1}...`);
      
      const thumbnail = thumbnails.nth(i);
      const groupContainer = thumbnail.locator('..').locator('..');
      
      // Hover
      await groupContainer.hover();
      await page.waitForTimeout(1000);
      
      // Check hover preview
      const hoverPreview = page.locator('div.absolute img[width="0"]').nth(i);
      await expect(hoverPreview).toBeVisible();
      
      // Check dimensions
      const naturalWidth = await hoverPreview.evaluate((img: HTMLImageElement) => img.naturalWidth);
      const naturalHeight = await hoverPreview.evaluate((img: HTMLImageElement) => img.naturalHeight);
      const displayWidth = await hoverPreview.evaluate((img: HTMLImageElement) => img.offsetWidth);
      const displayHeight = await hoverPreview.evaluate((img: HTMLImageElement) => img.offsetHeight);
      
      console.log(`Preview ${i}: Natural ${naturalWidth}x${naturalHeight}, Display ${displayWidth}x${displayHeight}`);
      
      // Verify actual size is being used
      expect(naturalWidth).toBeGreaterThan(0);
      expect(naturalHeight).toBeGreaterThan(0);
      expect(displayWidth).toBeGreaterThan(48); // Much larger than thumbnail
      expect(displayHeight).toBeGreaterThan(48); // Much larger than thumbnail
      
      console.log(`✅ Preview ${i} showing actual size correctly`);
      
      // Move mouse away
      await page.mouse.move(0, 0);
      await page.waitForTimeout(500);
    }
    
    console.log('✅ All hover previews are showing actual image sizes');
  });
});