/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false, // Temporarily disabled to prevent request doubling during development
  swcMinify: true,
  images: {
    domains: ['localhost'],
    unoptimized: true,
  },
  experimental: {
    serverComponentsExternalPackages: ['sharp', 'sqlite3']
  },
  webpack: (config) => {
    config.externals.push({
      'sharp': 'commonjs sharp',
      'sqlite3': 'commonjs sqlite3',
      'keytar': 'commonjs keytar'
    })
    
    // Exclude ReadySearch directory from Next.js compilation
    config.module.rules.push({
      test: /\.tsx?$/,
      exclude: [/node_modules/, /ReadySearch/],
    })
    
    return config
  },
  // Removed rewrites in favor of API route handlers for better JSON body handling
  // async rewrites() {
  //   return [
  //     {
  //       source: '/api/:path*',
  //       destination: `http://localhost:${process.env.BACKEND_PORT || 3003}/api/:path*`
  //     }
  //   ]
  // }
}

module.exports = nextConfig