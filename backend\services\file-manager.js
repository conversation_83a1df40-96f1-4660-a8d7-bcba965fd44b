const fs = require('fs').promises;
const path = require('path');
const logger = require('../utils/logger');
const SecurityMiddleware = require('../middleware/security');

/**
 * Unified File Management Service for OCR and ReadySearch Results
 * 
 * Handles all file saving operations with consistent naming and formatting.
 * Integrates OCR results with ReadySearch data automatically.
 */
class FileManagerService {
  constructor() {
    this.defaultSettings = {
      autoSaveJSON: true,
      autoSaveTXT: false,
      includeReadySearch: true,
      standardizedNaming: true
    };
  }

  /**
   * Generate standardized filename for result files
   */
  generateResultFilename(imagePath, fileType = 'json', suffix = 'ocr_results') {
    try {
      const directory = path.dirname(imagePath);
      const basename = path.basename(imagePath, path.extname(imagePath));
      
      // Standardized naming: always use underscore format
      const filename = `${basename}_${suffix}.${fileType}`;
      const fullPath = path.join(directory, filename);
      
      return SecurityMiddleware.validatePath(fullPath);
    } catch (error) {
      logger.error('Error generating result filename', { 
        error: error.message, 
        imagePath, 
        fileType, 
        suffix 
      });
      throw new Error(`Failed to generate filename for ${imagePath}`);
    }
  }

  /**
   * Save OCR results with optional ReadySearch integration
   */
  async saveOCRResults(imagePath, ocrResult, options = {}) {
    const settings = { ...this.defaultSettings, ...options };
    const results = [];

    try {
      const imageId = Buffer.from(imagePath).toString('base64');
      
      // Add metadata to OCR result
      const enhancedOCRResult = {
        ...ocrResult,
        imageId,
        imagePath,
        savedAt: new Date().toISOString(),
        fileFormat: 'standardized'
      };

      // Always save JSON file
      if (settings.autoSaveJSON !== false) {
        const jsonResult = await this.saveJSONResult(imagePath, enhancedOCRResult);
        results.push(jsonResult);
      }

      // Save TXT if requested
      if (settings.autoSaveTXT) {
        const txtResult = await this.saveTXTResult(imagePath, enhancedOCRResult);
        results.push(txtResult);
      }

      logger.info('OCR results saved successfully', {
        imagePath: imagePath.substring(imagePath.length - 50),
        jsonSaved: settings.autoSaveJSON !== false,
        txtSaved: settings.autoSaveTXT,
        resultCount: results.length
      });

      return {
        success: true,
        files: results,
        ocrResult: enhancedOCRResult
      };

    } catch (error) {
      logger.error('Error saving OCR results', { 
        error: error.message, 
        imagePath: imagePath.substring(imagePath.length - 50) 
      });
      throw error;
    }
  }

  /**
   * Update existing OCR results with ReadySearch data
   */
  async updateWithReadySearchResults(imagePath, readySearchResults) {
    try {
      const jsonPath = this.generateResultFilename(imagePath, 'json', 'ocr_results');
      
      // Read existing OCR results
      let existingData = {};
      try {
        const existingContent = await fs.readFile(jsonPath, 'utf8');
        existingData = JSON.parse(existingContent);
      } catch (error) {
        logger.warn('No existing OCR results found, creating new file', { jsonPath });
        existingData = {
          imagePath,
          imageId: Buffer.from(imagePath).toString('base64'),
          createdAt: new Date().toISOString()
        };
      }

      // Add ReadySearch results
      existingData.readySearchResults = {
        ...readySearchResults,
        addedAt: new Date().toISOString(),
        integrated: true
      };

      // Update modification timestamp
      existingData.lastModified = new Date().toISOString();

      // Save updated JSON
      await fs.writeFile(jsonPath, JSON.stringify(existingData, null, 2));

      // Update TXT file if it exists
      const txtPath = this.generateResultFilename(imagePath, 'txt', 'ocr_results');
      try {
        await fs.access(txtPath);
        await this.saveTXTResult(imagePath, existingData);
        logger.info('Updated TXT file with ReadySearch results', { txtPath });
      } catch (error) {
        // TXT file doesn't exist, that's okay
      }

      logger.info('Successfully updated OCR results with ReadySearch data', {
        jsonPath: jsonPath.substring(jsonPath.length - 50),
        hasMatches: readySearchResults.hasMatches,
        matchCount: readySearchResults.matches?.length || 0
      });

      return {
        success: true,
        filePath: jsonPath,
        data: existingData
      };

    } catch (error) {
      logger.error('Error updating OCR results with ReadySearch data', { 
        error: error.message, 
        imagePath: imagePath.substring(imagePath.length - 50) 
      });
      throw error;
    }
  }

  /**
   * Save JSON result file
   */
  async saveJSONResult(imagePath, data) {
    const jsonPath = this.generateResultFilename(imagePath, 'json', 'ocr_results');
    
    const jsonData = {
      ...data,
      fileVersion: '2.0',
      standardFormat: true,
      generatedBy: 'DL Organizer File Manager'
    };

    await fs.writeFile(jsonPath, JSON.stringify(jsonData, null, 2));
    
    return {
      type: 'json',
      path: jsonPath,
      size: JSON.stringify(jsonData).length,
      created: new Date().toISOString()
    };
  }

  /**
   * Save TXT result file with enhanced formatting
   */
  async saveTXTResult(imagePath, data) {
    const txtPath = this.generateResultFilename(imagePath, 'txt', 'ocr_results');
    const content = this.formatTXTContent(data);
    
    await fs.writeFile(txtPath, content, 'utf8');
    
    return {
      type: 'txt',
      path: txtPath,
      size: content.length,
      created: new Date().toISOString()
    };
  }

  /**
   * Format TXT content with OCR and ReadySearch results
   */
  formatTXTContent(data) {
    const timestamp = data.savedAt || new Date().toISOString();
    const mode = data.mode || 'us';
    const hasReadySearch = data.readySearchResults && Object.keys(data.readySearchResults).length > 0;
    
    let content = '';

    // Header section
    content += `Driver's License Analysis Results${mode === 'australian' ? ' (Australian)' : ' (US)'}\n`;
    content += '='.repeat(60) + '\n';
    content += `Processed: ${timestamp}\n`;
    content += `Model Used: ${data.modelUsed || 'Unknown'}\n`;
    content += `Confidence: ${Math.round((data.confidence || 0) * 100)}%\n`;
    content += `File Format: Standardized v2.0\n\n`;

    // OCR Results section
    content += 'OCR EXTRACTION RESULTS\n';
    content += '-'.repeat(30) + '\n';
    
    if (mode === 'australian') {
      content += `Surname: ${data.surname || data.lastName || ''}\n`;
      content += `Given Names: ${data.givenNames || ''}\n`;
      content += `First Name: ${data.firstName || ''}\n`;
      content += `Middle Name: ${data.middleName || ''}\n`;
      content += `Date of Birth: ${data.dateOfBirth || ''}\n`;
      content += `License Number: ${data.licenseNumber || ''}\n`;
      content += `Card Number: ${data.cardNumber || ''}\n`;
      content += `State/Territory: ${data.state || ''}\n`;
      content += `Expiration Date: ${data.expirationDate || ''}\n`;
      content += `Card Side: ${data.cardSide || 'Unknown'}\n`;
    } else {
      content += `First Name: ${data.firstName || ''}\n`;
      content += `Middle Name: ${data.middleName || ''}\n`;
      content += `Last Name: ${data.lastName || ''}\n`;
      content += `Date of Birth: ${data.dateOfBirth || ''}\n`;
      content += `License Number: ${data.licenseNumber || ''}\n`;
      content += `State: ${data.state || ''}\n`;
      content += `Issue Date: ${data.issueDate || ''}\n`;
      content += `Expiration Date: ${data.expirationDate || ''}\n`;
    }
    
    content += `Address: ${data.address || 'Not provided'}\n\n`;

    // ReadySearch Results section (if available)
    if (hasReadySearch) {
      const rs = data.readySearchResults;
      content += 'READYSEARCH ANALYSIS RESULTS\n';
      content += '-'.repeat(35) + '\n';
      content += `Search Query: ${rs.searchQuery || 'N/A'}\n`;
      content += `Processed: ${rs.addedAt || rs.timestamp || 'Unknown'}\n`;
      content += `Total Matches: ${rs.totalResults || rs.matches?.length || 0}\n`;
      content += `Has Matches: ${rs.hasMatches ? 'YES' : 'NO'}\n\n`;

      if (rs.matches && rs.matches.length > 0) {
        content += 'Individual Matches:\n';
        rs.matches.forEach((match, index) => {
          content += `  ${index + 1}. ${match.name} (${match.matchType})\n`;
          content += `     Confidence: ${(match.confidence * 100).toFixed(0)}%\n`;
        });
        content += '\n';
      }

      if (rs.summary) {
        content += `Summary: ${rs.summary}\n\n`;
      }
    } else {
      content += 'READYSEARCH ANALYSIS RESULTS\n';
      content += '-'.repeat(35) + '\n';
      content += 'No ReadySearch analysis performed for this image.\n\n';
    }

    // Raw text section
    content += 'RAW EXTRACTED TEXT\n';
    content += '-'.repeat(25) + '\n';
    content += (data.rawText || 'No raw text available') + '\n\n';

    // Processing details
    content += 'PROCESSING DETAILS\n';
    content += '-'.repeat(22) + '\n';
    content += `Processing Time: ${data.processingTime || 'Unknown'}ms\n`;
    content += `Cost: $${(data.cost || 0).toFixed(4)}\n`;
    content += `Image Path: ${data.imagePath || 'Unknown'}\n`;
    content += `Image ID: ${data.imageId || 'Unknown'}\n`;
    
    if (hasReadySearch) {
      const matchCount = data.readySearchResults.matches?.length || 0;
      content += `ReadySearch Status: ${matchCount > 0 ? `${matchCount} matches found` : 'No matches found'}\n`;
    }

    content += '\n' + '='.repeat(60) + '\n';
    content += 'Generated by DL Organizer File Manager v2.0\n';
    content += `Report generated: ${new Date().toISOString()}\n`;

    return content;
  }

  /**
   * Get ReadySearch match status for filename generation
   */
  getReadySearchStatus(data) {
    if (!data.readySearchResults) {
      return 'no-rs'; // No ReadySearch analysis
    }

    const hasMatches = data.readySearchResults.hasMatches;
    const matchCount = data.readySearchResults.matches?.length || 0;
    
    if (hasMatches && matchCount > 0) {
      return 'rs-m'; // ReadySearch with matches
    } else {
      return 'rs-nm'; // ReadySearch with no matches
    }
  }

  /**
   * Generate filename with ReadySearch indicators
   */
  generateFilenameWithReadySearchStatus(originalPath, data, options = {}) {
    try {
      const directory = path.dirname(originalPath);
      const extension = path.extname(originalPath);
      const basename = path.basename(originalPath, extension);
      
      const rsStatus = this.getReadySearchStatus(data);
      
      // Only add RS indicator if ReadySearch was processed
      let newBasename = basename;
      if (rsStatus !== 'no-rs' && options.includeReadySearchIndicator !== false) {
        // Remove existing rs- indicators to avoid duplicates
        newBasename = basename.replace(/-(rs-m|rs-nm|no-rs)$/i, '');
        newBasename = `${newBasename}-${rsStatus}`;
      }

      const newFilename = `${newBasename}${extension}`;
      const newPath = path.join(directory, newFilename);
      
      return {
        originalPath,
        newPath: SecurityMiddleware.validatePath(newPath),
        basename: newBasename,
        readySearchStatus: rsStatus,
        hasReadySearchData: rsStatus !== 'no-rs',
        hasMatches: rsStatus === 'rs-m'
      };
      
    } catch (error) {
      logger.error('Error generating filename with ReadySearch status', {
        error: error.message,
        originalPath
      });
      throw error;
    }
  }

  /**
   * Batch save OCR results
   */
  async batchSaveOCRResults(results, options = {}) {
    const savedFiles = [];
    const errors = [];

    for (const result of results) {
      try {
        const saveResult = await this.saveOCRResults(result.imagePath, result, options);
        savedFiles.push(saveResult);
      } catch (error) {
        errors.push({
          imagePath: result.imagePath,
          error: error.message
        });
      }
    }

    return {
      success: errors.length === 0,
      savedCount: savedFiles.length,
      errorCount: errors.length,
      savedFiles,
      errors
    };
  }

  /**
   * Clean up old result files
   */
  async cleanupOldResults(directory, maxAge = 30) {
    try {
      const files = await fs.readdir(directory);
      const cutoffDate = new Date(Date.now() - (maxAge * 24 * 60 * 60 * 1000));
      
      let cleanedCount = 0;
      
      for (const file of files) {
        if (file.includes('_ocr_results.') || file.includes('ocr-results-')) {
          const filePath = path.join(directory, file);
          const stats = await fs.stat(filePath);
          
          if (stats.mtime < cutoffDate) {
            await fs.unlink(filePath);
            cleanedCount++;
          }
        }
      }

      logger.info('Cleanup completed', { directory, cleanedCount, maxAge });
      
      return { cleanedCount };
      
    } catch (error) {
      logger.error('Error during cleanup', { error: error.message, directory });
      throw error;
    }
  }
}

module.exports = new FileManagerService();