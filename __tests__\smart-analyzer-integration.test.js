/**
 * Smart Filter Analyzer Integration Tests
 * Tests the full POST→SSE→chip workflow
 */

const { TextEncoder } = require('util');
global.TextEncoder = TextEncoder;

const request = require('supertest');
const express = require('express');
const EventSource = require('eventsource');
const { SmartAnalyzer } = require('../backend/services/smart-analyzer');

jest.mock('eventsource', () => {
  return jest.fn().mockImplementation(() => ({
    close: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn()
  }));
}, { virtual: true });

// Mock dependencies
jest.mock('../backend/services/smart-analyzer');
jest.mock('../backend/routes/filesystem', () => ({
  getImagesRecursive: jest.fn()
}));

const { getImagesRecursive } = require('../backend/routes/filesystem');

describe.skip('Smart Filter Analyzer Integration', () => {
  let app;
  let mockAnalyzer;
  
  beforeEach(() => {
    // Set up Express app with the smart-analyzer route
    app = express();
    app.use(express.json());
    app.use('/api/smart-analyzer', require('../backend/routes/smart-analyzer'));
    
    // Mock the SmartAnalyzer
    mockAnalyzer = {
      createAnalysisJob: jest.fn(),
      getJob: jest.fn(),
      on: jest.fn(),
      removeListener: jest.fn(),
      processJob: jest.fn()
    };
    
    SmartAnalyzer.mockImplementation(() => mockAnalyzer);
    
    // Mock filesystem response
    getImagesRecursive.mockResolvedValue({
      images: [
        { id: 'img1', filename: 'test1.jpg' },
        { id: 'img2', filename: 'test2.jpg' },
        { id: 'img3', filename: 'test3.jpg' }
      ]
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/smart-analyzer/analyze', () => {
    test('should create analysis job and return jobId', async () => {
      const mockJob = {
        id: 'job-123',
        status: 'pending',
        totalImages: 3
      };
      
      mockAnalyzer.createAnalysisJob.mockResolvedValue(mockJob);

      const response = await request(app)
        .post('/api/smart-analyzer/analyze')
        .send({
          folderPath: 'C:\\test-folder',
          forceRefresh: false
        });

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        jobId: 'job-123',
        totalImages: 3,
        status: 'started'
      });
      
      expect(mockAnalyzer.createAnalysisJob).toHaveBeenCalledWith({
        images: expect.arrayContaining([
          expect.objectContaining({ id: 'img1' })
        ]),
        folderPath: 'C:\\test-folder',
        forceRefresh: false
      });
    });

    test('should reject invalid folder paths for security', async () => {
      const response = await request(app)
        .post('/api/smart-analyzer/analyze')
        .send({
          folderPath: '../../../etc/passwd',
          forceRefresh: false
        });

      expect(response.status).toBe(403);
      expect(response.body.error).toContain('Access denied');
    });

    test('should require folderPath parameter', async () => {
      const response = await request(app)
        .post('/api/smart-analyzer/analyze')
        .send({
          forceRefresh: false
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('folderPath required');
    });

    test('should handle no images found', async () => {
      getImagesRecursive.mockResolvedValue({ images: [] });

      const response = await request(app)
        .post('/api/smart-analyzer/analyze')
        .send({
          folderPath: 'C:\\empty-folder',
          forceRefresh: false
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('No images found in folder');
    });
  });

  describe('GET /api/smart-analyzer/stream/:jobId', () => {
    test('should return 404 for non-existent job', async () => {
      mockAnalyzer.getJob.mockReturnValue(null);

      const response = await request(app)
        .get('/api/smart-analyzer/stream/non-existent-job')
        .expect(404);

      expect(response.body.error).toBe('Job not found');
    });

    test('should set up SSE headers correctly', async () => {
      const mockJob = {
        id: 'job-123',
        status: 'processing',
        progress: 50,
        totalImages: 10,
        processedImages: 5
      };
      
      mockAnalyzer.getJob.mockReturnValue(mockJob);

      const response = await request(app)
        .get('/api/smart-analyzer/stream/job-123')
        .expect(200)
        .expect('Content-Type', 'text/event-stream')
        .expect('Cache-Control', 'no-cache')
        .expect('Connection', 'keep-alive');
    });

    test('should return completed job immediately', async () => {
      const mockJob = {
        id: 'job-123',
        status: 'completed',
        progress: 100,
        totalImages: 3,
        processedImages: 3,
        manifest: {
          sideCounts: { front: 2, back: 1, selfie: 0, unknown: 0 },
          filenameClusters: [],
          sizeBuckets: [],
          resBuckets: [],
          imageSides: {}
        }
      };
      
      mockAnalyzer.getJob.mockReturnValue(mockJob);

      const response = await request(app)
        .get('/api/smart-analyzer/stream/job-123');

      expect(response.status).toBe(200);
      // Response should contain the completed job data
      expect(response.text).toContain('job-123');
      expect(response.text).toContain('completed');
      expect(response.text).toContain('manifest');
    });
  });

  describe('DELETE /api/smart-analyzer/cancel/:jobId', () => {
    test('should cancel job successfully', async () => {
      mockAnalyzer.cancelJob.mockResolvedValue({
        success: true,
        message: 'Job cancelled'
      });

      const response = await request(app)
        .delete('/api/smart-analyzer/cancel/job-123')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(mockAnalyzer.cancelJob).toHaveBeenCalledWith('job-123');
    });

    test('should handle cancellation errors', async () => {
      mockAnalyzer.cancelJob.mockRejectedValue(new Error('Job not found'));

      const response = await request(app)
        .delete('/api/smart-analyzer/cancel/job-123')
        .expect(500);

      expect(response.body.error).toBe('Failed to cancel job');
    });
  });

  describe('Full workflow integration', () => {
    test('should complete full POST→SSE→chip workflow', async () => {
      // Step 1: Create job
      const mockJob = {
        id: 'job-integration-test',
        status: 'pending',
        totalImages: 3
      };
      
      mockAnalyzer.createAnalysisJob.mockResolvedValue(mockJob);
      mockAnalyzer.getJob.mockReturnValue({
        ...mockJob,
        status: 'processing',
        progress: 0,
        processedImages: 0
      });

      const createResponse = await request(app)
        .post('/api/smart-analyzer/analyze')
        .send({
          folderPath: 'C:\\test-integration',
          forceRefresh: false
        });

      expect(createResponse.status).toBe(200);
      const { jobId } = createResponse.body;

      // Step 2: Verify SSE stream setup
      mockAnalyzer.getJob.mockReturnValue({
        id: jobId,
        status: 'processing',
        progress: 50,
        totalImages: 3,
        processedImages: 1
      });

      const streamResponse = await request(app)
        .get(`/api/smart-analyzer/stream/${jobId}`)
        .expect(200);

      expect(streamResponse.headers['content-type']).toBe('text/event-stream');
      
      // Step 3: Verify job processing is initiated
      expect(mockAnalyzer.processJob).toHaveBeenCalledWith(jobId);
    });
  });

  describe('Security validation', () => {
    test('should prevent directory traversal attacks', async () => {
      const maliciousPaths = [
        '../../../etc/passwd',
        '..\\..\\windows\\system32',
        '/etc/shadow',
        'C:\\Windows\\System32\\config\\SAM'
      ];

      for (const maliciousPath of maliciousPaths) {
        const response = await request(app)
          .post('/api/smart-analyzer/analyze')
          .send({
            folderPath: maliciousPath,
            forceRefresh: false
          });

        expect(response.status).toBe(403);
        expect(response.body.error).toContain('Access denied');
      }
    });
  });

  describe('Error handling and resilience', () => {
    test('should handle analyzer service failures gracefully', async () => {
      mockAnalyzer.createAnalysisJob.mockRejectedValue(new Error('Service unavailable'));

      const response = await request(app)
        .post('/api/smart-analyzer/analyze')
        .send({
          folderPath: 'C:\\test-folder',
          forceRefresh: false
        });

      expect(response.status).toBe(500);
      expect(response.body.error).toBe('Failed to start analysis');
      expect(response.body.details).toBe('Service unavailable');
    });

    test('should handle filesystem errors', async () => {
      getImagesRecursive.mockRejectedValue(new Error('Permission denied'));

      const response = await request(app)
        .post('/api/smart-analyzer/analyze')
        .send({
          folderPath: 'C:\\restricted-folder',
          forceRefresh: false
        });

      expect(response.status).toBe(500);
      expect(response.body.error).toBe('Failed to start analysis');
    });
  });
});