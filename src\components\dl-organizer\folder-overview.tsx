"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Search, 
  Grid, 
  List, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Users,
  Image as ImageIcon,
  FileText,
  Filter,
  SortAsc,
  Eye
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { FolderNode } from '@/types'

interface FolderStats {
  name: string
  path: string
  imageCount: number
  hasTextFile: boolean
  status: 'completed' | 'pending' | 'current' | 'unknown'
}

interface FolderOverviewProps {
  folders: FolderNode[]
  onFolderSelect: (folder: FolderNode) => Promise<void>
  selectedFolderId?: string
  className?: string
}

export default function FolderOverview({
  folders,
  onFolderSelect,
  selectedFolderId,
  className
}: FolderOverviewProps) {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'name' | 'images' | 'status'>('name')
  const [flatFolders, setFlatFolders] = useState<FolderStats[]>([])

  // Flatten folder tree to get all folders
  useEffect(() => {
    const flatten = (folders: any[]): FolderStats[] => {
      const result: FolderStats[] = []
      
      const processFolder = (folder: any) => {
        // Add current folder
        result.push({
          name: folder.name,
          path: folder.path,
          imageCount: folder.imageCount || 0,
          hasTextFile: folder.hasTextFile || false,
          status: folder.status || 'unknown'
        })
        
        // Process children
        if (folder.children && folder.children.length > 0) {
          folder.children.forEach(processFolder)
        }
      }
      
      folders.forEach(processFolder)
      return result
    }
    
    setFlatFolders(flatten(folders))
  }, [folders])

  // Filter and sort folders
  const filteredFolders = flatFolders
    .filter(folder => {
      const matchesSearch = folder.name.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesStatus = statusFilter === 'all' || folder.status === statusFilter
      return matchesSearch && matchesStatus
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'images':
          return b.imageCount - a.imageCount
        case 'status':
          const statusOrder = { completed: 0, current: 1, pending: 2, unknown: 3 }
          return statusOrder[a.status] - statusOrder[b.status]
        default:
          return 0
      }
    })

  const stats = {
    total: flatFolders.length,
    completed: flatFolders.filter(f => f.status === 'completed').length,
    pending: flatFolders.filter(f => f.status === 'pending').length,
    current: flatFolders.filter(f => f.status === 'current').length,
    totalImages: flatFolders.reduce((sum, f) => sum + f.imageCount, 0),
    withText: flatFolders.filter(f => f.hasTextFile).length
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'current':
        return <AlertCircle className="h-4 w-4 text-blue-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'current':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
    }
  }

  if (flatFolders.length === 0) {
    return (
      <Card className={cn("h-full", className)}>
        <CardContent className="flex items-center justify-center h-full text-muted-foreground">
          <div className="text-center">
            <Users className="h-16 w-16 mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-semibold mb-2">No Folders Found</h3>
            <p className="text-sm">
              The project doesn&apos;t contain any folders yet.<br />
              Try refreshing or check the project path.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn("h-full", className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Project Folders</CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Compact Stats */}
        <div className="grid grid-cols-2 gap-2 mt-4">
          <div className="text-center">
            <div className="text-lg font-bold">{stats.total}</div>
            <div className="text-xs text-muted-foreground">Total</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-green-600">{stats.completed}</div>
            <div className="text-xs text-muted-foreground">Done</div>
          </div>
        </div>

        {/* Compact Progress */}
        <div className="space-y-1">
          <div className="flex justify-between text-xs">
            <span>Progress</span>
            <span>{Math.round((stats.completed / stats.total) * 100)}%</span>
          </div>
          <Progress value={(stats.completed / stats.total) * 100} className="w-full h-2" />
        </div>

        {/* Compact Search */}
        <div className="space-y-2">
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground" />
            <Input
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-7 h-8 text-sm"
            />
          </div>
        </div>
      </CardHeader>

      <CardContent className="h-[calc(100%-200px)] overflow-auto">
        {viewMode === 'grid' ? (
          <div className="grid grid-cols-1 gap-2">
            {filteredFolders.map((folder, index) => (
              <Card 
                key={`${folder.path}-${index}`}
                className={cn(
                  "cursor-pointer hover:shadow-md transition-all border-2",
                  selectedFolderId === folder.path ? "border-blue-500 bg-blue-50 dark:bg-blue-950" : "border-transparent"
                )}
                onClick={() => onFolderSelect({
                  id: Buffer.from(folder.path).toString('base64'),
                  name: folder.name,
                  path: folder.path,
                  imageCount: folder.imageCount,
                  hasTextFile: folder.hasTextFile,
                  children: [],
                  isExpanded: false,
                  tags: [],
                  metadata: {}
                })}
              >
                <CardContent className="p-2">
                  <div className="space-y-1">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-xs truncate" title={folder.name}>
                          {folder.name}
                        </h4>
                      </div>
                      {getStatusIcon(folder.status)}
                    </div>
                    
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <ImageIcon className="h-3 w-3" />
                        <span>{folder.imageCount}</span>
                        {folder.hasTextFile && (
                          <FileText className="h-3 w-3 text-green-500 ml-1" />
                        )}
                      </div>
                      <Badge 
                        variant="outline" 
                        className={cn("text-xs px-1 py-0", getStatusColor(folder.status))}
                      >
                        {folder.status}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="space-y-1">
            {filteredFolders.map((folder, index) => (
              <Card
                key={`${folder.path}-${index}`}
                className={cn(
                  "cursor-pointer hover:shadow-sm transition-all border-l-2",
                  selectedFolderId === folder.path ? "border-l-blue-500 bg-blue-50 dark:bg-blue-950" : "border-l-transparent",
                  folder.status === 'completed' && "border-l-green-500",
                  folder.status === 'pending' && "border-l-yellow-500",
                  folder.status === 'current' && "border-l-blue-500"
                )}
                onClick={() => onFolderSelect({
                  id: Buffer.from(folder.path).toString('base64'),
                  name: folder.name,
                  path: folder.path,
                  imageCount: folder.imageCount,
                  hasTextFile: folder.hasTextFile,
                  children: [],
                  isExpanded: false,
                  tags: [],
                  metadata: {}
                })}
              >
                <CardContent className="p-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      {getStatusIcon(folder.status)}
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-xs truncate" title={folder.name}>
                          {folder.name}
                        </h4>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <ImageIcon className="h-3 w-3" />
                        <span>{folder.imageCount}</span>
                      </div>
                      {folder.hasTextFile && (
                        <FileText className="h-3 w-3 text-green-500" />
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {filteredFolders.length === 0 && (searchTerm || statusFilter !== 'all') && (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No folders match your filters</p>
            <Button 
              variant="ghost" 
              size="sm"
              className="mt-2"
              onClick={() => {
                setSearchTerm('')
                setStatusFilter('all')
              }}
            >
              Clear filters
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
