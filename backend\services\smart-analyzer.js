const EventEmitter = require('events');
const fs = require('fs').promises;
const path = require('path');
const cache = require('../utils/ocr-cache');
const { detectLicenseSide } = require('../utils/detect-license-side');
const { buildAnalysisManifest } = require('../utils/build-manifest');

class SmartAnalyzer extends EventEmitter {
  constructor(ocrService) {
    super();
    this.ocrService = ocrService;
    this.jobs = new Map();
    this.isProcessing = false;
    this.maxConcurrentJobs = 2; // Lower than OCR batch processing
    this.activeJobs = 0;
    this.queue = [];
  }

  async createAnalysisJob(options) {
    const job = {
      id: this.generateJobId(),
      status: 'pending',
      progress: 0,
      startTime: null,
      endTime: null,
      totalImages: options.images.length,
      processedImages: 0,
      manifest: null,
      error: null,
      images: options.images,
      folderPath: options.folderPath,
      forceRefresh: options.forceRefresh || false,
      stats: {
        sideCounts: { front: 0, back: 0, selfie: 0, unknown: 0 },
        filenameClusters: new Map(),
        sizeBuckets: new Map(),
        resBuckets: new Map(),
        imageIds: [],
        imageSides: new Map()
      }
    };

    console.log(`📝 Smart Analyzer: Created job ${job.id} with ${job.totalImages} images`);
    this.jobs.set(job.id, job);
    this.queue.push(job.id);
    console.log(`📋 Smart Analyzer: Added job to queue, queue length: ${this.queue.length}`);
    
    // Start processing if not already running
    if (!this.isProcessing) {
      console.log(`🚀 Smart Analyzer: Starting queue processing for job ${job.id}`);
      this.processQueue();
    } else {
      console.log(`⏳ Smart Analyzer: Queue processing already running, job ${job.id} will be processed when ready`);
    }

    return job;
  }

  generateJobId() {
    return `sfa_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async processQueue() {
    console.log(`🔄 Smart Analyzer: processQueue called - isProcessing: ${this.isProcessing}, queue length: ${this.queue.length}, activeJobs: ${this.activeJobs}`);
    if (this.isProcessing || this.queue.length === 0) return;
    
    this.isProcessing = true;
    console.log(`🚀 Smart Analyzer: Starting queue processing`);
    
    while (this.queue.length > 0 && this.activeJobs < this.maxConcurrentJobs) {
      const jobId = this.queue.shift();
      const job = this.jobs.get(jobId);
      
      if (job && job.status === 'pending') {
        this.activeJobs++;
        // Don't await here - let it run asynchronously
        this.processAnalysisJob(job).catch(error => {
          console.error(`❌ Smart Analyzer: Job processing error:`, error);
          this.activeJobs--;
          this.processQueue();
        });
      }
    }
    
    // Check if we need to stop processing
    if (this.activeJobs === 0 && this.queue.length === 0) {
      this.isProcessing = false;
    }
  }

  async processAnalysisJob(job) {
    try {
      job.status = 'processing';
      job.startTime = new Date().toISOString();
      
      console.log(`🔍 Smart Analyzer: Starting analysis job ${job.id} with ${job.totalImages} images`);
      
      for (let i = 0; i < job.images.length; i++) {
        const image = job.images[i];
        
        try {
          // Check if job was cancelled
          if (job.status === 'cancelled') {
            console.log(`⏹️ Smart Analyzer: Job ${job.id} was cancelled`);
            break;
          }
          
          // Analyze image
          await this.analyzeImage(image, job);
          
          job.processedImages++;
          job.progress = Math.round((job.processedImages / job.totalImages) * 100);
          
          // Emit progress update
          this.emit('progress', {
            jobId: job.id,
            progress: job.progress,
            processedImages: job.processedImages,
            totalImages: job.totalImages,
            currentImage: {
              id: image.id,
              filename: image.filename || path.basename(image.path)
            }
          });
          
          // Small delay to prevent overwhelming the system (reduced for faster processing)
          await this.sleep(10);
          
        } catch (error) {
          console.error(`❌ Smart Analyzer: Failed to analyze image ${image.id}:`, error);
          // Continue processing other images
        }
      }
      
      // Generate final manifest
      if (job.status === 'processing') {
        job.manifest = buildAnalysisManifest(job.stats);
        job.status = 'completed';
        job.endTime = new Date().toISOString();
        
        console.log(`✅ Smart Analyzer: Job ${job.id} completed successfully`);
        console.log(`📊 Smart Analyzer: Manifest summary:`, {
          totalImages: job.totalImages,
          sideCounts: job.stats.sideCounts,
          filenameClusters: job.stats.filenameClusters.size,
          sizeBuckets: job.stats.sizeBuckets.size,
          resBuckets: job.stats.resBuckets.size
        });
        
        // Send completion event
        this.emit('progress', {
          jobId: job.id,
          done: true,
          status: 'completed',
          manifest: job.manifest,
          processingTime: Date.now() - new Date(job.startTime).getTime()
        });
      }
      
    } catch (error) {
      job.status = 'failed';
      job.endTime = new Date().toISOString();
      job.error = error.message;
      
      console.error(`💥 Smart Analyzer: Job ${job.id} failed:`, error);
      
      this.emit('progress', {
        jobId: job.id,
        done: true,
        status: 'failed',
        error: error.message
      });
    } finally {
      this.activeJobs--;
      this.processQueue(); // Continue processing remaining jobs
    }
  }

  async analyzeImage(image, job) {
    const imageId = image.id || image.path;
    const filename = image.filename || path.basename(image.path);
    
    // Add to image IDs list
    job.stats.imageIds.push(imageId);
    
    // Smart image classification and grouping
    const classification = this.classifyImage(image, filename);
    
    // Group similar images for deduplication
    this.groupSimilarImages(image, classification, job.stats);
    
    // Analyze filename patterns for insights
    this.analyzeFilename(image, job.stats);
    
    // Analyze file size and resolution
    this.analyzeFileSize(image, job.stats);
    if (image.width && image.height) {
      this.analyzeResolution(image, job.stats);
    }

    // Update side counts
    job.stats.sideCounts[classification.side] = (job.stats.sideCounts[classification.side] || 0) + 1;

    // Record side and classification for this image
    job.stats.imageSides.set(imageId, classification.side);
    
    // Store full classification data
    if (!job.stats.imageClassifications) {
      job.stats.imageClassifications = new Map();
    }
    job.stats.imageClassifications.set(imageId, classification);
  }

  classifyImage(image, filename) {
    const lowerFilename = filename.toLowerCase();
    const baseName = path.parse(filename).name.toLowerCase();
    
    // Detect image side/type
    let side = 'unknown';
    if (lowerFilename.includes('front') || lowerFilename.includes('face')) {
      side = 'front';
    } else if (lowerFilename.includes('back') || lowerFilename.includes('rear')) {
      side = 'back';
    } else if (lowerFilename.includes('selfie') || lowerFilename.includes('self')) {
      side = 'selfie';
    }
    
    // Detect if it's an expanded/zoomed version
    const isExpanded = lowerFilename.includes('expand') || 
                      lowerFilename.includes('zoom') || 
                      lowerFilename.includes('large') ||
                      lowerFilename.includes('_e') ||
                      lowerFilename.includes('-e');
    
    // Extract base identifier (remove expanded/duplicate markers)
    let baseId = baseName
      .replace(/[-_](expand|expanded|zoom|large|e|copy|duplicate).*$/g, '')
      .replace(/[-_]\d+$/g, '') // Remove trailing numbers
      .replace(/[-_](front|back|selfie|self)$/g, ''); // Remove side indicators
    
    // Quality scoring (prefer higher resolution, non-expanded versions)
    let qualityScore = 0;
    if (image.width && image.height) {
      qualityScore += Math.log(image.width * image.height) / 100; // Resolution score
    }
    if (!isExpanded) {
      qualityScore += 10; // Prefer originals over expanded
    }
    if (image.fileSize) {
      qualityScore += Math.log(image.fileSize) / 1000; // File size score
    }
    
    return {
      side,
      isExpanded,
      baseId,
      qualityScore,
      filename,
      imageId: image.id || image.path
    };
  }

  groupSimilarImages(image, classification, stats) {
    if (!stats.imageGroups) {
      stats.imageGroups = new Map();
    }
    
    // Create group key based on base ID and side
    const groupKey = `${classification.baseId}_${classification.side}`;
    
    if (!stats.imageGroups.has(groupKey)) {
      stats.imageGroups.set(groupKey, {
        baseId: classification.baseId,
        side: classification.side,
        images: [],
        selectedImage: null,
        duplicateCount: 0
      });
    }
    
    const group = stats.imageGroups.get(groupKey);
    group.images.push({
      imageId: classification.imageId,
      filename: classification.filename,
      qualityScore: classification.qualityScore,
      isExpanded: classification.isExpanded,
      width: image.width,
      height: image.height,
      fileSize: image.fileSize
    });
    
    // Update selected image if this one has higher quality score
    if (!group.selectedImage || classification.qualityScore > group.selectedImage.qualityScore) {
      group.selectedImage = {
        imageId: classification.imageId,
        filename: classification.filename,
        qualityScore: classification.qualityScore,
        isExpanded: classification.isExpanded,
        width: image.width,
        height: image.height,
        fileSize: image.fileSize
      };
    }
    
    // Count duplicates (more than 1 image in group)
    group.duplicateCount = Math.max(0, group.images.length - 1);
  }

  analyzeFilename(image, stats) {
    const filename = image.filename || path.basename(image.path);
    const baseName = path.parse(filename).name.toLowerCase();
    
    // Extract tokens from filename
    const tokens = baseName.split(/[-_.\s]+/).filter(token => 
      token.length > 1 && !/^\d+$/.test(token) // Ignore single chars and pure numbers
    );
    
    // Count token occurrences
    tokens.forEach(token => {
      const count = stats.filenameClusters.get(token) || 0;
      stats.filenameClusters.set(token, count + 1);
    });
  }

  analyzeFileSize(image, stats) {
    const size = image.fileSize || image.size || 0;
    
    let bucket;
    if (size < 100 * 1024) bucket = '< 100KB';
    else if (size < 300 * 1024) bucket = '100KB - 300KB';
    else if (size < 500 * 1024) bucket = '300KB - 500KB';
    else if (size < 1024 * 1024) bucket = '500KB - 1MB';
    else if (size < 2 * 1024 * 1024) bucket = '1MB - 2MB';
    else if (size < 5 * 1024 * 1024) bucket = '2MB - 5MB';
    else bucket = '> 5MB';
    
    const count = stats.sizeBuckets.get(bucket) || 0;
    stats.sizeBuckets.set(bucket, count + 1);
  }

  analyzeResolution(image, stats) {
    const width = image.width || 0;
    const height = image.height || 0;
    
    if (width === 0 || height === 0) return;
    
    // Create resolution bucket
    let bucket;
    const megapixels = (width * height) / (1024 * 1024);
    
    if (megapixels < 1) bucket = '< 1MP';
    else if (megapixels < 2) bucket = '1-2MP';
    else if (megapixels < 5) bucket = '2-5MP';
    else if (megapixels < 8) bucket = '5-8MP';
    else if (megapixels < 12) bucket = '8-12MP';
    else bucket = '> 12MP';
    
    const count = stats.resBuckets.get(bucket) || 0;
    stats.resBuckets.set(bucket, count + 1);
  }

  async cancelJob(jobId) {
    const job = this.jobs.get(jobId);
    if (!job) {
      throw new Error('Job not found');
    }
    
    if (job.status === 'processing' || job.status === 'pending') {
      job.status = 'cancelled';
      job.endTime = new Date().toISOString();
      
      this.emit('progress', {
        jobId: job.id,
        done: true,
        status: 'cancelled'
      });
      
      console.log(`⏹️ Smart Analyzer: Job ${jobId} cancelled`);
    }
    
    return job;
  }

  getJob(jobId) {
    return this.jobs.get(jobId);
  }

  getAllJobs() {
    return Array.from(this.jobs.values()).sort((a, b) => 
      new Date(b.startTime || 0).getTime() - new Date(a.startTime || 0).getTime()
    );
  }

  deleteJob(jobId) {
    const job = this.jobs.get(jobId);
    if (job && job.status !== 'processing') {
      this.jobs.delete(jobId);
      return true;
    }
    return false;
  }

  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = { SmartAnalyzer };