const express = require('express');
const path = require('path');
const fs = require('fs').promises;
const sharp = require('sharp');
const os = require('os');
const router = express.Router();

// Helper function to count image files in a directory (recursively)
async function countImages(dirPath, recursive = true, maxDepth = 10, currentDepth = 0) {
  try {
    if (currentDepth >= maxDepth) return 0;
    
    const files = await fs.readdir(dirPath, { withFileTypes: true });
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff'];
    
    let imageCount = 0;
    
    for (const file of files) {
      if (file.isFile()) {
        const ext = path.extname(file.name).toLowerCase();
        if (imageExtensions.includes(ext)) {
          imageCount++;
        }
      } else if (file.isDirectory() && recursive) {
        // Recursively count images in subdirectories
        const subDirPath = path.join(dirPath, file.name);
        const subCount = await countImages(subDirPath, recursive, maxDepth, currentDepth + 1);
        imageCount += subCount;
      }
    }
    
    return imageCount;
  } catch (error) {
    return 0;
  }
}

// Helper function to check for text files (recursively)
async function hasTextFile(dirPath, recursive = true, maxDepth = 10, currentDepth = 0) {
  try {
    if (currentDepth >= maxDepth) return false;
    
    const files = await fs.readdir(dirPath, { withFileTypes: true });
    
    for (const file of files) {
      if (file.isFile()) {
        if (path.extname(file.name).toLowerCase() === '.txt') {
          return true;
        }
      } else if (file.isDirectory() && recursive) {
        // Recursively check subdirectories
        const subDirPath = path.join(dirPath, file.name);
        const hasText = await hasTextFile(subDirPath, recursive, maxDepth, currentDepth + 1);
        if (hasText) return true;
      }
    }
    
    return false;
  } catch (error) {
    return false;
  }
}

// Helper function to determine folder status based on name
function getFolderStatus(folderName) {
  const name = folderName.toLowerCase();
  if (name.includes('done')) return 'completed';
  if (name.includes('current')) return 'current';
  if (name.includes('todo') || name.includes('to_do')) return 'pending';
  return 'unknown';
}

// Get images from a folder (with recursive scanning support)
router.post('/images', async (req, res) => {
  try {
    const { folderPath, recursive = false, maxDepth, generateThumbnails = true } = req.body;
    
    // When recursive is true, default to deep scanning (high maxDepth)
    const effectiveMaxDepth = recursive ? (maxDepth || 999) : (maxDepth || 1);
    
    if (!folderPath) {
      return res.status(400).json({ error: 'Folder path is required' });
    }

    // Verify path exists and is accessible
    try {
      const stats = await fs.stat(folderPath);
      if (!stats.isDirectory()) {
        return res.status(400).json({ error: 'Path is not a directory' });
      }
    } catch (error) {
      return res.status(400).json({ error: 'Path does not exist or is not accessible' });
    }

    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff'];
    const images = [];

    // Function to recursively find all images
    async function findImages(dirPath, currentDepth = 0) {
      if (currentDepth >= effectiveMaxDepth) return;
      
      try {
        const files = await fs.readdir(dirPath, { withFileTypes: true });
        
        for (const file of files) {
          const fullPath = path.join(dirPath, file.name);
          
          if (file.isFile()) {
            const ext = path.extname(file.name).toLowerCase();
            if (imageExtensions.includes(ext)) {
              const stats = await fs.stat(fullPath);
              
              // Generate thumbnail path
              const imageId = Buffer.from(fullPath).toString('base64');
              const thumbnailPath = path.join(__dirname, '../../data/thumbnails', `${imageId}.jpg`);
              const previewPath = path.join(__dirname, '../../data/previews', `${imageId}.jpg`);
              
              // Generate thumbnails if requested
              if (generateThumbnails) {
                try {
                  // Ensure thumbnail directories exist
                  await fs.mkdir(path.dirname(thumbnailPath), { recursive: true });
                  await fs.mkdir(path.dirname(previewPath), { recursive: true });
                  
                  // Generate thumbnail (150x150) if it doesn't exist
                  try {
                    await fs.access(thumbnailPath);
                  } catch {
                    await sharp(fullPath)
                      .resize(150, 150, { fit: 'inside', withoutEnlargement: false })
                      .jpeg({ quality: 85 })
                      .toFile(thumbnailPath);
                  }
                  
                  // Generate preview (600x600) if it doesn't exist
                  try {
                    await fs.access(previewPath);
                  } catch {
                    await sharp(fullPath)
                      .resize(600, 600, { fit: 'inside', withoutEnlargement: false })
                      .jpeg({ quality: 90 })
                      .toFile(previewPath);
                  }
                } catch (thumbnailError) {
                  console.warn(`Failed to generate thumbnail for ${fullPath}:`, thumbnailError.message);
                }
              }
              
              // Extract image dimensions using Sharp
              let width = 0, height = 0;
              try {
                const metadata = await sharp(fullPath).metadata();
                width = metadata.width || 0;
                height = metadata.height || 0;
              } catch (sharpError) {
                console.warn(`Could not extract image dimensions for: ${fullPath}`, sharpError.message);
              }

              images.push({
                id: imageId,
                filename: file.name,
                path: fullPath,
                relativePath: path.relative(folderPath, fullPath),
                fileSize: stats.size,
                lastModified: stats.mtime,
                width: width,
                height: height,
                rotation: 0,
                thumbnailUrl: `/thumbnails/${imageId}.jpg`,
                previewUrl: `/previews/${imageId}.jpg`,
                folderId: Buffer.from(folderPath).toString('base64')
              });
            }
          } else if (file.isDirectory() && recursive) {
            // Recursively scan subdirectories
            await findImages(fullPath, currentDepth + 1);
          }
        }
      } catch (error) {
        console.error(`Error scanning directory ${dirPath}:`, error);
      }
    }

    await findImages(folderPath);

    res.json({
      success: true,
      folderPath,
      imageCount: images.length,
      recursive,
      maxDepth: effectiveMaxDepth,
      images: images.sort((a, b) => a.relativePath.localeCompare(b.relativePath))
    });
    
  } catch (error) {
    console.error('Error getting images:', error);
    res.status(500).json({
      error: 'Failed to get images',
      details: error.message
    });
  }
});

// Get image file for OCR processing
router.get('/image-file', async (req, res) => {
  try {
    const { imagePath } = req.query;
    
    if (!imagePath) {
      return res.status(400).json({ error: 'Image path is required' });
    }

    // Check if file exists
    const stats = await fs.stat(imagePath);
    if (!stats.isFile()) {
      return res.status(404).json({ error: 'Image file not found' });
    }

    // Read the file
    const imageData = await fs.readFile(imagePath);
    
    // Determine content type based on file extension
    const ext = path.extname(imagePath).toLowerCase();
    const contentTypeMap = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.bmp': 'image/bmp',
      '.webp': 'image/webp',
      '.tiff': 'image/tiff'
    };
    
    const contentType = contentTypeMap[ext] || 'application/octet-stream';
    
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Length', imageData.length);
    res.send(imageData);
  } catch (error) {
    console.error('Error serving image file:', error);
    res.status(500).json({ 
      error: 'Failed to serve image file',
      details: error.message 
    });
  }
});

// Get available drives (Windows-specific)
router.get('/drives', async (req, res) => {
  try {
    if (os.platform() !== 'win32') {
      return res.status(400).json({ error: 'Drive detection only supported on Windows' });
    }

    const drives = [];
    
    // Check common drive letters A-Z
    for (let i = 65; i <= 90; i++) {
      const driveLetter = String.fromCharCode(i);
      const drivePath = `${driveLetter}:\\`;
      
      try {
        const stats = await fs.stat(drivePath);
        if (stats.isDirectory()) {
          drives.push({
            name: `${driveLetter}: Drive`,
            path: drivePath,
            type: 'drive',
            isExpanded: false
          });
        }
      } catch (error) {
        // Drive not accessible, skip it
      }
    }

    res.json({
      success: true,
      drives: drives.sort((a, b) => a.path.localeCompare(b.path))
    });
    
  } catch (error) {
    console.error('Error getting drives:', error);
    res.status(500).json({
      error: 'Failed to get drives',
      details: error.message
    });
  }
});

// Get folders in a directory (for folder picker)
router.post('/folders', async (req, res) => {
  try {
    const { path: folderPath, includeStats = false, maxDepth = 1 } = req.body;
    
    if (!folderPath) {
      return res.status(400).json({ error: 'Path is required' });
    }

    // Verify path exists and is accessible
    try {
      const stats = await fs.stat(folderPath);
      if (!stats.isDirectory()) {
        return res.status(400).json({ error: 'Path is not a directory' });
      }
    } catch (error) {
      return res.status(400).json({ error: 'Path does not exist or is not accessible' });
    }

    const folders = [];
    
    try {
      const entries = await fs.readdir(folderPath, { withFileTypes: true });
      const subfolders = entries.filter(entry => entry.isDirectory());
      
      for (const folder of subfolders) {
        const subfolderPath = path.join(folderPath, folder.name);
        
        // Get folder statistics if requested
        let imageCount = 0;
        let hasText = false;
        
        if (includeStats) {
          imageCount = await countImages(subfolderPath);
          hasText = await hasTextFile(subfolderPath);
        }
        
        const folderNode = {
          name: folder.name,
          path: subfolderPath,
          type: 'folder',
          isExpanded: false,
          imageCount,
          hasTextFile: hasText
        };
        
        folders.push(folderNode);
      }
    } catch (error) {
      console.error(`Error reading directory ${folderPath}:`, error);
      return res.status(500).json({
        error: 'Failed to read directory',
        details: error.message
      });
    }

    res.json({
      success: true,
      path: folderPath,
      folders: folders.sort((a, b) => a.name.localeCompare(b.name))
    });
    
  } catch (error) {
    console.error('Error getting folders:', error);
    res.status(500).json({
      error: 'Failed to get folders',
      details: error.message
    });
  }
});

// Scan directory for folders and create folder tree
router.post('/scan', async (req, res) => {
  try {
    const { rootPath, maxDepth = 10, includeStats = true, deepImageScan = true } = req.body;
    
    if (!rootPath) {
      return res.status(400).json({ error: 'Root path is required' });
    }

    // Detect potentially large directories and adjust parameters
    const isRootDrive = rootPath.match(/^[A-Z]:\\?$/);
    const isSystemPath = rootPath.includes('Program Files') || rootPath.includes('Windows') || rootPath.includes('System32');
    const isLargeDirectory = isRootDrive || isSystemPath || rootPath.length <= 3;
    
    // Adjust scanning parameters for large directories
    const effectiveMaxDepth = isLargeDirectory ? Math.min(maxDepth, 2) : maxDepth;
    const effectiveImageScan = isLargeDirectory ? false : deepImageScan;
    
    console.log(`Scanning directory: ${rootPath}, isLarge: ${isLargeDirectory}, depth: ${effectiveMaxDepth}`);

    // Verify path exists and is accessible
    try {
      const stats = await fs.stat(rootPath);
      if (!stats.isDirectory()) {
        return res.status(400).json({ error: 'Path is not a directory' });
      }
    } catch (error) {
      return res.status(400).json({ 
        error: 'Path does not exist or is not accessible',
        details: error.message 
      });
    }

    const folders = [];
    
    // Recursive function to scan directories
    async function scanDirectory(dirPath, currentDepth = 0, parentId = null) {
      if (currentDepth >= effectiveMaxDepth) return [];
      
      try {
        const entries = await fs.readdir(dirPath, { withFileTypes: true });
        const subfolders = entries.filter(entry => entry.isDirectory());
        
        // Skip hidden system folders for large directories
        const filteredSubfolders = isLargeDirectory && currentDepth === 0 
          ? subfolders.filter(folder => !folder.name.startsWith('.') && 
                                       !folder.name.startsWith('$') &&
                                       !['System Volume Information', 'Recovery'].includes(folder.name))
          : subfolders;
        
        const folderData = [];
        
        for (const folder of filteredSubfolders) {
          const folderPath = path.join(dirPath, folder.name);
          const folderId = Buffer.from(folderPath).toString('base64');
          
          // Get folder statistics if requested
          let imageCount = 0;
          let hasText = false;
          
          if (includeStats) {
            // Use optimized scanning parameters
            imageCount = await countImages(folderPath, effectiveImageScan);
            hasText = await hasTextFile(folderPath, effectiveImageScan);
          }
          
          const folderNode = {
            id: folderId,
            name: folder.name,
            path: folderPath,
            parentId,
            imageCount,
            hasTextFile: hasText,
            status: getFolderStatus(folder.name),
            isExpanded: false,
            tags: [],
            children: []
          };
          
          // Recursively scan subdirectories
          folderNode.children = await scanDirectory(folderPath, currentDepth + 1, folderId);
          
          folderData.push(folderNode);
        }
        
        return folderData;
      } catch (error) {
        console.error(`Error scanning directory ${dirPath}:`, error);
        return [];
      }
    }

    const scannedFolders = await scanDirectory(rootPath);
    
    // If no subfolders found at the root but there are images, create virtual folder structure
    if (scannedFolders.length === 0 && includeStats) {
      const rootImageCount = await countImages(rootPath, false); // Count only direct images
      if (rootImageCount > 0) {
        // Create a virtual folder for the root directory images
        const rootFolderId = Buffer.from(rootPath).toString('base64');
        scannedFolders.push({
          id: rootFolderId,
          name: path.basename(rootPath) || 'Root Images',
          path: rootPath,
          parentId: null,
          imageCount: rootImageCount,
          hasTextFile: await hasTextFile(rootPath, false),
          status: 'unknown',
          isExpanded: false,
          tags: [],
          children: [],
          isVirtual: true
        });
      }
    }
    
    // Calculate total statistics
    const totalStats = {
      totalFolders: 0,
      totalImages: 0,
      completedFolders: 0,
      pendingFolders: 0
    };
    
    function calculateStats(folders) {
      for (const folder of folders) {
        totalStats.totalFolders++;
        totalStats.totalImages += folder.imageCount;
        
        if (folder.status === 'completed') totalStats.completedFolders++;
        if (folder.status === 'pending') totalStats.pendingFolders++;
        
        calculateStats(folder.children);
      }
    }
    
    calculateStats(scannedFolders);

    res.json({
      success: true,
      rootPath,
      folders: scannedFolders,
      stats: totalStats,
      scanTime: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Error scanning filesystem:', error);
    res.status(500).json({
      error: 'Failed to scan filesystem',
      details: error.message
    });
  }
});


// Get folder overview/summary
router.post('/overview', async (req, res) => {
  try {
    const { rootPath } = req.body;
    
    if (!rootPath) {
      return res.status(400).json({ error: 'Root path is required' });
    }

    const overview = {
      totalFolders: 0,
      completedFolders: 0,
      pendingFolders: 0,
      currentFolders: 0,
      totalImages: 0,
      totalTextFiles: 0,
      foldersByStatus: {
        completed: [],
        pending: [],
        current: [],
        unknown: []
      },
      recentActivity: []
    };

    async function analyzeFolder(dirPath, depth = 0) {
      if (depth > 2) return; // Limit depth for performance
      
      try {
        const entries = await fs.readdir(dirPath, { withFileTypes: true });
        
        for (const entry of entries) {
          if (entry.isDirectory()) {
            const folderPath = path.join(dirPath, entry.name);
            const status = getFolderStatus(entry.name);
            
            overview.totalFolders++;
            
            switch (status) {
              case 'completed':
                overview.completedFolders++;
                overview.foldersByStatus.completed.push({
                  name: entry.name,
                  path: folderPath
                });
                break;
              case 'pending':
                overview.pendingFolders++;
                overview.foldersByStatus.pending.push({
                  name: entry.name,
                  path: folderPath
                });
                break;
              case 'current':
                overview.currentFolders++;
                overview.foldersByStatus.current.push({
                  name: entry.name,
                  path: folderPath
                });
                break;
              default:
                overview.foldersByStatus.unknown.push({
                  name: entry.name,
                  path: folderPath
                });
            }
            
            // Count files in this folder
            try {
              const files = await fs.readdir(folderPath);
              for (const file of files) {
                const ext = path.extname(file).toLowerCase();
                if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff'].includes(ext)) {
                  overview.totalImages++;
                } else if (ext === '.txt') {
                  overview.totalTextFiles++;
                }
              }
            } catch (error) {
              // Folder not accessible, skip
            }
            
            // Recursively analyze subfolders
            await analyzeFolder(folderPath, depth + 1);
          }
        }
      } catch (error) {
        console.error(`Error analyzing folder ${dirPath}:`, error);
      }
    }

    await analyzeFolder(rootPath);
    
    // Limit the folder lists to prevent overwhelming the UI
    Object.keys(overview.foldersByStatus).forEach(status => {
      overview.foldersByStatus[status] = overview.foldersByStatus[status].slice(0, 20);
    });

    res.json({
      success: true,
      rootPath,
      overview,
      generatedAt: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Error generating folder overview:', error);
    res.status(500).json({
      error: 'Failed to generate folder overview',
      details: error.message
    });
  }
});

// Create a new folder
router.post('/create-folder', async (req, res) => {
  try {
    const { parentPath, folderName } = req.body;
    
    if (!parentPath || !folderName) {
      return res.status(400).json({ error: 'Parent path and folder name are required' });
    }

    const newFolderPath = path.join(parentPath, folderName);
    
    // Check if folder already exists
    try {
      await fs.stat(newFolderPath);
      return res.status(400).json({ error: 'Folder already exists' });
    } catch {
      // Folder doesn't exist, which is what we want
    }

    await fs.mkdir(newFolderPath, { recursive: true });
    
    res.json({
      success: true,
      folderPath: newFolderPath,
      message: 'Folder created successfully'
    });
    
  } catch (error) {
    console.error('Error creating folder:', error);
    res.status(500).json({
      error: 'Failed to create folder',
      details: error.message
    });
  }
});

// Rename a folder
router.post('/rename-folder', async (req, res) => {
  try {
    const { oldPath, newName } = req.body;
    
    if (!oldPath || !newName) {
      return res.status(400).json({ error: 'Old path and new name are required' });
    }

    const newPath = path.join(path.dirname(oldPath), newName);
    
    await fs.rename(oldPath, newPath);
    
    res.json({
      success: true,
      oldPath,
      newPath,
      message: 'Folder renamed successfully'
    });
    
  } catch (error) {
    console.error('Error renaming folder:', error);
    res.status(500).json({
      error: 'Failed to rename folder',
      details: error.message
    });
  }
});

// Get all text files in a folder
router.post('/text-files', async (req, res) => {
  try {
    const { folderPath } = req.body;
    
    if (!folderPath) {
      return res.status(400).json({ error: 'Folder path is required' });
    }

    // Verify folder exists
    try {
      const stats = await fs.stat(folderPath);
      if (!stats.isDirectory()) {
        return res.status(400).json({ error: 'Path is not a directory' });
      }
    } catch (error) {
      return res.status(400).json({ error: 'Folder does not exist or is not accessible' });
    }

    const files = await fs.readdir(folderPath);
    const textExtensions = ['.txt', '.md', '.log', '.json', '.csv'];
    const textFiles = [];
    
    for (const file of files) {
      const filePath = path.join(folderPath, file);
      const ext = path.extname(file).toLowerCase();
      
      if (textExtensions.includes(ext)) {
        try {
          const stats = await fs.stat(filePath);
          
          textFiles.push({
            name: file,
            path: filePath,
            size: stats.size,
            lastModified: stats.mtime,
            exists: true
          });
        } catch (error) {
          console.error(`Error processing text file ${file}:`, error);
        }
      }
    }

    res.json({
      success: true,
      folderPath,
      files: textFiles.sort((a, b) => a.name.localeCompare(b.name))
    });
    
  } catch (error) {
    console.error('Error getting text files:', error);
    res.status(500).json({
      error: 'Failed to get text files',
      details: error.message
    });
  }
});

// Get content of a specific text file
router.get('/text-content', async (req, res) => {
  try {
    const { filePath } = req.query;
    
    if (!filePath) {
      return res.status(400).json({ error: 'File path is required' });
    }

    // Verify file exists
    try {
      const stats = await fs.stat(filePath);
      if (!stats.isFile()) {
        return res.status(400).json({ error: 'Path is not a file' });
      }
    } catch (error) {
      return res.status(404).json({ error: 'File does not exist' });
    }

    const content = await fs.readFile(filePath, 'utf-8');
    
    res.json({
      success: true,
      content,
      path: filePath
    });
    
  } catch (error) {
    console.error('Error reading text file:', error);
    res.status(500).json({
      error: 'Failed to read text file',
      details: error.message
    });
  }
});

// Create a new text file
router.post('/text-content', async (req, res) => {
  try {
    const { filePath, content = '' } = req.body;
    
    if (!filePath) {
      return res.status(400).json({ error: 'File path is required' });
    }

    // Check if file already exists
    try {
      await fs.stat(filePath);
      return res.status(400).json({ error: 'File already exists' });
    } catch {
      // File doesn't exist, which is what we want
    }

    // Ensure directory exists
    const dir = path.dirname(filePath);
    await fs.mkdir(dir, { recursive: true });
    
    await fs.writeFile(filePath, content, 'utf-8');
    
    res.json({
      success: true,
      path: filePath,
      message: 'File created successfully'
    });
    
  } catch (error) {
    console.error('Error creating text file:', error);
    res.status(500).json({
      error: 'Failed to create text file',
      details: error.message
    });
  }
});

// Update content of a text file
router.put('/text-content', async (req, res) => {
  try {
    const { filePath, content } = req.body;
    
    if (!filePath || content === undefined) {
      return res.status(400).json({ error: 'File path and content are required' });
    }

    // Verify file exists
    try {
      const stats = await fs.stat(filePath);
      if (!stats.isFile()) {
        return res.status(400).json({ error: 'Path is not a file' });
      }
    } catch (error) {
      return res.status(404).json({ error: 'File does not exist' });
    }

    await fs.writeFile(filePath, content, 'utf-8');
    
    res.json({
      success: true,
      path: filePath,
      message: 'File updated successfully'
    });
    
  } catch (error) {
    console.error('Error updating text file:', error);
    res.status(500).json({
      error: 'Failed to update text file',
      details: error.message
    });
  }
});

// Delete a text file
router.delete('/text-content', async (req, res) => {
  try {
    const { filePath } = req.body;
    
    if (!filePath) {
      return res.status(400).json({ error: 'File path is required' });
    }

    // Verify file exists
    try {
      const stats = await fs.stat(filePath);
      if (!stats.isFile()) {
        return res.status(400).json({ error: 'Path is not a file' });
      }
    } catch (error) {
      return res.status(404).json({ error: 'File does not exist' });
    }

    await fs.unlink(filePath);
    
    res.json({
      success: true,
      path: filePath,
      message: 'File deleted successfully'
    });
    
  } catch (error) {
    console.error('Error deleting text file:', error);
    res.status(500).json({
      error: 'Failed to delete text file',
      details: error.message
    });
  }
});

// Rename a text file and update associated files
router.post('/rename', async (req, res) => {
  try {
    const { oldPath, newPath, updateAssociatedFiles } = req.body;
    
    if (!oldPath || !newPath) {
      return res.status(400).json({ error: 'Both oldPath and newPath are required' });
    }

    // Verify old file exists
    try {
      const stats = await fs.stat(oldPath);
      if (!stats.isFile()) {
        return res.status(400).json({ error: 'Path is not a file' });
      }
    } catch (error) {
      return res.status(404).json({ error: 'File does not exist' });
    }

    // Check if new path already exists
    try {
      await fs.stat(newPath);
      return res.status(400).json({ error: 'A file with that name already exists' });
    } catch (error) {
      // Good, file doesn't exist
    }

    // Extract filename parts for associated file detection
    const oldBasename = path.basename(oldPath, path.extname(oldPath));
    const newBasename = path.basename(newPath, path.extname(newPath));
    const dir = path.dirname(oldPath);
    const ext = path.extname(oldPath);

    // Rename the main file
    await fs.rename(oldPath, newPath);
    
    const updatedFiles = [{ old: oldPath, new: newPath }];

    // If updateAssociatedFiles is true, handle OCR result files
    if (updateAssociatedFiles) {
      // Check for OCR result files (JSON cache files with hash)
      const files = await fs.readdir(dir);
      const ocrPattern = new RegExp(`^${oldBasename}_ocr_results_[a-f0-9]+\\.json$`);
      const txtPattern = new RegExp(`^${oldBasename}_ocr_results\\.txt$`);
      
      for (const file of files) {
        let shouldRename = false;
        let newFileName = '';
        
        if (ocrPattern.test(file)) {
          // JSON cache file with hash
          newFileName = file.replace(oldBasename, newBasename);
          shouldRename = true;
        } else if (txtPattern.test(file)) {
          // TXT export file
          newFileName = file.replace(oldBasename, newBasename);
          shouldRename = true;
        }
        
        if (shouldRename) {
          const oldFilePath = path.join(dir, file);
          const newFilePath = path.join(dir, newFileName);
          
          try {
            // For JSON files, also update the imagePath inside
            if (file.endsWith('.json')) {
              const content = await fs.readFile(oldFilePath, 'utf-8');
              const data = JSON.parse(content);
              
              // Update imagePath if it exists
              if (data.imagePath) {
                data.imagePath = data.imagePath.replace(path.basename(oldPath), path.basename(newPath));
              }
              
              // Add to rename history
              if (!data.renameHistory) {
                data.renameHistory = [];
              }
              data.renameHistory.push({
                from: path.basename(oldPath),
                to: path.basename(newPath),
                timestamp: new Date().toISOString()
              });
              
              data.lastModified = new Date().toISOString();
              
              // Write updated content
              await fs.writeFile(oldFilePath, JSON.stringify(data, null, 2));
            }
            
            // Rename the file
            await fs.rename(oldFilePath, newFilePath);
            updatedFiles.push({ old: oldFilePath, new: newFilePath });
          } catch (err) {
            console.error(`Error updating associated file ${file}:`, err);
          }
        }
      }
    }
    
    res.json({
      success: true,
      updatedFiles,
      message: 'File renamed successfully'
    });
    
  } catch (error) {
    console.error('Error renaming file:', error);
    res.status(500).json({
      error: 'Failed to rename file',
      details: error.message
    });
  }
});

// Extract reusable function for getting images recursively
async function getImagesRecursive(folderPath, options = {}) {
  const { recursive = true, maxDepth = 999, generateThumbnails = false } = options;
  const effectiveMaxDepth = recursive ? maxDepth : 1;
  
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff'];
  const images = [];
  
  // Function to recursively find all images
  async function findImages(dirPath, currentDepth = 0) {
    if (currentDepth >= effectiveMaxDepth) return;
    
    try {
      const files = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const file of files) {
        const fullPath = path.join(dirPath, file.name);
        
        if (file.isFile()) {
          const ext = path.extname(file.name).toLowerCase();
          if (imageExtensions.includes(ext)) {
            const stats = await fs.stat(fullPath);
            
            // Generate thumbnail path
            const imageId = Buffer.from(fullPath).toString('base64');
            
            let width = 0, height = 0;
            try {
              const metadata = await sharp(fullPath).metadata();
              width = metadata.width || 0;
              height = metadata.height || 0;
            } catch (sharpError) {
              console.warn(`Could not extract image dimensions for: ${fullPath}`, sharpError.message);
            }

            images.push({
              id: imageId,
              filename: file.name,
              path: fullPath,
              relativePath: path.relative(folderPath, fullPath),
              fileSize: stats.size,
              lastModified: stats.mtime,
              width: width,
              height: height,
              rotation: 0,
              thumbnailUrl: `/thumbnails/${imageId}.jpg`,
              previewUrl: `/previews/${imageId}.jpg`,
              folderId: Buffer.from(folderPath).toString('base64')
            });
          }
        } else if (file.isDirectory() && recursive) {
          // Recursively scan subdirectories
          await findImages(fullPath, currentDepth + 1);
        }
      }
    } catch (error) {
      console.error(`Error scanning directory ${dirPath}:`, error);
    }
  }

  await findImages(folderPath);
  
  return {
    success: true,
    folderPath,
    imageCount: images.length,
    recursive,
    maxDepth: effectiveMaxDepth,
    images: images.sort((a, b) => a.relativePath.localeCompare(b.relativePath))
  };
}

// Export both the router and the utility function
module.exports = router;
module.exports.getImagesRecursive = getImagesRecursive;
