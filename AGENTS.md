# AGENTS.md

## Build/Lint/Test Commands
- `npm run dev` - Start dev servers
- `npm run lint` - ESLint check
- `npm run typecheck` - TypeScript check
- `npm run test` - Run Jest tests
- `npm run test:e2e` - Playwright E2E tests
- `npm run test:watch` - Jest watch mode
- `npm run test:e2e -- --grep="test-name"` - Run single E2E test

## Code Style Guidelines
- **Imports**: Use `@/` prefix for src imports, absolute paths preferred
- **Types**: Strict TypeScript with `strict: true`, prefer interfaces over types
- **Naming**: camelCase for variables/functions, PascalCase for components, UPPER_SNAKE_CASE for constants
- **Error Handling**: Use try/catch with proper error logging via Winston
- **Formatting**: Follow Next.js ESLint rules, no trailing semicolons
- **Components**: Functional components with TypeScript, use React.FC sparingly
- **Backend**: CommonJS modules, use `module.exports` and `require()`
- **Testing**: Jest for unit tests, Playwright for E2E, test files use `.spec.ts` or `.test.js`