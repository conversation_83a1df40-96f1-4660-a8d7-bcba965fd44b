const logger = require('../utils/logger');

class ErrorHandler {
  static handle(err, req, res, next) {
    // Log the error
    logger.error('Unhandled error', {
      error: err.message,
      stack: err.stack,
      url: req.originalUrl,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
    
    // Determine error type and response
    let statusCode = 500;
    let message = 'Internal server error';
    let details = null;
    
    if (err.name === 'ValidationError') {
      statusCode = 400;
      message = 'Validation failed';
      details = err.details;
    } else if (err.name === 'UnauthorizedError') {
      statusCode = 401;
      message = 'Unauthorized';
    } else if (err.name === 'ForbiddenError') {
      statusCode = 403;
      message = 'Forbidden';
    } else if (err.name === 'NotFoundError') {
      statusCode = 404;
      message = 'Resource not found';
    } else if (err.code === 'ENOENT') {
      statusCode = 404;
      message = 'File not found';
    } else if (err.code === 'EACCES') {
      statusCode = 403;
      message = 'Permission denied';
    } else if (err.code === 'EBUSY') {
      statusCode = 423;
      message = 'Resource is busy, please try again';
    }
    
    // Don't expose internal errors in production
    if (process.env.NODE_ENV === 'production' && statusCode === 500) {
      message = 'Internal server error';
      details = null;
    } else if (process.env.NODE_ENV !== 'production') {
      details = {
        stack: err.stack,
        code: err.code
      };
    }
    
    res.status(statusCode).json({
      success: false,
      error: message,
      details,
      timestamp: new Date().toISOString()
    });
  }
  
  static notFound(req, res, next) {
    const error = new Error(`Route not found: ${req.originalUrl}`);
    error.name = 'NotFoundError';
    next(error);
  }
  
  static asyncHandler(fn) {
    return (req, res, next) => {
      Promise.resolve(fn(req, res, next)).catch(next);
    };
  }
}

module.exports = ErrorHandler;