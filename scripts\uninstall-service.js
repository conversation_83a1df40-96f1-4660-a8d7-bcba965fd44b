const Service = require('node-windows').Service;
const path = require('path');

// Create a new service object
const svc = new Service({
  name: 'DL Organizer',
  script: path.join(__dirname, '..', 'backend', 'server.js')
});

// Listen for the "uninstall" event
svc.on('uninstall', () => {
  console.log('✅ DL Organizer service uninstalled successfully');
});

svc.on('error', (err) => {
  console.error('❌ Service error:', err);
});

// Check if running as administrator
function isAdmin() {
  try {
    require('child_process').execSync('net session', { stdio: 'ignore' });
    return true;
  } catch (error) {
    return false;
  }
}

if (!isAdmin()) {
  console.error('❌ This script must be run as Administrator');
  console.log('Right-click Command Prompt and select "Run as Administrator"');
  process.exit(1);
}

console.log('Uninstalling DL Organizer Windows Service...');
svc.uninstall();