@echo off
echo Starting DL Organizer Development Environment...

REM Add Bun to PATH
set PATH=C:\Users\<USER>\.bun\bin;%PATH%

echo Starting Frontend server on port 3030...
start "Frontend Server" cmd /k "bun x next dev -p 3030"

timeout /t 3 /nobreak >nul

echo Starting Backend server with bun...
start "Backend Server" cmd /k "bun run --hot backend/server.js"

echo.
echo Development servers are starting in separate windows...
echo Frontend: http://localhost:3030
echo Backend: Check port in Backend Server window
echo.
echo Both servers should be running in separate command windows.
echo Close this window or press Ctrl+C to continue.
timeout /t 5 /nobreak >nul