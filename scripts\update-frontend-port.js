#!/usr/bin/env node

/**
 * Update frontend port in package.json based on port-config.json
 * This ensures the frontend uses the correct dynamically assigned port
 */

const fs = require('fs');
const path = require('path');

function updateFrontendPort() {
    try {
        // Load port configuration
        const configPath = path.join(__dirname, '..', 'data', 'port-config.json');
        let frontendPort = '3030'; // default
        
        if (fs.existsSync(configPath)) {
            const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
            frontendPort = config.environment?.FRONTEND_PORT || config.ports?.frontend || '3030';
            console.log(`📋 Loading dynamic frontend port: ${frontendPort}`);
        } else {
            console.log(`📋 Using default frontend port: ${frontendPort}`);
        }
        
        // Update package.json
        const packagePath = path.join(__dirname, '..', 'package.json');
        const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
        
        // Update the dev:frontend script
        packageContent.scripts['dev:frontend'] = `next dev -p ${frontendPort}`;
        
        // Write back to package.json
        fs.writeFileSync(packagePath, JSON.stringify(packageContent, null, 2));
        console.log(`✅ Updated package.json frontend port to ${frontendPort}`);
        
        return frontendPort;
    } catch (error) {
        console.error('❌ Failed to update frontend port:', error.message);
        return '3030';
    }
}

// Run if called directly
if (require.main === module) {
    updateFrontendPort();
}

module.exports = { updateFrontendPort };