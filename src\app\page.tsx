"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useRef, useMemo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { ThemeToggle } from "@/components/theme-provider";
import ProjectOverview from "@/components/dl-organizer/project-overview";
import FolderTree from "@/components/dl-organizer/folder-tree";
import FolderOverview from "@/components/dl-organizer/folder-overview";
import EnhancedImageGrid from "@/components/dl-organizer/enhanced-image-grid";
import ProcessingModeTabs from "@/components/dl-organizer/processing-mode-tabs";
import TextEditor from "@/components/dl-organizer/text-editor";
import SettingsPanel from "@/components/dl-organizer/settings-panel";
import { CountryModeToggle } from "@/components/dl-organizer/country-mode-toggle";
import CostTrackerPanel from "@/components/dl-organizer/cost-tracker-panel";
import ImageRenamingPanel from "@/components/dl-organizer/image-renaming-panel";
import BatchOrganizationPanel from "@/components/dl-organizer/batch-organization-panel";
import OCRSaveSuccessDialog from "@/components/dl-organizer/ocr-save-success-dialog";
import {
  ArrowLeft,
  RefreshCw,
  AlertCircle,
  Grid,
  List,
  FileText,
  Bot,
  CheckSquare,
  Square,
  Play,
  DollarSign,
  Edit3,
  Archive,
  Cpu,
  CheckCircle,
  Trash2,
} from "lucide-react";
import Link from "next/link";
import { Project, FolderNode, ImageFile, OCRResult } from "@/types";
import { DLOrganizerStorageUtils } from "@/lib/storage-utils";
import { useSettingsStandalone } from "@/hooks/use-settings-sync";
import { useOCRMode } from "@/hooks/use-ocr-mode";
import { useFilterReducer } from "@/hooks/use-filter-reducer";
import { useSmartAnalyzer } from "@/hooks/useSmartAnalyzer";
import { dedupeAndThrottleRequest, dedupeFetch } from "@/utils/request-deduper";
import { safeStringify } from "@/utils/safe-json";
import { applyFilters, getFilterStats } from "@/utils/image-filtering";
import { cn } from "@/lib/utils";

export default function HomePage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [currentProject, setCurrentProject] = useState<Project | null>(null);
  const [folders, setFolders] = useState<FolderNode[]>([]);
  const [images, setImages] = useState<ImageFile[]>([]);
  const [selectedFolder, setSelectedFolder] = useState<FolderNode | null>(null);
  const [selectedImage, setSelectedImage] = useState<ImageFile | null>(null);
  const [ocrResult, setOcrResult] = useState<OCRResult | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [scanProgress, setScanProgress] = useState("");
  const [gridSize, setGridSize] = useState(200);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<"tree" | "overview">("tree");
  const [rightPanelMode, setRightPanelMode] = useState<
    "processing" | "text" | "cost" | "rename" | "organize"
  >("processing");
  const [batchSelectionMode, setBatchSelectionMode] = useState(false);
  const [selectedFolderIds, setSelectedFolderIds] = useState<string[]>([]);
  const [selectedBatchImages, setSelectedBatchImages] = useState<ImageFile[]>(
    []
  );
  const [multiSelectIds, setMultiSelectIds] = useState<Set<string>>(new Set());
  const [countryMode, setCountryMode] = useState<"us" | "australian">("us");
  const [lastOperationCost, setLastOperationCost] = useState<
    | {
        modelUsed: string;
        estimatedCost: number;
        currentLimits: any;
      }
    | undefined
  >(undefined);

  // Filter system state
  const { filters, addFilter, removeFilter, clearAllFilters } =
    useFilterReducer();
  const [showAllImages, setShowAllImages] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(100);

  // OCR Save Success Dialog state
  const [showSaveSuccessDialog, setShowSaveSuccessDialog] = useState(false);
  const [savedFiles, setSavedFiles] = useState<{ json?: string; txt?: string }>(
    {}
  );
  const [savedImageName, setSavedImageName] = useState("");

  // Settings hook for dynamic model display
  const {
    openRouterConfig,
    connectionStatus,
    testConnection: settingsTestConnection,
    isLoading,
    isSettingsLoaded,
  } = useSettingsStandalone();

  // OCR mode hook for document type selection
  const { documentType, isAustralianDocumentType, isUSDocumentType } =
    useOCRMode();

  // Smart Analyzer for generating filter chips from folder analysis
  const smartAnalyzer = useSmartAnalyzer(selectedFolder?.path);

  // Test connection state
  const [isTestingConnection, setIsTestingConnection] = useState(false);

  // Helper function to map document type to backend parameters
  const getBackendParameters = (docType: string) => {
    switch (docType) {
      case "us_driver_license":
        return { extractionType: "us_driver_license", mode: "us" };
      case "us_id_card":
        return { extractionType: "us_id_card", mode: "us" };
      case "us_passport":
        return { extractionType: "us_passport", mode: "us" };
      case "australian_driver_license":
        return {
          extractionType: "australian_driver_license",
          mode: "australian",
        };
      case "australian_id_card":
        return { extractionType: "australian_id_card", mode: "australian" };
      case "australian_passport":
        return { extractionType: "australian_passport", mode: "australian" };
      case "auto_detect":
      default:
        return { extractionType: "auto_detect", mode: countryMode };
    }
  };

  // Helper function to get current model display name
  const getCurrentModelDisplay = () => {
    const modelId = openRouterConfig.selectedModel;

    // Vision models mapping for display
    const visionModels = [
      {
        id: "google/gemini-flash-1.5",
        name: "Gemini Flash 1.5",
        provider: "Google",
        tier: "free",
      },
      {
        id: "qwen/qwen-2-vl-7b-instruct",
        name: "Qwen2-VL 7B",
        provider: "Qwen",
        tier: "free",
      },
      {
        id: "meta-llama/llama-3.2-11b-vision-instruct",
        name: "Llama 3.2 11B Vision",
        provider: "Meta",
        tier: "free",
      },
      {
        id: "microsoft/phi-3.5-vision-instruct",
        name: "Phi-3.5 Vision",
        provider: "Microsoft",
        tier: "free",
      },
      { id: "openai/gpt-4o", name: "GPT-4o", provider: "OpenAI", tier: "paid" },
      {
        id: "openai/gpt-4o-mini",
        name: "GPT-4o Mini",
        provider: "OpenAI",
        tier: "paid",
      },
      {
        id: "anthropic/claude-3.5-sonnet",
        name: "Claude 3.5 Sonnet",
        provider: "Anthropic",
        tier: "paid",
      },
      {
        id: "google/gemini-pro-1.5",
        name: "Gemini Pro 1.5",
        provider: "Google",
        tier: "paid",
      },
      {
        id: "anthropic/claude-3-5-haiku",
        name: "Claude 3.5 Haiku",
        provider: "Anthropic",
        tier: "paid",
      },
    ];

    const model = visionModels.find((m) => m.id === modelId);
    if (model) {
      return {
        name: model.name,
        provider: model.provider,
        tier: model.tier,
        isCustom: false,
      };
    }

    // Handle custom model
    if (
      openRouterConfig.customModel &&
      modelId === openRouterConfig.customModel
    ) {
      return {
        name: modelId.split("/").pop() || modelId,
        provider: modelId.split("/")[0] || "Custom",
        tier: "paid",
        isCustom: true,
      };
    }

    // Fallback for unknown models
    return {
      name: modelId.split("/").pop() || modelId,
      provider: modelId.split("/")[0] || "Unknown",
      tier: "paid",
      isCustom: false,
    };
  };

  // Test connection function
  const testConnection = async () => {
    setIsTestingConnection(true);
    try {
      await settingsTestConnection();
    } catch (error) {
      console.error("Connection test error:", error);
    } finally {
      setIsTestingConnection(false);
    }
  };

  // Load projects from storage and API on mount
  useEffect(() => {
    const loadProjects = async () => {
      // First load from localStorage for immediate display
      const storedProjects = localStorage.getItem("dl-organizer-projects-v1");
      let localProjects: Project[] = [];

      if (storedProjects) {
        localProjects = JSON.parse(storedProjects);
        setProjects(localProjects);

        // Load the most recent project and automatically scan it
        if (localProjects.length > 0) {
          const lastProject = localProjects[localProjects.length - 1];
          setCurrentProject(lastProject);

          // Automatically scan the project folders on load
          scanProjectFolders(lastProject);
        }
      }

      // Then load from API and merge
      try {
        const response = await fetch("/api/projects");
        if (response.ok) {
          const apiData = await response.json();
          if (apiData.success && apiData.data) {
            const apiProjects: Project[] = apiData.data.map((project: any) => ({
              id: project.id,
              name: project.name,
              rootPath: project.root_path,
              createdAt: project.created_at,
              updatedAt: project.updated_at,
            }));

            // Merge API projects with local projects (API takes precedence)
            const projectMap = new Map();

            // Add local projects first
            localProjects.forEach((project) =>
              projectMap.set(project.id, project)
            );

            // Override with API projects (newer/authoritative)
            apiProjects.forEach((project) =>
              projectMap.set(project.id, project)
            );

            const mergedProjects = Array.from(projectMap.values());
            setProjects(mergedProjects);

            // If no current project set, use the most recent and scan it
            if (!currentProject && mergedProjects.length > 0) {
              const lastProject = mergedProjects[mergedProjects.length - 1];
              setCurrentProject(lastProject);

              // Automatically scan the project folders
              scanProjectFolders(lastProject);
            }
          }
        }
      } catch (error) {
        console.error("Failed to load projects from API:", error);
        // Continue with localStorage projects if API fails
      }
    };

    loadProjects();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Save projects to storage whenever they change
  useEffect(() => {
    if (projects.length > 0) {
      localStorage.setItem("dl-organizer-projects-v1", safeStringify(projects));
    }
  }, [projects]);

  // Clear success message after 5 seconds
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  const handleProjectCreate = async (name: string, rootPath: string) => {
    try {
      // Create project via API
      const response = await fetch("/api/projects", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          rootPath,
          description: "",
          settings: {
            defaultAIProvider: "openai",
            autoSaveInterval: 30000,
            thumbnailSize: 150,
            enableAutoBackup: true,
            ocrOutputFormat: "structured",
          },
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create project");
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || "Failed to create project");
      }

      // Map backend project to frontend format
      const newProject: Project = {
        id: result.data.id,
        name: result.data.name,
        rootPath: result.data.root_path,
        createdAt: new Date(result.data.created_at),
        updatedAt: new Date(result.data.updated_at),
        settings: {
          defaultAIProvider: "openai",
          autoSaveInterval: 30000,
          thumbnailSize: 150,
          enableAutoBackup: true,
          ocrOutputFormat: "structured",
        },
      };

      setProjects((prev) => [...prev, newProject]);
      setCurrentProject(newProject);

      // Create backup
      const backupData = {
        projects: [...projects, newProject],
        folders: [],
        images: [],
        ocrResults: [],
        settings: newProject.settings,
        metadata: {
          exportDate: new Date().toISOString(),
          version: "1.0",
          totalProjects: projects.length + 1,
          totalFolders: 0,
          totalImages: 0,
        },
      };
      DLOrganizerStorageUtils.createBackup(backupData);

      // Automatically scan the project folder
      scanProjectFolders(newProject);
    } catch (error) {
      console.error("Error creating project:", error);
      setError(
        error instanceof Error ? error.message : "Failed to create project"
      );
    }
  };

  const handleProjectSelect = (project: Project) => {
    setCurrentProject(project);
    scanProjectFolders(project);
  };

  const scanProjectFolders = async (project: Project) => {
    setIsScanning(true);
    setError(null);
    setScanProgress("Initializing scan...");

    try {
      // Validate path before scanning
      const isLargeDirectory =
        project.rootPath.length <= 3 ||
        project.rootPath.match(/^[A-Z]:\\?$/) ||
        project.rootPath.includes("Program Files") ||
        project.rootPath.includes("Windows");

      if (isLargeDirectory) {
        setScanProgress("Large directory detected - using optimized scan...");
      } else {
        setScanProgress("Validating path...");
      }

      const controller = new AbortController();
      const timeoutId = setTimeout(
        () => controller.abort(),
        isLargeDirectory ? 120000 : 30000
      ); // 2 min for large dirs, 30s for others

      setScanProgress(`Scanning ${project.rootPath}...`);

      // Use deduplication to prevent multiple simultaneous scans of the same path
      const scanKey = `filesystem-scan-${project.rootPath}`;

      const response = await dedupeFetch(scanKey, async () => {
        return fetch("/api/filesystem/scan", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          signal: controller.signal,
          body: JSON.stringify({
            rootPath: project.rootPath,
            maxDepth: isLargeDirectory ? 2 : 3, // Reduce depth for large directories
            includeStats: true,
          }),
        });
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage =
          errorData.details ||
          errorData.error ||
          "Failed to scan project folders";
        throw new Error(
          `Scan failed: ${
            typeof errorMessage === "object"
              ? JSON.stringify(errorMessage)
              : errorMessage
          }`
        );
      }

      setScanProgress("Processing scan results...");
      const data = await response.json();

      setScanProgress(`Found ${data.folders?.length || 0} folders`);
      setFolders(data.folders);

      // Save to localStorage for future use
      localStorage.setItem(
        `dl-organizer-folders-${project.id}`,
        safeStringify(data.folders)
      );

      setScanProgress("Scan completed successfully!");
    } catch (error) {
      let errorMessage = "Failed to scan folders";

      if (error instanceof Error) {
        if (error.name === "AbortError") {
          errorMessage =
            "Scan timeout: Directory too large. Try selecting a smaller folder or increase timeout.";
        } else if (error.message.includes("network")) {
          errorMessage = "Network error: Check your connection and try again.";
        } else if (
          error.message.includes("permission") ||
          error.message.includes("access")
        ) {
          errorMessage =
            "Permission denied: You may not have access to this folder.";
        } else if (
          error.message.includes("not found") ||
          error.message.includes("not exist")
        ) {
          errorMessage =
            "Path not found: The selected folder may have been moved or deleted.";
        } else {
          errorMessage = error.message;
        }
      }

      setError(errorMessage);
      console.error("Folder scanning error:", error);

      // Add user guidance for common issues
      if (errorMessage.includes("timeout")) {
        console.info(
          "💡 Tip: Try selecting a more specific subdirectory instead of scanning entire drives"
        );
      }
    } finally {
      setIsScanning(false);
      // Clear progress message after a delay
      setTimeout(() => setScanProgress(""), 2000);
    }
  };

  const handleProjectEdit = (
    project: Project,
    name: string,
    rootPath: string
  ) => {
    const updatedProject = {
      ...project,
      name,
      rootPath,
      updatedAt: new Date(),
    };

    setProjects((prev) =>
      prev.map((p) => (p.id === project.id ? updatedProject : p))
    );

    // If this is the current project, update it and rescan if path changed
    if (currentProject?.id === project.id) {
      setCurrentProject(updatedProject);
      if (rootPath !== project.rootPath) {
        // Path changed, reset state and rescan
        setFolders([]);
        setImages([]);
        setSelectedFolder(null);
        setSelectedImage(null);
        setOcrResult(null);
        setError(null);
        scanProjectFolders(updatedProject);
      }
    }
  };

  const handleProjectDelete = (projectId: string) => {
    setProjects((prev) => prev.filter((p) => p.id !== projectId));

    // Clean up localStorage for the deleted project
    localStorage.removeItem(`dl-organizer-folders-${projectId}`);
    localStorage.removeItem(`dl-organizer-images-${projectId}`);

    if (currentProject?.id === projectId) {
      setCurrentProject(null);
      setFolders([]);
      setImages([]);
      setSelectedFolder(null);
      setSelectedImage(null);
      setOcrResult(null);
      setError(null);
    }
  };

  const handleProjectUpdate = (
    projectId: string,
    updates: Partial<Project>
  ) => {
    setProjects((prev) =>
      prev.map((p) =>
        p.id === projectId ? { ...p, ...updates, updatedAt: new Date() } : p
      )
    );
  };

  const loadProjectData = (projectId: string) => {
    // Load folders and images for the project
    const storedFolders = localStorage.getItem(
      `dl-organizer-folders-${projectId}`
    );
    const storedImages = localStorage.getItem(
      `dl-organizer-images-${projectId}`
    );

    if (storedFolders) {
      setFolders(JSON.parse(storedFolders));
    } else {
      setFolders([]);
    }

    if (storedImages) {
      setImages(JSON.parse(storedImages));
    } else {
      setImages([]);
    }
  };

  const handleFolderSelect = async (folder: FolderNode) => {
    setSelectedFolder(folder);
    setImages([]);
    setSelectedImage(null);
    setOcrResult(null);

    // Load images for the selected folder
    const requestBody = {
      folderPath: folder.path,
      recursive: true, // Always fetch recursively to show all images
      generateThumbnails: true,
    };

    try {
      const response = await fetch("/api/filesystem/images", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error("Failed to load folder images");
      }

      const data = await response.json();

      // Convert to ImageFile format
      const imageFiles: ImageFile[] = data.images.map((img: any) => ({
        id: img.id,
        filename: img.filename,
        path: img.path,
        relativePath: img.relativePath || img.filename,
        thumbnailUrl: img.thumbnailUrl
          ? `/api${img.thumbnailUrl}`
          : `/api/thumbnails/${img.id}.jpg`,
        previewUrl: img.previewUrl
          ? `/api${img.previewUrl}`
          : `/api/previews/${img.id}.jpg`,
        rotation: img.rotation || 0,
        lastModified: new Date(img.lastModified),
        fileSize: img.fileSize || 0,
        width: img.width || 0,
        height: img.height || 0,
        folderId: folder.id,
      }));

      setImages(imageFiles);
    } catch (error) {
      console.error("Error loading folder images:", error);
      setError("Failed to load images from folder");
    }
  };

  const handleFolderExpand = (folderId: string) => {
    const updateFolderRecursively = (folders: FolderNode[]): FolderNode[] => {
      return folders.map((folder) => {
        if (folder.id === folderId) {
          return { ...folder, isExpanded: !folder.isExpanded };
        }
        if (folder.children.length > 0) {
          return {
            ...folder,
            children: updateFolderRecursively(folder.children),
          };
        }
        return folder;
      });
    };

    setFolders((prev) => updateFolderRecursively(prev));
  };

  const handleFolderTag = (folderId: string, tags: string[]) => {
    const updateFolderTagsRecursively = (
      folders: FolderNode[]
    ): FolderNode[] => {
      return folders.map((folder) => {
        if (folder.id === folderId) {
          return { ...folder, tags };
        }
        if (folder.children.length > 0) {
          return {
            ...folder,
            children: updateFolderTagsRecursively(folder.children),
          };
        }
        return folder;
      });
    };

    setFolders((prev) => updateFolderTagsRecursively(prev));
  };

  const handleFolderRename = async (folderId: string, newName: string) => {
    try {
      // Find the folder to rename
      const folder = folders.find((f) => f.id === folderId);
      if (!folder) return;

      // Call the API to rename the folder
      const response = await fetch("/api/filesystem/rename-folder", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          oldPath: folder.path,
          newName: newName,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to rename folder");
      }

      const data = await response.json();

      // Update the folder in the state
      setFolders((prev) =>
        prev.map((f) =>
          f.id === folderId ? { ...f, name: newName, path: data.newPath } : f
        )
      );
    } catch (error) {
      console.error("Error renaming folder:", error);
      setError("Failed to rename folder");
    }
  };

  const handleImageSelect = async (image: ImageFile) => {
    setSelectedImage(image);

    // Check for cached OCR results BEFORE clearing current results
    let cacheLoaded = false;
    try {
      // Use image.id which is already base64-encoded path from backend
      const encodedPath = image.id;

      // Get current OCR parameters for cache lookup
      const backendParams = getBackendParameters(documentType);

      // Wait for settings to be loaded before using selectedModel for cache key generation
      // This prevents using the default model ID when settings are still loading
      let effectiveModelId = openRouterConfig.selectedModel;

      // Check if settings are actually loaded - if not, wait briefly for them to load
      if (!isLoading && !isSettingsLoaded) {
        console.log(`🔧 Settings not yet loaded, waiting briefly...`);
        const maxWaitTime = 1500; // 1.5 seconds max wait
        const waitStart = Date.now();

        // Wait for settings to load or timeout
        while (Date.now() - waitStart < maxWaitTime && !isSettingsLoaded) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }

        effectiveModelId = openRouterConfig.selectedModel;
        console.log(
          `🔧 Settings load wait completed. Loaded: ${isSettingsLoaded}, Using model: ${effectiveModelId}`
        );
      }

      const cacheParams = {
        imagePath: encodedPath,
        extractionType: backendParams.extractionType,
        mode: backendParams.mode,
        modelId: effectiveModelId, // Use the effective model ID after settings are loaded
        cardSide: null, // Will be determined during OCR
      };

      console.log(
        "🔍 Checking for cached OCR results with parameters:",
        cacheParams
      );

      const response = await fetch("/api/ocr/cache/check", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(cacheParams),
      });

      if (response.ok) {
        const cacheInfo = await response.json();
        console.log("💾 Cache check result:", cacheInfo);

        if (cacheInfo.exists) {
          // Load cached results with same parameters
          const loadResponse = await fetch("/api/ocr/cache/load", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(cacheParams),
          });

          if (loadResponse.ok) {
            const cachedResult = await loadResponse.json();
            console.log("✅ Cached result loaded:", {
              success: cachedResult.success,
              fallback: cachedResult.fallback,
              parameters: cachedResult.parameters,
            });

            if (cachedResult.success) {
              setOcrResult(cachedResult.result);
              cacheLoaded = true;

              // Show cache status to user
              if (cachedResult.fallback) {
                if (cachedResult.modeChanged) {
                  console.log(
                    `🔄 Using ${cachedResult.usedMode} mode cache (requested ${cachedResult.originalMode} mode)`
                  );
                  // Show user-friendly message about mode fallback
                  if (
                    cachedResult.usedMode === "australian" &&
                    cachedResult.originalMode === "us"
                  ) {
                    console.log(
                      "💡 Tip: This appears to be an Australian license, consider switching to Australian mode for better accuracy"
                    );
                  } else if (
                    cachedResult.usedMode === "us" &&
                    cachedResult.originalMode === "australian"
                  ) {
                    console.log(
                      "💡 Tip: This appears to be a US license, consider switching to US mode for better accuracy"
                    );
                  }
                } else {
                  console.log("📄 Using fallback cache (different parameters)");
                }
              } else {
                console.log("💾 Using exact parameter match cache");
              }
            }
          } else {
            // Handle cache load errors with better error parsing
            let errorMessage = `HTTP ${loadResponse.status}: ${loadResponse.statusText}`;
            try {
              const errorData = await loadResponse.json();
              errorMessage = errorData.error || errorMessage;
            } catch (parseError) {
              console.warn(
                "Cache load: Failed to parse error response:",
                parseError
              );
            }
            console.warn("❌ Failed to load cached results:", errorMessage);
          }
        } else {
          console.log("🆕 No cached results found - will run fresh OCR");
        }
      } else {
        // Handle non-OK responses with better error parsing
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch (parseError) {
          // If JSON parsing fails, use the HTTP status message
          console.warn(
            "Cache check: Failed to parse error response:",
            parseError
          );
        }
        console.warn("⚠️ Cache check request failed:", errorMessage);
      }
    } catch (error) {
      console.error("Error checking for cached OCR results:", error);
      // Continue without cache - not a critical error
    }

    // Only clear OCR results if no cached results were loaded
    if (!cacheLoaded) {
      console.log(
        "🧹 No cache available - clearing OCR results for fresh analysis"
      );
      setOcrResult(null);
    }
  };

  const handleImageRotate = async (imageId: string, degrees: number) => {
    try {
      // Find the image to get its path
      const image = images.find((img) => img.id === imageId);
      if (!image) {
        throw new Error("Image not found");
      }

      // Use the pre-computed base64 id for the path (already unique)

      // Call the backend API to rotate the actual image file (correct route is /api/images)
      const response = await fetch(
        `/api/images/${encodeURIComponent(image.id)}/rotate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ degrees }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to rotate image");
      }

      const result = await response.json();

      if (result.success && result.image) {
        const { thumbnailUrl, previewUrl, lastModified } = result.image;

        // Build relative URLs that go through Next.js API routes
        const absThumb = `/api${thumbnailUrl}`;
        const absPrev = `/api${previewUrl}`;

        // Unique cache-buster so the browser grabs the new file
        const uniqueTimestamp = `${Date.now()}-${Math.random()
          .toString(36)
          .substr(2, 9)}`;

        const updatedImages = images.map((img) =>
          img.id === imageId
            ? {
                ...img,
                lastModified: new Date(lastModified),
                rotation: ((img.rotation || 0) + degrees) % 360,
                thumbnailUrl: `${absThumb}?t=${uniqueTimestamp}`,
                previewUrl: `${absPrev}?t=${uniqueTimestamp}`,
              }
            : img
        );
        setImages(updatedImages);

        if (selectedImage?.id === imageId) {
          setSelectedImage({
            ...selectedImage,
            lastModified: new Date(lastModified),
            rotation: ((selectedImage.rotation || 0) + degrees) % 360,
            thumbnailUrl: `${absThumb}?t=${uniqueTimestamp}`,
            previewUrl: `${absPrev}?t=${uniqueTimestamp}`,
          });
        }

        // Allow backend a moment to finish disk writes then force re-render
        setTimeout(() => setImages((prev) => [...prev]), 800);
      } else {
        throw new Error(result.error || "Failed to rotate image");
      }
    } catch (error) {
      console.error("Error rotating image:", error);
      setError(
        error instanceof Error ? error.message : "Failed to rotate image"
      );
    }
  };

  const handleOCRAnalyze = async (
    imageId: string,
    modelId?: string,
    forceRefresh: boolean = false
  ) => {
    setIsProcessing(true);
    setError(null);

    try {
      console.log("OCR Analysis starting:", {
        imageId: imageId ? "provided" : "missing",
        modelId,
        countryMode,
        forceRefresh,
      });

      // Get the selected image
      const image = images.find((img) => img.id === imageId);
      if (!image) {
        console.error(
          "OCR Analysis: Image not found in images array:",
          imageId
        );
        throw new Error("Image not found");
      }

      console.log("OCR Analysis: Found image:", {
        filename: image.filename,
        path: image.path,
        imageIdLength: imageId.length,
      });

      // Get backend parameters based on selected document type
      const backendParams = getBackendParameters(documentType);

      // Use the new analyze-by-path endpoint
      const requestBody = {
        imagePath: imageId, // imageId is already base64 encoded path
        modelId: modelId || "gpt-4o-mini",
        extractionType: backendParams.extractionType,
        mode: backendParams.mode,
        cardSide: null,
        forceRefresh: forceRefresh,
      };

      console.log("OCR Analysis: Making API request with:", requestBody);

      const response = await fetch("/api/ocr/analyze-by-path", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: safeStringify(requestBody),
      });

      console.log(
        "OCR Analysis: API response status:",
        response.status,
        response.statusText
      );

      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
        } catch (parseError) {
          console.error(
            "OCR Analysis: Failed to parse error response:",
            parseError
          );
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        console.error("OCR Analysis: API error:", errorData);
        // Use specific error details when available
        let errorMessage =
          errorData.error || `OCR analysis failed (${response.status})`;
        if (errorData.details && errorData.specificError) {
          errorMessage = errorData.details;
        }
        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log("OCR Analysis: API success response received:", {
        success: data.success,
        modelUsed: data.modelUsed,
      });

      if (!data.success) {
        console.error("OCR Analysis: Service returned failure:", data.error);
        // Provide more specific error message for model-related issues
        let errorMessage = data.error || "OCR analysis failed";
        if (data.modelRequested && data.modelRequested !== "auto") {
          errorMessage = `Selected model "${data.modelRequested}" failed: ${errorMessage}`;
        }
        throw new Error(errorMessage);
      }

      // Check for incomplete response (success but missing model info)
      if (data.success && !data.modelUsed) {
        console.warn(
          "OCR Analysis: Response marked as success but missing model information"
        );
        console.warn(
          "OCR Analysis: This suggests an incomplete response from the backend"
        );

        // Still proceed if we have result data, but warn the user
        if (!data.result || !data.result.rawText) {
          throw new Error(
            "OCR analysis incomplete: No model information or results returned"
          );
        }
      }

      // Update cost tracking information
      if (data.costInfo) {
        setLastOperationCost({
          modelUsed: data.costInfo.modelUsed,
          estimatedCost: data.costInfo.estimatedCost,
          currentLimits: data.costInfo.currentLimits,
        });
      }

      // Create OCR result with AI-populated fields and cache info
      const ocrResult: OCRResult = {
        id: data.result?.id || crypto.randomUUID(),
        imageId: image.id, // Use the actual image ID from the images array
        folderId: selectedFolder?.id || "",
        firstName: data.result?.firstName || "",
        middleName: data.result?.middleName || "", // Added middleName support
        lastName: data.result?.lastName || "",
        dateOfBirth: data.result?.dateOfBirth || "",
        address: data.result?.address || "",
        licenseNumber: data.result?.licenseNumber || "",
        cardNumber: data.result?.cardNumber || "", // Added cardNumber support for Australian licenses
        expirationDate: data.result?.expirationDate || "",
        issueDate: data.result?.issueDate || "",
        state: data.result?.state || "",
        confidence: data.result?.confidence || 0,
        rawText: data.result?.rawText || "",
        processedAt: data.result?.processedAt
          ? new Date(data.result.processedAt)
          : new Date(),
        mode: data.result?.mode || countryMode,
        cached: data.cached || false,
        cacheTimestamp: data.cacheTimestamp || null,
      };

      setOcrResult(ocrResult);

      // Log success
      console.log("OCR analysis completed successfully:", {
        model: data.modelUsed,
        cost: data.cost,
        processingTime: data.processingTime,
        costInfo: data.costInfo,
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "OCR analysis failed";
      console.error("OCR Analysis: Final error caught:", error);
      console.error("OCR Analysis: Error message set:", errorMessage);
      setError(errorMessage);
    } finally {
      console.log("OCR Analysis: Processing completed");
      setIsProcessing(false);
    }
  };

  const handleOCRSave = async (data: OCRResult) => {
    try {
      setIsProcessing(true);
      setError(null);
      setSuccessMessage(null);

      // Save OCR result to localStorage
      const storedResults = localStorage.getItem("dl-organizer-ocr-results-v1");
      const results = storedResults ? JSON.parse(storedResults) : [];
      results.push(data);
      localStorage.setItem(
        "dl-organizer-ocr-results-v1",
        safeStringify(results)
      );

      // Get the current image to determine file path
      // Use selectedImage first, then fall back to searching by imageId
      console.log("🔍 Saving OCR - Debug info:", {
        hasSelectedImage: !!selectedImage,
        selectedImageId: selectedImage?.id,
        dataImageId: data.imageId,
        imagesCount: images.length,
        imageIds: images.map((img) => img.id).slice(0, 3), // Show first 3 for debugging
      });

      const image =
        selectedImage || images.find((img) => img.id === data.imageId);
      if (image) {
        // Save .txt and .json files using the OCR service
        try {
          console.log("Saving OCR results to files...");
          const response = await fetch("/api/ocr/save-results", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              ocrResult: data,
              imagePath: image.path,
              formats: ["txt", "json"],
            }),
          });

          if (response.ok) {
            const result = await response.json();
            console.log(
              "✅ OCR results saved to files successfully:",
              result.savedFiles
            );

            // Clear any previous errors and show success message
            setError(null);

            // Show success dialog with saved files
            if (result.savedFiles && typeof result.savedFiles === "object") {
              setSavedFiles(result.savedFiles);
              setSavedImageName(image.filename || "Unknown Image");
              setShowSaveSuccessDialog(true);
            }
          } else {
            const errorData = await response
              .json()
              .catch(() => ({ error: "Unknown server error" }));
            console.error("❌ Failed to save OCR results:", errorData);
            setError(
              `Failed to save OCR results: ${
                errorData.error || "Unknown error"
              }`
            );
            setIsProcessing(false);
            return; // Don't close the panel if saving failed
          }
        } catch (saveError) {
          console.error("❌ Network error saving OCR results:", saveError);
          const errorMessage =
            saveError instanceof Error
              ? saveError.message
              : "Unknown network error";
          setError(`Network error saving OCR results: ${errorMessage}`);
          setIsProcessing(false);
          return; // Don't close the panel if saving failed
        }
      } else {
        console.error("❌ No image found for OCR result");
        setError("Cannot save: Image not found");
        setIsProcessing(false);
        return;
      }

      // Don't clear the results immediately to prevent UI flashing
      // Keep the OCR result and selected image for better UX
      setError(null);

      // No need to refresh folder - just show success message
      // The saved files don't affect the image display and refreshing causes focus loss
    } catch (error) {
      console.error("❌ Unexpected error saving OCR results:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      setError(`Failed to save OCR results: ${errorMessage}`);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleOCRCancel = () => {
    setOcrResult(null);
  };

  // Cache management handlers
  const handleClearCache = async (imageId?: string) => {
    try {
      if (imageId) {
        // Clear cache for specific image
        const image = images.find((img) => img.id === imageId);
        if (image) {
          const encodedPath = btoa(image.path);
          const response = await fetch("/api/ocr/cache/delete", {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ imagePath: encodedPath }),
          });

          if (response.ok) {
            console.log("Cache cleared for:", image.filename);
            // Clear OCR result if it was from the cleared image
            if (selectedImage?.id === imageId) {
              setOcrResult(null);
            }
          }
        }
      } else {
        // Clear cache for entire directory
        if (selectedFolder) {
          const encodedPath = btoa(selectedFolder.path);
          const response = await fetch("/api/ocr/cache/clear-directory", {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ directoryPath: encodedPath }),
          });

          if (response.ok) {
            const result = await response.json();
            console.log(
              `Cleared cache for ${result.deletedCount} files in directory`
            );
            // Clear current OCR result as it might be from cleared cache
            setOcrResult(null);
          }
        }
      }
    } catch (error) {
      console.error("Error clearing cache:", error);
    }
  };

  const handleBatchSelectionToggle = () => {
    setBatchSelectionMode(!batchSelectionMode);
    if (batchSelectionMode) {
      setSelectedFolderIds([]);
      setSelectedBatchImages([]);
      setMultiSelectIds(new Set());
    }
  };

  const handleBatchImageSelectionChange = useCallback(
    (selectedImages: ImageFile[]) => {
      setSelectedBatchImages(selectedImages);
    },
    []
  );

  // Cache status map for filter processing
  const hasCacheMap = useMemo(() => {
    const map = new Map<string, boolean>();
    // This will be populated by the EnhancedImageGrid component
    // For now, return empty map - will be enhanced when cache checking is integrated
    return map;
  }, []);

  // Side cache from Smart Analyzer manifest for side-based filtering
  const sideCache = useMemo(() => {
    const cache = new Map<string, "front" | "back" | "selfie" | "unknown">();

    if (smartAnalyzer.manifest && smartAnalyzer.manifest.imageSides) {
      Object.entries(smartAnalyzer.manifest.imageSides).forEach(
        ([id, side]) => {
          cache.set(id, side as "front" | "back" | "selfie" | "unknown");
        }
      );
    }

    return cache;
  }, [smartAnalyzer.manifest]);

  // Filtered and paginated images
  const filteredImages = useMemo(() => {
    return applyFilters(images, filters, hasCacheMap, sideCache);
  }, [images, filters, hasCacheMap, sideCache]);

  const paginatedImages = useMemo(() => {
    if (showAllImages) return filteredImages;

    const startIndex = (currentPage - 1) * pageSize;
    return filteredImages.slice(startIndex, startIndex + pageSize);
  }, [filteredImages, currentPage, pageSize, showAllImages]);

  const totalPages = Math.ceil(filteredImages.length / pageSize);

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  // Optimized multi-select with useRef to avoid unnecessary re-renders
  const multiSelectIdsRef = useRef<Set<string>>(new Set());

  const handleMultiSelect = useCallback(
    (ids: Set<string>) => {
      // Update the ref directly for immediate access
      multiSelectIdsRef.current = new Set(ids);

      // Only trigger React state update when count changes
      setMultiSelectIds(new Set(ids));

      // Also update selectedBatchImages for batch processing compatibility
      const selectedImages = images.filter((img) => ids.has(img.id));
      setSelectedBatchImages(selectedImages);
    },
    [images]
  );

  const handleBatchSelectFolders = (folderIds: string[]) => {
    setSelectedFolderIds(folderIds);
  };

  const handleBatchOCRProcess = async () => {
    if (selectedFolderIds.length === 0) {
      setError("Please select at least one folder for batch processing");
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      const response = await fetch("/api/ocr/batch-process", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          folderIds: selectedFolderIds,
          mode: "auto-detect",
          exportFormats: ["json", "txt"],
          includeSelfiDescription: true,
        }),
      });

      if (!response.ok) {
        throw new Error("Batch OCR processing failed");
      }

      const data = await response.json();

      // Clear selection after successful processing
      setSelectedFolderIds([]);
      setBatchSelectionMode(false);

      // Refresh the project folders to reflect new text files
      if (currentProject) {
        scanProjectFolders(currentProject);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Batch OCR processing failed";
      setError(errorMessage);
      console.error("Batch OCR processing error:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBatchImageOCRProcess = async () => {
    if (selectedBatchImages.length === 0) {
      setError("Please select at least one image for batch processing");
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // Get backend parameters based on selected document type
      const backendParams = getBackendParameters(documentType);

      const response = await fetch("/api/batch-ocr/process-images", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          images: selectedBatchImages.map((img) => ({
            id: img.id,
            path: img.path,
            filename: img.filename,
          })),
          modelId: openRouterConfig.selectedModel || "gpt-4o-mini",
          extractionType: backendParams.extractionType,
          mode: backendParams.mode,
          exportFormats: ["json", "txt"],
        }),
      });

      if (!response.ok) {
        throw new Error("Batch image OCR processing failed");
      }

      const data = await response.json();

      // Clear selection after successful processing
      setSelectedBatchImages([]);
      setBatchSelectionMode(false);

      // Refresh the current folder to reflect new text files
      if (selectedFolder) {
        await handleFolderSelect(selectedFolder);
      }

      setSuccessMessage(
        `Successfully processed ${
          data.successCount || selectedBatchImages.length
        } images`
      );
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Batch image OCR processing failed";
      setError(errorMessage);
      console.error("Batch image OCR processing error:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBackupExport = () => {
    const exportData = DLOrganizerStorageUtils.exportData();
    if (exportData) {
      const blob = new Blob([exportData], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `dl-organizer-backup-${
        new Date().toISOString().split("T")[0]
      }.json`;
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  const handleBackupImport = (data: string) => {
    const success = DLOrganizerStorageUtils.importData(data);
    if (success) {
      // Reload projects after import
      const storedProjects = localStorage.getItem("dl-organizer-projects-v1");
      if (storedProjects) {
        setProjects(JSON.parse(storedProjects));
      }
    }
  };

  // Image renaming handlers
  const handleImageRename = async (
    imageId: string,
    newName: string
  ): Promise<boolean> => {
    try {
      const response = await fetch("/api/images/rename", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ imageId, newName }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        setError(errorData.error || "Failed to rename image");
        return false;
      }

      const result = await response.json();

      // Update images list with new filename and ID
      setImages((prev) =>
        prev.map((img) =>
          img.id === imageId
            ? {
                ...img,
                id: result.newImageId,
                filename: result.newFilename,
                path: result.newPath,
              }
            : img
        )
      );

      // Update selected image if it was renamed
      if (selectedImage?.id === imageId) {
        setSelectedImage((prev) =>
          prev
            ? {
                ...prev,
                id: result.newImageId,
                filename: result.newFilename,
                path: result.newPath,
              }
            : null
        );
      }

      return true;
    } catch (error) {
      console.error("Error renaming image:", error);
      setError("Failed to rename image");
      return false;
    }
  };

  const handleBatchImageRename = async (
    renamingPairs: Array<{ imageId: string; newName: string }>
  ): Promise<boolean> => {
    try {
      const response = await fetch("/api/images/batch-rename", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ renamingPairs }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        setError(errorData.error || "Failed to rename images");
        return false;
      }

      const result = await response.json();

      // Update images list with new filenames and IDs
      setImages((prev) =>
        prev.map((img) => {
          const renamedResult = result.results.find(
            (r: any) => r.originalImageId === img.id
          );
          if (renamedResult && renamedResult.success) {
            return {
              ...img,
              id: renamedResult.newImageId,
              filename: renamedResult.newFilename,
              path: renamedResult.newPath,
            };
          }
          return img;
        })
      );

      // Clear selected image if it was renamed
      if (
        selectedImage &&
        result.results.some((r: any) => r.originalImageId === selectedImage.id)
      ) {
        setSelectedImage(null);
      }

      if (result.errors.length > 0) {
        setError(
          `${result.stats.successCount} images renamed, ${result.stats.errorCount} failed`
        );
      }

      return result.stats.errorCount === 0;
    } catch (error) {
      console.error("Error in batch rename:", error);
      setError("Failed to rename images");
      return false;
    }
  };

  // Batch organization handlers
  const handleBatchOrganization = async (
    organizationPlan: any
  ): Promise<boolean> => {
    try {
      const response = await fetch("/api/batch-organization/organize", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ organizationPlan }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        setError(errorData.error || "Failed to organize images");
        return false;
      }

      const result = await response.json();

      // Refresh the current folder to reflect the changes
      if (selectedFolder) {
        await handleFolderSelect(selectedFolder);
      }

      // Also refresh the project folders to show new folder structure
      if (currentProject) {
        await scanProjectFolders(currentProject);
      }

      if (result.errors.length > 0) {
        setError(
          `${result.summary.successCount} images organized, ${result.summary.errorCount} failed`
        );
      }

      return result.summary.errorCount === 0;
    } catch (error) {
      console.error("Error in batch organization:", error);
      setError("Failed to organize images");
      return false;
    }
  };

  if (!currentProject) {
    return (
      <div className="container mx-auto p-6">
        <ProjectOverview
          projects={projects}
          onProjectSelect={handleProjectSelect}
          onProjectCreate={handleProjectCreate}
          onProjectEdit={handleProjectEdit}
          onProjectDelete={handleProjectDelete}
          onBackToProjects={() => setCurrentProject(null)}
        />
      </div>
    );
  }

  return (
    <div className="w-full p-4 h-screen max-w-none">
      <div className="mb-4">
        <div className="flex flex-col gap-4">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h1 className="text-2xl font-bold truncate">
                {currentProject.name}
              </h1>
              <p className="text-sm text-muted-foreground break-all">
                Driver&apos;s License OCR Processing - {currentProject.rootPath}
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Link href="/ocr-testing">
                <Button variant="outline" size="sm">
                  <Bot className="h-4 w-4 mr-2" />
                  OCR Testing
                </Button>
              </Link>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentProject(null)}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back
              </Button>
            </div>
          </div>

          {/* Control bar */}
          <div className="flex flex-wrap items-center gap-3 p-3 bg-muted/30 rounded-lg">
            {/* Country mode toggle - prominent placement */}
            <CountryModeToggle value={countryMode} onChange={setCountryMode} />

            <div className="h-8 w-px bg-border" />

            {/* Batch processing controls */}
            <div className="flex items-center gap-2">
              <Button
                variant={batchSelectionMode ? "default" : "outline"}
                size="sm"
                onClick={handleBatchSelectionToggle}
                className="flex items-center gap-2"
              >
                {batchSelectionMode ? (
                  <CheckSquare className="h-4 w-4" />
                ) : (
                  <Square className="h-4 w-4" />
                )}
                Batch Mode
              </Button>
              {batchSelectionMode &&
                (selectedFolderIds.length > 0 ||
                  selectedBatchImages.length > 0) && (
                  <Button
                    variant="success"
                    size="sm"
                    onClick={
                      selectedBatchImages.length > 0
                        ? handleBatchImageOCRProcess
                        : handleBatchOCRProcess
                    }
                    disabled={isProcessing}
                    className="flex items-center gap-2"
                  >
                    <Play className="h-4 w-4" />
                    {selectedBatchImages.length > 0
                      ? `Process ${selectedBatchImages.length} Images`
                      : `Process ${selectedFolderIds.length} Folders`}
                  </Button>
                )}
            </div>

            <div className="h-8 w-px bg-border" />

            {/* Settings and refresh */}
            <div className="flex items-center gap-2">
              <SettingsPanel />
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  currentProject && scanProjectFolders(currentProject)
                }
                disabled={isScanning || !currentProject}
                className="flex items-center gap-2"
              >
                <RefreshCw
                  className={`h-4 w-4 ${isScanning ? "animate-spin" : ""}`}
                />
                {isScanning ? scanProgress || "Scanning..." : "Refresh"}
              </Button>
              {/* Global cache clear button */}
              <Button
                variant="destructive-outline"
                size="sm"
                onClick={() => handleClearCache()}
                disabled={!selectedFolder}
                className="flex items-center gap-2"
                title="Clear all cached OCR results in this folder"
              >
                <Trash2 className="h-4 w-4" />
                Clear Cache
              </Button>
            </div>

            <div className="h-8 w-px bg-border" />

            {/* Model Display & Connection Status */}
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <Cpu className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">Model:</span>
                {(() => {
                  const modelDisplay = getCurrentModelDisplay();
                  return (
                    <div className="flex items-center gap-2">
                      <Badge
                        variant={
                          modelDisplay.tier === "free" ? "default" : "outline"
                        }
                        className="text-xs px-2 py-1"
                      >
                        {modelDisplay.name}
                      </Badge>
                      <Badge variant="secondary" className="text-xs px-2 py-1">
                        {modelDisplay.provider}
                      </Badge>
                      {modelDisplay.tier === "free" && (
                        <Badge variant="success" className="text-xs px-2 py-1">
                          FREE
                        </Badge>
                      )}
                    </div>
                  );
                })()}
              </div>

              {/* Connection Status & Test Button */}
              <div className="flex items-center gap-2">
                <div
                  className={cn(
                    "h-2 w-2 rounded-full transition-all duration-300",
                    connectionStatus === "connected"
                      ? "bg-green-500 shadow-sm"
                      : connectionStatus === "failed"
                      ? "bg-red-500 shadow-sm"
                      : "bg-gray-400"
                  )}
                />
                <span
                  className={cn(
                    "text-xs font-medium",
                    connectionStatus === "connected"
                      ? "text-green-600 dark:text-green-400"
                      : connectionStatus === "failed"
                      ? "text-red-600 dark:text-red-400"
                      : "text-gray-500"
                  )}
                >
                  {connectionStatus === "connected"
                    ? "Connected"
                    : connectionStatus === "failed"
                    ? "Failed"
                    : "Unknown"}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={testConnection}
                  disabled={isTestingConnection}
                  className="text-xs px-2 py-1 h-8 ml-2"
                >
                  {isTestingConnection ? (
                    <>
                      <RefreshCw className="h-3 w-3 animate-spin mr-1" />
                      Testing...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Test
                    </>
                  )}
                </Button>
              </div>
            </div>

            <div className="ml-auto flex items-center gap-3">
              {/* Grid size control */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Grid:</span>
                <input
                  type="range"
                  min="120"
                  max="300"
                  value={gridSize}
                  onChange={(e) => setGridSize(Number(e.target.value))}
                  className="w-24"
                />
                <span className="text-sm text-muted-foreground">
                  {gridSize}px
                </span>
              </div>

              <div className="h-8 w-px bg-border" />

              <ThemeToggle />
            </div>
          </div>
        </div>

        {error && (
          <Alert className="mt-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 h-[calc(100vh-200px)]">
        {/* Left sidebar - Folder navigation */}
        <div className="lg:col-span-1">
          <Tabs
            value={viewMode}
            onValueChange={(value) => setViewMode(value as "tree" | "overview")}
          >
            <TabsList className="grid w-full grid-cols-2 mb-4">
              <TabsTrigger value="tree" className="flex items-center gap-2">
                <List className="h-4 w-4" />
                Tree
              </TabsTrigger>
              <TabsTrigger value="overview" className="flex items-center gap-2">
                <Grid className="h-4 w-4" />
                Overview
              </TabsTrigger>
            </TabsList>

            <TabsContent value="tree" className="h-[calc(100%-60px)]">
              <FolderTree
                rootPath={currentProject.rootPath}
                folders={folders}
                onFolderSelect={handleFolderSelect}
                onFolderExpand={handleFolderExpand}
                onFolderTag={handleFolderTag}
                onFolderRename={handleFolderRename}
                selectedFolderId={selectedFolder?.id}
                className="h-full"
                enableBatchSelection={batchSelectionMode}
                selectedFolderIds={selectedFolderIds}
                onBatchSelect={handleBatchSelectFolders}
              />
            </TabsContent>

            <TabsContent value="overview" className="h-[calc(100%-60px)]">
              <FolderOverview
                folders={folders}
                onFolderSelect={handleFolderSelect}
                selectedFolderId={selectedFolder?.id}
                className="h-full"
              />
            </TabsContent>
          </Tabs>
        </div>

        {/* Center - Image grid */}
        <div className="lg:col-span-3">
          <Card className="h-full">
            <CardContent className="p-4 pr-6 h-full overflow-auto">
              <EnhancedImageGrid
                images={paginatedImages}
                allImages={images}
                filteredImages={filteredImages}
                folderPath={selectedFolder?.path}
                onImageSelect={handleImageSelect}
                onImageRotate={handleImageRotate}
                selectedImageId={selectedImage?.id}
                gridSize={gridSize}
                onClearCache={handleClearCache}
                multiSelect={batchSelectionMode}
                selectedIds={
                  batchSelectionMode
                    ? multiSelectIds
                    : new Set(selectedImage?.id ? [selectedImage.id] : [])
                }
                onSelectionChange={handleMultiSelect}
                // Filter system props
                filters={filters}
                onAddFilter={addFilter}
                onRemoveFilter={removeFilter}
                onClearAllFilters={clearAllFilters}
                // Pagination props
                showAllImages={showAllImages}
                onShowAllToggle={() => setShowAllImages(!showAllImages)}
                currentPage={currentPage}
                totalPages={totalPages}
                pageSize={pageSize}
                onPageChange={setCurrentPage}
                onPageSizeChange={(size) => {
                  setPageSize(size);
                  setCurrentPage(1);
                }}
                onHasCacheMapUpdate={(newMap) => {
                  // Update cache map for filtering
                  Object.assign(hasCacheMap, newMap);
                }}
              />
            </CardContent>
          </Card>
        </div>

        {/* Right sidebar - Processing, Text, Cost, Rename, and Organization panels */}
        <div className="lg:col-span-1">
          <Tabs
            value={rightPanelMode}
            onValueChange={(value) =>
              setRightPanelMode(
                value as "processing" | "text" | "cost" | "rename" | "organize"
              )
            }
          >
            <TabsList className="grid w-full grid-cols-5 mb-4">
              <TabsTrigger
                value="processing"
                className="flex items-center gap-1 text-xs"
              >
                <Bot className="h-3 w-3" />
                OCR
              </TabsTrigger>
              <TabsTrigger
                value="rename"
                className="flex items-center gap-1 text-xs"
              >
                <Edit3 className="h-3 w-3" />
                Rename
              </TabsTrigger>
              <TabsTrigger
                value="organize"
                className="flex items-center gap-1 text-xs"
              >
                <Archive className="h-3 w-3" />
                Organize
              </TabsTrigger>
              <TabsTrigger
                value="text"
                className="flex items-center gap-1 text-xs"
              >
                <FileText className="h-3 w-3" />
                Text
              </TabsTrigger>
              <TabsTrigger
                value="cost"
                className="flex items-center gap-1 text-xs"
              >
                <DollarSign className="h-3 w-3" />
                Cost
              </TabsTrigger>
            </TabsList>

            <TabsContent value="processing" className="h-[calc(100%-60px)]">
              <ProcessingModeTabs
                images={images}
                selectedImage={selectedImage}
                onImageSelect={handleImageSelect}
                onAnalyze={handleOCRAnalyze}
                ocrResult={ocrResult}
                isProcessing={isProcessing}
                onSave={handleOCRSave}
                onCancel={handleOCRCancel}
                countryMode={countryMode}
                error={error}
                successMessage={successMessage}
                className="h-full"
                folderId={selectedFolder?.id}
                folderPath={selectedFolder?.path}
              />
            </TabsContent>

            <TabsContent value="rename" className="h-[calc(100%-60px)]">
              <ImageRenamingPanel
                images={images}
                onRename={handleImageRename}
                onBatchRename={handleBatchImageRename}
                countryMode={countryMode}
                className="h-full"
              />
            </TabsContent>

            <TabsContent value="organize" className="h-[calc(100%-60px)]">
              <BatchOrganizationPanel
                sourceFolder={selectedFolder?.path || ""}
                images={images}
                onOrganize={handleBatchOrganization}
                countryMode={countryMode}
                className="h-full"
              />
            </TabsContent>

            <TabsContent value="text" className="h-[calc(100%-60px)]">
              <TextEditor
                folderPath={selectedFolder?.path || ""}
                className="h-full"
              />
            </TabsContent>

            <TabsContent value="cost" className="h-[calc(100%-60px)]">
              <CostTrackerPanel
                lastOperationCost={lastOperationCost}
                className="h-full"
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* OCR Save Success Dialog */}
      <OCRSaveSuccessDialog
        isOpen={showSaveSuccessDialog}
        onClose={() => setShowSaveSuccessDialog(false)}
        savedFiles={savedFiles}
        imageName={savedImageName}
      />
    </div>
  );
}
