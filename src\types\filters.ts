// Filter system types for power-filter workbench
// Discriminated union for type-safe filter handling

export type Filter =
  | { kind: 'size-min'; kb: number }
  | { kind: 'size-max'; kb: number }
  | { kind: 'resolution-min'; w: number; h: number }
  | { kind: 'resolution-max'; w: number; h: number }
  | { kind: 'resolution-exact'; w: number; h: number }
  | { kind: 'filename-contains'; text: string; caseSensitive?: boolean }
  | { kind: 'cached'; value: boolean }
  | { kind: 'side'; value: 'front' | 'back' | 'selfie' | 'unknown' }
  | { kind: 'size-range'; range: string }
  | { kind: 'resolution-range'; range: string }
  | { kind: 'select-visible' };

export type FilterStack = Filter[];

export interface FilterAction {
  type: 'add' | 'remove' | 'reset' | 'replace';
  filter?: Filter;
  index?: number;
  filters?: Filter[];
}

// Helper function to render human-readable filter labels
export const renderFilterLabel = (filter: Filter): string => {
  switch (filter.kind) {
    case 'size-min':
      return `≥ ${filter.kb} KB`;
    case 'size-max':
      return `≤ ${filter.kb} KB`;
    case 'resolution-min':
      return `≥ ${filter.w}×${filter.h}`;
    case 'resolution-max':
      return `≤ ${filter.w}×${filter.h}`;
    case 'resolution-exact':
      return `${filter.w}×${filter.h}`;
    case 'filename-contains':
      return `name contains "${filter.text}"${filter.caseSensitive ? ' (case-sensitive)' : ''}`;
    case 'cached':
      return filter.value ? 'Processed' : 'Unprocessed';
    case 'side':
      return `${filter.value} side`;
    case 'size-range':
      return filter.range;
    case 'resolution-range':
      return filter.range;
    case 'select-visible':
      return 'all visible';
    default:
      return 'Unknown filter';
  }
};

// Validation helpers
export const isValidFilter = (filter: Filter): boolean => {
  switch (filter.kind) {
    case 'size-min':
    case 'size-max':
      return filter.kb > 0;
    case 'resolution-min':
    case 'resolution-max':
    case 'resolution-exact':
      return filter.w > 0 && filter.h > 0;
    case 'filename-contains':
      return filter.text.trim().length > 0;
    case 'cached':
      return typeof filter.value === 'boolean';
    case 'side':
      return ['front', 'back', 'selfie', 'unknown'].includes(filter.value);
    case 'size-range':
    case 'resolution-range':
      return filter.range.trim().length > 0;
    case 'select-visible':
      return true;
    default:
      return false;
  }
};