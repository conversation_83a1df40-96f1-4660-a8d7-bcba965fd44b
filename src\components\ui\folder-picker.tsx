"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import {
  Folder,
  FolderOpen,
  HardDrive,
  ChevronRight,
  RefreshCw,
} from "lucide-react";

interface FolderInfo {
  name: string;
  path: string;
  type: "folder" | "drive";
  children?: FolderInfo[];
  isExpanded?: boolean;
  imageCount?: number;
}

interface FolderPickerProps {
  value?: string;
  onChange: (path: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export default function FolderPicker({
  value = "",
  onChange,
  placeholder = "Select a folder...",
  className = "",
  disabled = false,
}: FolderPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [folders, setFolders] = useState<FolderInfo[]>([]);
  const [selectedPath, setSelectedPath] = useState(value);
  const [isLoading, setIsLoading] = useState(false);
  const [expandingPath, setExpandingPath] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const loadDrives = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/filesystem/drives");
      if (!response.ok) {
        throw new Error("Failed to load drives");
      }

      const data = await response.json();
      // Mark drives as expandable by setting children to empty array
      const drivesWithExpandability = (data.drives || []).map(
        (drive: FolderInfo) => ({
          ...drive,
          children: [], // Empty array indicates it can be expanded
        })
      );
      setFolders(drivesWithExpandability);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load drives");
      console.error("Error loading drives:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const loadSubfolders = async (folderPath: string, maxDepth: number = 1) => {
    setExpandingPath(folderPath);
    setError(null);

    try {
      // For drive root paths, skip image counting for performance
      const isDriveRoot = /^[A-Za-z]:[\\\/]?$/.test(folderPath);

      // Add timeout for drive expansion requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout for deeper scans

      const response = await fetch("/api/filesystem/folders", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          path: folderPath,
          includeStats: !isDriveRoot, // Skip stats for drive roots for performance
          maxDepth: maxDepth,
          recursive: maxDepth > 1, // Enable recursive scanning for deeper traversal
        }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error("Failed to load subfolders");
      }

      const data = await response.json();

      // Process folders recursively to build proper tree structure
      const processFolder = (folder: any): FolderInfo => {
        const processed: FolderInfo = {
          ...folder,
          children: folder.children ? folder.children.map(processFolder) : [],
        };

        // Mark as expandable if it has children or could potentially have children
        if (!processed.children || processed.children.length === 0) {
          processed.children = []; // Empty array indicates it can be expanded
        }

        return processed;
      };

      const foldersWithExpandability = (data.folders || []).map(processFolder);
      return foldersWithExpandability;
    } catch (err) {
      if (err instanceof Error && err.name === "AbortError") {
        setError("Request timed out - try expanding a smaller folder first");
      } else {
        setError(
          err instanceof Error ? err.message : "Failed to load subfolders"
        );
      }
      console.error("Error loading subfolders:", err);
      return [];
    } finally {
      setExpandingPath(null);
    }
  };

  const handleFolderExpand = async (
    folderPath: string,
    deepScan: boolean = false
  ) => {
    // Only expand if not already expanded
    const targetFolder = findFolderInTree(folders, folderPath);
    if (targetFolder && targetFolder.isExpanded) {
      // If already expanded, just collapse it
      setFolders((prev) =>
        prev.map((folder) => updateFolderInTree(folder, folderPath, []))
      );
      return;
    }

    // Use deeper scanning for better folder traversal
    const maxDepth = deepScan ? 3 : 1;
    const subfolders = await loadSubfolders(folderPath, maxDepth);

    setFolders((prev) =>
      prev.map((folder) => updateFolderInTree(folder, folderPath, subfolders))
    );
  };

  const findFolderInTree = (
    folders: FolderInfo[],
    targetPath: string
  ): FolderInfo | null => {
    for (const folder of folders) {
      if (folder.path === targetPath) {
        return folder;
      }
      if (folder.children) {
        const found = findFolderInTree(folder.children, targetPath);
        if (found) return found;
      }
    }
    return null;
  };

  const updateFolderInTree = (
    folder: FolderInfo,
    targetPath: string,
    children: FolderInfo[]
  ): FolderInfo => {
    if (folder.path === targetPath) {
      return {
        ...folder,
        isExpanded: !folder.isExpanded,
        children: folder.isExpanded ? [] : children, // Collapse to empty array, expand with loaded children
      };
    }

    if (folder.children) {
      return {
        ...folder,
        children: folder.children.map((child) =>
          updateFolderInTree(child, targetPath, children)
        ),
      };
    }

    return folder;
  };

  const handleFolderSelect = (folderPath: string) => {
    setSelectedPath(folderPath);
  };

  const handleConfirm = () => {
    if (selectedPath) {
      onChange(selectedPath);
      setIsOpen(false);
    }
  };

  const handleOpenDialog = () => {
    setIsOpen(true);
    setSelectedPath(value);
    loadDrives();
  };

  const renderFolderTree = (folders: FolderInfo[], level = 0) => {
    return folders.map((folder) => (
      <div key={folder.path} className="select-none">
        <div
          className={`flex items-center gap-2 p-2 rounded cursor-pointer hover:bg-muted/50 ${
            selectedPath === folder.path ? "bg-blue-100 dark:bg-blue-900" : ""
          }`}
          style={{ marginLeft: level * 20 }}
          onClick={() => handleFolderSelect(folder.path)}
        >
          <div className="flex items-center gap-1">
            {(folder.children !== undefined || folder.type === "drive") &&
              (expandingPath === folder.path ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <ChevronRight
                  className={`h-4 w-4 transition-transform ${
                    folder.isExpanded ? "rotate-90" : ""
                  }`}
                  onClick={(e) => {
                    e.stopPropagation();
                    // Use Ctrl+Click for deep scan
                    const deepScan = e.ctrlKey || e.metaKey;
                    handleFolderExpand(folder.path, deepScan);
                  }}
                />
              ))}
            {folder.type === "drive" ? (
              <HardDrive className="h-4 w-4 text-blue-500" />
            ) : folder.isExpanded ? (
              <FolderOpen className="h-4 w-4 text-yellow-500" />
            ) : (
              <Folder className="h-4 w-4 text-yellow-500" />
            )}
          </div>
          <span className="text-sm flex-1">{folder.name}</span>
          {folder.imageCount !== undefined && folder.imageCount > 0 && (
            <Badge variant="secondary" className="text-xs">
              {folder.imageCount} images
            </Badge>
          )}
        </div>
        {folder.isExpanded && folder.children && (
          <div>{renderFolderTree(folder.children, level + 1)}</div>
        )}
      </div>
    ));
  };

  return (
    <div className={className}>
      <div className="flex gap-2">
        <Input
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          className="flex-1"
        />
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogTrigger asChild>
            <Button
              variant="outline"
              onClick={handleOpenDialog}
              disabled={disabled}
            >
              <Folder className="h-4 w-4 mr-2" />
              Browse
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[80vh]">
            <DialogHeader>
              <DialogTitle>Select Folder</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              {error && (
                <div className="text-sm text-red-500 bg-red-50 dark:bg-red-900/20 p-3 rounded">
                  {error}
                </div>
              )}

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadDrives}
                  disabled={isLoading}
                >
                  <RefreshCw
                    className={`h-4 w-4 mr-2 ${
                      isLoading ? "animate-spin" : ""
                    }`}
                  />
                  Refresh
                </Button>
                {selectedPath && (
                  <div className="flex-1 text-sm text-muted-foreground">
                    Selected: {selectedPath}
                  </div>
                )}
              </div>

              <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded">
                💡 Tip: Hold Ctrl/Cmd while clicking folder arrows for deep scan
                (shows nested folders with images)
              </div>

              <Card>
                <CardContent className="p-0">
                  <ScrollArea className="h-[400px]">
                    <div className="p-4">
                      {isLoading && folders.length === 0 ? (
                        <div className="flex items-center justify-center py-8">
                          <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                          <span>Loading...</span>
                        </div>
                      ) : (
                        renderFolderTree(folders)
                      )}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleConfirm} disabled={!selectedPath}>
                  Select Folder
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
