import { NextRequest, NextResponse } from "next/server";
import { getAppConfig } from "../../../../config/app-config";

// Add explicit connection test
const testBackendConnection = async (backendUrl: string) => {
  try {
    const testResponse = await fetch(`${backendUrl}/api/health`, {
      method: "GET",
      signal: AbortSignal.timeout(5000), // 5 second timeout for test
    });
    console.log(
      "🔍 Backend connection test:",
      testResponse.ok ? "SUCCESS" : "FAILED",
      `(${testResponse.status})`
    );
    return testResponse.ok;
  } catch (testError) {
    console.error(
      "🔍 Backend connection test FAILED:",
      testError instanceof Error ? testError.message : testError
    );
    return false;
  }
};

export async function POST(request: NextRequest) {
  try {
    const config = getAppConfig();
    const BACKEND_URL = config.backendUrl;

    // Test backend connection first
    const backendAlive = await testBackendConnection(BACKEND_URL);
    if (!backendAlive) {
      return NextResponse.json(
        {
          success: false,
          error: "Backend server is not responding",
          details: `Cannot connect to backend at ${BACKEND_URL}`,
          backendUrl: BACKEND_URL,
        },
        { status: 503 }
      );
    }

    // Get the request body
    const body = await request.text();

    console.log("🔄 OCR Analyze API Route: Received request");
    console.log(
      "📤 OCR Analyze API Route: Request body preview:",
      body.substring(0, 200) + "..."
    );
    console.log(
      "🎯 OCR Analyze API Route: Forwarding to backend",
      `${BACKEND_URL}/api/ocr/analyze-by-path`
    );

    // Forward the request to the backend with longer timeout (30 seconds for OCR processing)
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    const response = await fetch(`${BACKEND_URL}/api/ocr/analyze-by-path`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "User-Agent": request.headers.get("User-Agent") || "",
      },
      body: body,
      signal: controller.signal,
    });

    clearTimeout(timeoutId); // Clear timeout on successful response

    // Get response data
    const data = await response.text();

    console.log(
      "📥 OCR Analyze API Route: Backend response status:",
      response.status
    );
    console.log(
      "📥 OCR Analyze API Route: Response preview:",
      data.substring(0, 200) + "..."
    );

    // Return response with same status and headers
    return new NextResponse(data, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("❌ API Proxy Error (ocr/analyze-by-path):", error);
    console.error("❌ Backend URL attempted:", BACKEND_URL);
    console.error(
      "❌ Error name:",
      error instanceof Error ? error.name : "Unknown"
    );
    console.error(
      "❌ Error message:",
      error instanceof Error ? error.message : String(error)
    );
    console.error(
      "❌ Error cause:",
      error instanceof Error ? (error as any).cause : "None"
    );

    let errorMessage =
      "Proxy error: Failed to forward OCR analyze request to backend";
    let errorDetails = error instanceof Error ? error.message : "Unknown error";

    if (error instanceof Error && error.name === "AbortError") {
      errorMessage =
        "Request timeout: OCR analysis took too long (>30 seconds)";
      errorDetails =
        "The OCR processing timed out. Try again or use a smaller image.";
    }

    // Add more specific error handling
    if (error instanceof Error) {
      if (error.message.includes("ECONNREFUSED")) {
        errorMessage =
          "Backend connection refused: Backend server may not be running";
        errorDetails = `Cannot connect to backend at ${BACKEND_URL}`;
      } else if (
        error.message.includes("fetch failed") ||
        error.message.includes("network")
      ) {
        errorMessage = "Network error: Failed to connect to backend server";
        errorDetails = `Check if backend server is running at ${BACKEND_URL}`;
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        details: errorDetails,
        backendUrl: BACKEND_URL,
        specificError: true,
      },
      { status: 500 }
    );
  }
}
