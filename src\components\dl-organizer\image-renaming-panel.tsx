"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Edit3, 
  Wand2, 
  FolderOpen, 
  Image as ImageIcon, 
  CheckCircle2, 
  AlertCircle,
  Loader2,
  Download,
  FileText,
  Copy
} from 'lucide-react'
import { ImageFile, OCRResult } from '@/types'
import { cn } from '@/lib/utils'

interface ImageRenamingPanelProps {
  images: ImageFile[]
  onRename: (imageId: string, newName: string) => Promise<boolean>
  onBatchRename: (renamingPairs: Array<{imageId: string, newName: string}>) => Promise<boolean>
  className?: string
  countryMode: 'us' | 'australian'
}

interface RenamingOperation {
  imageId: string
  originalName: string
  newName: string
  status: 'pending' | 'processing' | 'completed' | 'error'
  error?: string
}

interface AutoRenamingSettings {
  includeNames: boolean
  includeDOB: boolean
  includeExpiration: boolean
  includeSide: boolean
  dateFormat: 'YYYY-MM-DD' | 'MMDDYYYY' | 'DDMMYYYY'
  separator: '-' | '_'
  case: 'original' | 'lowercase' | 'uppercase'
}

// Front/Back detection logic
const detectLicenseSide = (ocrText: string, extractedData: Partial<OCRResult>): 'front' | 'back' | 'unknown' => {
  const text = ocrText.toLowerCase()
  
  // Strong indicators for front side
  const frontIndicators = [
    'driver license', 'driver licence', 'driving license', 'driving licence',
    'date of birth', 'dob', 'address', 'height', 'weight', 'sex', 'eyes',
    'photo', 'picture', 'restrictions', 'class', 'endorsements'
  ]
  
  // Strong indicators for back side  
  const backIndicators = [
    'organ donor', 'veteran', 'conditions', 'restrictions',
    'magnetic stripe', 'barcode', '2d barcode', 'pdf417',
    'emergency contact', 'medical information'
  ]
  
  // Check for specific patterns
  const frontScore = frontIndicators.reduce((score, indicator) => 
    text.includes(indicator) ? score + 1 : score, 0)
  const backScore = backIndicators.reduce((score, indicator) => 
    text.includes(indicator) ? score + 1 : score, 0)
  
  // Additional heuristics
  const hasPersonalInfo = !!(extractedData.firstName || extractedData.lastName || extractedData.dateOfBirth)
  const hasLicenseNumber = !!extractedData.licenseNumber
  
  if (frontScore > backScore || (hasPersonalInfo && hasLicenseNumber)) {
    return 'front'
  } else if (backScore > frontScore) {
    return 'back'
  }
  
  return 'unknown'
}

// Generate automatic filename based on OCR data
const generateAutoFilename = (
  ocrData: Partial<OCRResult>, 
  side: 'front' | 'back' | 'unknown',
  settings: AutoRenamingSettings,
  originalExtension: string
): string => {
  const parts: string[] = []
  
  // Add names
  if (settings.includeNames && ocrData.firstName && ocrData.lastName) {
    const firstName = ocrData.firstName.trim()
    const lastName = ocrData.lastName.trim()
    parts.push(`${firstName}${settings.separator}${lastName}`)
  }
  
  // Add DOB
  if (settings.includeDOB && ocrData.dateOfBirth) {
    const dob = new Date(ocrData.dateOfBirth)
    if (!isNaN(dob.getTime())) {
      let dobStr = ''
      switch (settings.dateFormat) {
        case 'YYYY-MM-DD':
          dobStr = dob.toISOString().split('T')[0].replace(/-/g, '')
          break
        case 'MMDDYYYY':
          dobStr = `${(dob.getMonth() + 1).toString().padStart(2, '0')}${dob.getDate().toString().padStart(2, '0')}${dob.getFullYear()}`
          break
        case 'DDMMYYYY':
          dobStr = `${dob.getDate().toString().padStart(2, '0')}${(dob.getMonth() + 1).toString().padStart(2, '0')}${dob.getFullYear()}`
          break
      }
      parts.push(dobStr)
    }
  }
  
  // Add expiration
  if (settings.includeExpiration && ocrData.expirationDate) {
    const exp = new Date(ocrData.expirationDate)
    if (!isNaN(exp.getTime())) {
      const expStr = `exp${exp.getFullYear()}${(exp.getMonth() + 1).toString().padStart(2, '0')}`
      parts.push(expStr)
    }
  }
  
  // Add side
  if (settings.includeSide && side !== 'unknown') {
    parts.push(side)
  }
  
  // Join parts and apply case transformation
  let filename = parts.join(settings.separator)
  
  switch (settings.case) {
    case 'lowercase':
      filename = filename.toLowerCase()
      break
    case 'uppercase':
      filename = filename.toUpperCase()
      break
  }
  
  return filename ? `${filename}${originalExtension}` : `unnamed${originalExtension}`
}

export default function ImageRenamingPanel({
  images,
  onRename,
  onBatchRename,
  className,
  countryMode
}: ImageRenamingPanelProps) {
  const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set())
  const [operations, setOperations] = useState<RenamingOperation[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [manualRename, setManualRename] = useState<{imageId: string, newName: string} | null>(null)
  
  // Auto-renaming settings
  const [autoSettings, setAutoSettings] = useState<AutoRenamingSettings>({
    includeNames: true,
    includeDOB: true,
    includeExpiration: false,
    includeSide: true,
    dateFormat: 'YYYY-MM-DD',
    separator: '-',
    case: 'lowercase'
  })
  
  // Mock OCR data - in real app, this would come from actual OCR results
  const [ocrDataMap, setOcrDataMap] = useState<Map<string, {ocrResult: Partial<OCRResult>, side: 'front' | 'back' | 'unknown'}>>(new Map())

  const handleImageSelect = (imageId: string, checked: boolean) => {
    const newSelected = new Set(selectedImages)
    if (checked) {
      newSelected.add(imageId)
    } else {
      newSelected.delete(imageId)
    }
    setSelectedImages(newSelected)
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedImages(new Set(images.map(img => img.id)))
    } else {
      setSelectedImages(new Set())
    }
  }

  // Simulate OCR analysis for demo purposes
  const performOCRAnalysis = async (imageId: string): Promise<{ocrResult: Partial<OCRResult>, side: 'front' | 'back' | 'unknown'}> => {
    // In real implementation, this would call your OCR API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Mock OCR data based on image name patterns
    const image = images.find(img => img.id === imageId)
    const filename = image?.filename.toLowerCase() || ''
    
    const mockData: Partial<OCRResult> = {
      firstName: 'John',
      lastName: 'Smith', 
      dateOfBirth: '1990-05-15',
      licenseNumber: 'D123456789',
      state: countryMode === 'australian' ? 'NSW' : 'CA',
      expirationDate: '2025-05-15'
    }
    
    // Detect side based on filename or content
    const side = filename.includes('back') ? 'back' : 
                 filename.includes('front') ? 'front' : 
                 detectLicenseSide('driver license john smith date of birth', mockData)
    
    return { ocrResult: mockData, side }
  }

  const handleAutoRename = async () => {
    if (selectedImages.size === 0) return
    
    setIsProcessing(true)
    const newOperations: RenamingOperation[] = []
    
    try {
      // First, perform OCR analysis on selected images
      for (const imageId of selectedImages) {
        const image = images.find(img => img.id === imageId)
        if (!image) continue
        
        newOperations.push({
          imageId,
          originalName: image.filename,
          newName: 'Analyzing...',
          status: 'processing'
        })
      }
      
      setOperations(newOperations)
      
      // Perform OCR analysis
      for (let i = 0; i < newOperations.length; i++) {
        const op = newOperations[i]
        const image = images.find(img => img.id === op.imageId)
        if (!image) continue
        
        try {
          const { ocrResult, side } = await performOCRAnalysis(op.imageId)
          ocrDataMap.set(op.imageId, { ocrResult, side })
          
          const extension = '.' + image.filename.split('.').pop()
          const newName = generateAutoFilename(ocrResult, side, autoSettings, extension)
          
          newOperations[i] = {
            ...op,
            newName,
            status: 'pending'
          }
          
          setOperations([...newOperations])
        } catch (error) {
          newOperations[i] = {
            ...op,
            status: 'error',
            error: 'OCR analysis failed'
          }
          setOperations([...newOperations])
        }
      }
    } finally {
      setIsProcessing(false)
    }
  }

  const handleExecuteRename = async () => {
    const validOps = operations.filter(op => op.status === 'pending' && op.newName !== op.originalName)
    if (validOps.length === 0) return
    
    setIsProcessing(true)
    
    try {
      // Execute batch rename
      const renamePairs = validOps.map(op => ({
        imageId: op.imageId,
        newName: op.newName
      }))
      
      const success = await onBatchRename(renamePairs)
      
      if (success) {
        setOperations(ops => ops.map(op => 
          validOps.some(v => v.imageId === op.imageId) 
            ? { ...op, status: 'completed' as const }
            : op
        ))
      } else {
        setOperations(ops => ops.map(op => 
          validOps.some(v => v.imageId === op.imageId)
            ? { ...op, status: 'error' as const, error: 'Rename failed' }
            : op
        ))
      }
    } finally {
      setIsProcessing(false)
    }
  }

  const handleManualRename = async () => {
    if (!manualRename) return
    
    setIsProcessing(true)
    try {
      const success = await onRename(manualRename.imageId, manualRename.newName)
      if (success) {
        setManualRename(null)
      }
    } finally {
      setIsProcessing(false)
    }
  }

  const getStatusIcon = (status: RenamingOperation['status']) => {
    switch (status) {
      case 'pending':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
      case 'processing':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
      case 'completed':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
    }
  }

  return (
    <Card className={cn("h-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Edit3 className="h-5 w-5" />
          Image Renaming
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Image Selection */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">Select Images</h4>
            <div className="flex items-center gap-2">
              <Checkbox
                checked={selectedImages.size === images.length && images.length > 0}
                onCheckedChange={handleSelectAll}
                id="select-all"
              />
              <label htmlFor="select-all" className="text-sm">
                Select All ({images.length})
              </label>
            </div>
          </div>
          
          <div className="max-h-32 overflow-y-auto space-y-1">
            {images.map(image => (
              <div key={image.id} className="flex items-center gap-2 p-2 rounded border">
                <Checkbox
                  checked={selectedImages.has(image.id)}
                  onCheckedChange={(checked) => handleImageSelect(image.id, checked as boolean)}
                  id={`image-${image.id}`}
                />
                <ImageIcon className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm truncate flex-1">{image.filename}</span>
                <Badge variant="outline" className="text-xs">
                  {Math.round(image.fileSize / 1024)}KB
                </Badge>
              </div>
            ))}
          </div>
          
          {selectedImages.size > 0 && (
            <Badge variant="secondary">
              {selectedImages.size} image{selectedImages.size !== 1 ? 's' : ''} selected
            </Badge>
          )}
        </div>

        {/* Auto-Renaming Settings */}
        <div className="space-y-3">
          <h4 className="font-medium flex items-center gap-2">
            <Wand2 className="h-4 w-4" />
            Auto-Renaming Settings
          </h4>
          
          <div className="grid grid-cols-2 gap-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-names"
                checked={autoSettings.includeNames}
                onCheckedChange={(checked) => setAutoSettings(prev => ({
                  ...prev,
                  includeNames: checked as boolean
                }))}
              />
              <label htmlFor="include-names" className="text-sm">Include Names</label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-dob"
                checked={autoSettings.includeDOB}
                onCheckedChange={(checked) => setAutoSettings(prev => ({
                  ...prev,
                  includeDOB: checked as boolean
                }))}
              />
              <label htmlFor="include-dob" className="text-sm">Include DOB</label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-exp"
                checked={autoSettings.includeExpiration}
                onCheckedChange={(checked) => setAutoSettings(prev => ({
                  ...prev,
                  includeExpiration: checked as boolean
                }))}
              />
              <label htmlFor="include-exp" className="text-sm">Include Expiration</label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-side"
                checked={autoSettings.includeSide}
                onCheckedChange={(checked) => setAutoSettings(prev => ({
                  ...prev,
                  includeSide: checked as boolean
                }))}
              />
              <label htmlFor="include-side" className="text-sm">Include Side</label>
            </div>
          </div>
          
          <div className="grid grid-cols-3 gap-3">
            <div className="space-y-1">
              <label className="text-xs font-medium">Date Format</label>
              <Select
                value={autoSettings.dateFormat}
                onValueChange={(value: any) => setAutoSettings(prev => ({
                  ...prev,
                  dateFormat: value
                }))}
              >
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                  <SelectItem value="MMDDYYYY">MM/DD/YYYY</SelectItem>
                  <SelectItem value="DDMMYYYY">DD/MM/YYYY</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-1">
              <label className="text-xs font-medium">Separator</label>
              <Select
                value={autoSettings.separator}
                onValueChange={(value: any) => setAutoSettings(prev => ({
                  ...prev,
                  separator: value
                }))}
              >
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="-">Dash (-)</SelectItem>
                  <SelectItem value="_">Underscore (_)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-1">
              <label className="text-xs font-medium">Case</label>
              <Select
                value={autoSettings.case}
                onValueChange={(value: any) => setAutoSettings(prev => ({
                  ...prev,
                  case: value
                }))}
              >
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="original">Original</SelectItem>
                  <SelectItem value="lowercase">Lowercase</SelectItem>
                  <SelectItem value="uppercase">Uppercase</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button
            onClick={handleAutoRename}
            disabled={selectedImages.size === 0 || isProcessing}
            className="flex-1"
          >
            <Wand2 className="h-4 w-4 mr-2" />
            Auto-Rename Selected
          </Button>
          
          {operations.length > 0 && operations.some(op => op.status === 'pending') && (
            <Button
              onClick={handleExecuteRename}
              disabled={isProcessing}
              variant="default"
            >
              <Download className="h-4 w-4 mr-2" />
              Execute Rename
            </Button>
          )}
        </div>

        {/* Renaming Operations */}
        {operations.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium">Renaming Preview</h4>
            <div className="max-h-48 overflow-y-auto space-y-2">
              {operations.map(op => (
                <div key={op.imageId} className="p-3 rounded border bg-muted/50">
                  <div className="flex items-center gap-2 mb-2">
                    {getStatusIcon(op.status)}
                    <span className="text-sm font-medium capitalize">{op.status}</span>
                    {op.error && (
                      <span className="text-xs text-red-500">({op.error})</span>
                    )}
                  </div>
                  <div className="space-y-1 text-xs">
                    <div className="flex">
                      <span className="w-12 text-muted-foreground">From:</span>
                      <span className="font-mono">{op.originalName}</span>
                    </div>
                    <div className="flex">
                      <span className="w-12 text-muted-foreground">To:</span>
                      <span className="font-mono text-green-600">{op.newName}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Manual Rename */}
        {manualRename && (
          <div className="space-y-3 p-3 rounded border">
            <h4 className="font-medium">Manual Rename</h4>
            <div className="space-y-2">
              <Input
                value={manualRename.newName}
                onChange={(e) => setManualRename(prev => prev ? {
                  ...prev,
                  newName: e.target.value
                } : null)}
                placeholder="Enter new filename"
              />
              <div className="flex gap-2">
                <Button onClick={handleManualRename} disabled={isProcessing} size="sm">
                  Rename
                </Button>
                <Button 
                  onClick={() => setManualRename(null)} 
                  variant="outline" 
                  size="sm"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}