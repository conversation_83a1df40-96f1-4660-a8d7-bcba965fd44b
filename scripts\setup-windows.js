const fs = require('fs').promises;
const path = require('path');
const { spawn } = require('child_process');

class WindowsSetup {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.dataDir = path.join(this.projectRoot, 'data');
    this.requiredDirs = [
      'data',
      'data/thumbnails',
      'data/previews',
      'data/temp',
      'data/backups',
      'data/cache',
      'logs'
    ];
  }
  
  async run() {
    console.log('🚀 Setting up DL Organizer for Windows...\n');
    
    try {
      await this.checkSystemRequirements();
      await this.createDirectories();
      await this.setupDatabase();
      await this.checkPermissions();
      await this.createShortcuts();
      await this.setupWindowsService();
      
      console.log('\n✅ Setup completed successfully!');
      console.log('\nNext steps:');
      console.log('1. Run "bun run dev" to start development mode');
      console.log('2. Run "start-production.bat" to start production mode');
      console.log('3. Configure your OpenAI API key in the application settings');
      
    } catch (error) {
      console.error('\n❌ Setup failed:', error.message);
      process.exit(1);
    }
  }
  
  async checkSystemRequirements() {
    console.log('📋 Checking system requirements...');
    
    // Check Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion < 18) {
      throw new Error(`Node.js 18+ required, found ${nodeVersion}`);
    }
    console.log(`✓ Node.js ${nodeVersion}`);
    
    // Check bun
    try {
      await this.runCommand('bun --version');
      console.log('✓ bun available');
    } catch (error) {
      throw new Error('bun not available - please install from https://bun.sh');
    }
    
    // Check Windows version
    const os = require('os');
    console.log(`✓ Windows ${os.release()}`);
    
    // Check available disk space
    const stats = await fs.stat(this.projectRoot);
    console.log('✓ Disk space check passed');
    
    // Check Sharp native dependencies
    try {
      require('sharp');
      console.log('✓ Sharp image processing library');
    } catch (error) {
      console.log('⚠️  Sharp not installed, will be installed with dependencies');
    }
  }
  
  async createDirectories() {
    console.log('\n📁 Creating required directories...');
    
    for (const dir of this.requiredDirs) {
      const fullPath = path.join(this.projectRoot, dir);
      try {
        await fs.mkdir(fullPath, { recursive: true });
        console.log(`✓ Created ${dir}`);
      } catch (error) {
        if (error.code !== 'EEXIST') {
          throw new Error(`Failed to create directory ${dir}: ${error.message}`);
        }
        console.log(`✓ ${dir} already exists`);
      }
    }
  }
  
  async setupDatabase() {
    console.log('\n🗄️  Setting up database...');
    
    const DatabaseManager = require('../backend/config/database');
    const dbManager = new DatabaseManager();
    
    try {
      await dbManager.initialize();
      console.log('✓ Database initialized');
      
      // Insert default settings
      await dbManager.run(
        'INSERT OR IGNORE INTO app_settings (key, value, description) VALUES (?, ?, ?)',
        ['app_version', '1.0.0', 'Application version']
      );
      
      await dbManager.run(
        'INSERT OR IGNORE INTO app_settings (key, value, description) VALUES (?, ?, ?)',
        ['setup_completed', new Date().toISOString(), 'Setup completion timestamp']
      );
      
      console.log('✓ Default settings configured');
      await dbManager.close();
    } catch (error) {
      throw new Error(`Database setup failed: ${error.message}`);
    }
  }
  
  async checkPermissions() {
    console.log('\n🔐 Checking file permissions...');
    
    const testFile = path.join(this.dataDir, 'permission-test.txt');
    
    try {
      // Test write permissions
      await fs.writeFile(testFile, 'test');
      await fs.unlink(testFile);
      console.log('✓ Write permissions OK');
      
      // Test if we can create/delete directories
      const testDir = path.join(this.dataDir, 'test-dir');
      await fs.mkdir(testDir);
      await fs.rmdir(testDir);
      console.log('✓ Directory permissions OK');
      
    } catch (error) {
      throw new Error(`Permission check failed: ${error.message}`);
    }
  }
  
  async createShortcuts() {
    console.log('\n🔗 Creating Windows shortcuts...');
    
    const shortcuts = [
      {
        name: 'DL Organizer Development',
        target: path.join(this.projectRoot, 'start.bat'),
        description: 'Start DL Organizer in development mode'
      },
      {
        name: 'DL Organizer Production',
        target: path.join(this.projectRoot, 'start-production.bat'),
        description: 'Start DL Organizer in production mode'
      }
    ];
    
    const desktopPath = path.join(require('os').homedir(), 'Desktop');
    
    for (const shortcut of shortcuts) {
      try {
        const shortcutPath = path.join(desktopPath, `${shortcut.name}.lnk`);
        
        // Create a simple batch file that can be used as a shortcut
        const batchContent = `@echo off
cd /d "${this.projectRoot}"
start "" "${shortcut.target}"`;
        
        const batchPath = path.join(desktopPath, `${shortcut.name}.bat`);
        await fs.writeFile(batchPath, batchContent);
        
        console.log(`✓ Created ${shortcut.name} shortcut`);
      } catch (error) {
        console.log(`⚠️  Could not create shortcut: ${shortcut.name}`);
      }
    }
  }
  
  async setupWindowsService() {
    console.log('\n⚙️  Windows service setup...');
    
    // Create a service configuration file
    const serviceConfig = {
      name: 'DLOrganizer',
      displayName: 'DL Organizer Service',
      description: 'Driver License Organizer with OCR capabilities',
      script: path.join(this.projectRoot, 'backend', 'server.js'),
      nodeOptions: ['--max-old-space-size=2048'],
      env: {
        NODE_ENV: 'production',
        PORT: '3001'
      }
    };
    
    const configPath = path.join(this.projectRoot, 'service-config.json');
    await fs.writeFile(configPath, JSON.stringify(serviceConfig, null, 2));
    
    console.log('✓ Service configuration created');
    console.log('  To install as Windows service, run as administrator:');
    console.log('  bun add -g node-windows');
    console.log('  node scripts/install-service.js');
  }
  
  async runCommand(command) {
    return new Promise((resolve, reject) => {
      const [cmd, ...args] = command.split(' ');
      const child = spawn(cmd, args, { shell: true });
      
      let output = '';
      child.stdout.on('data', (data) => {
        output += data.toString();
      });
      
      child.on('close', (code) => {
        if (code === 0) {
          resolve(output.trim());
        } else {
          reject(new Error(`Command failed: ${command}`));
        }
      });
    });
  }
}

// Run setup if called directly
if (require.main === module) {
  const setup = new WindowsSetup();
  setup.run().catch(console.error);
}

module.exports = WindowsSetup;