{"name": "dl-organizer", "version": "1.1.0", "private": true, "description": "Driver's License Organizer with AI-powered OCR capabilities", "main": "server.js", "scripts": {"dev": "start-dev.bat", "dev:frontend": "next dev -p 3031", "dev:backend": "bun run --hot backend/server.js", "dev:smart": "start-dev-smart.bat", "build": "next build", "start": "concurrently \"npx next start -p 3001\" \"C:\\Users\\<USER>\\.bun\\bin\\bun.exe run scripts/start-backend.js\"", "start:frontend": "next start -p 3001", "start:backend": "C:\\Users\\<USER>\\.bun\\bin\\bun.exe run scripts/start-backend.js", "setup": "C:\\Users\\<USER>\\.bun\\bin\\bun.exe run scripts/production-setup.js", "setup:windows": "C:\\Users\\<USER>\\.bun\\bin\\bun.exe run scripts/setup-windows.js", "backup": "C:\\Users\\<USER>\\.bun\\bin\\bun.exe run scripts/backup-database.js create", "backup:list": "C:\\Users\\<USER>\\.bun\\bin\\bun.exe run scripts/backup-database.js list", "backup:restore": "C:\\Users\\<USER>\\.bun\\bin\\bun.exe run scripts/backup-database.js restore", "maintenance": "C:\\Users\\<USER>\\.bun\\bin\\bun.exe run scripts/maintenance.js", "health": "C:\\Users\\<USER>\\.bun\\bin\\bun.exe run scripts/maintenance.js health", "service:install": "C:\\Users\\<USER>\\.bun\\bin\\bun.exe run scripts/install-service.js", "service:uninstall": "C:\\Users\\<USER>\\.bun\\bin\\bun.exe run scripts/uninstall-service.js", "ports:check": "C:\\Users\\<USER>\\.bun\\bin\\bun.exe run scripts/port-manager.js check", "ports:resolve": "C:\\Users\\<USER>\\.bun\\bin\\bun.exe run scripts/port-manager.js resolve", "ports:force": "C:\\Users\\<USER>\\.bun\\bin\\bun.exe run scripts/port-manager.js resolve --force", "launcher": "launcher.bat", "launcher:dev": "launcher.bat dev", "lint": "next lint", "typecheck": "tsc --noEmit", "test": "C:\\Users\\<USER>\\.bun\\bin\\bun.exe test", "test:watch": "C:\\Users\\<USER>\\.bun\\bin\\bun.exe test --watch", "test:e2e": "playwright test", "test:integration": "C:\\Users\\<USER>\\.bun\\bin\\bun.exe test --testPathPattern=integration", "postinstall": "C:\\Users\\<USER>\\.bun\\bin\\bun.exe run -e \"console.log('\\n🚀 DL Organizer installed!\\n\\nNext steps:\\n1. Run: bun run setup\\n2. Start: bun run dev\\n3. Visit: http://localhost:3030\\n')\""}, "keywords": ["driver-license", "ocr", "image-processing", "document-management"], "author": "<PERSON>", "license": "MIT", "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toggle": "^1.1.9", "axios": "^1.6.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "compression": "^1.8.0", "concurrently": "^8.2.0", "cors": "^2.8.5", "events": "^3.3.0", "express": "^4.18.2", "express-rate-limit": "^8.0.1", "form-data": "^4.0.4", "framer-motion": "^11.0.0", "fuse.js": "^7.1.0", "helmet": "^8.1.0", "keytar": "^7.9.0", "lodash": "^4.17.21", "lucide-react": "^0.400.0", "morgan": "^1.10.1", "multer": "^1.4.5-lts.1", "next": "^14.2.30", "node-fetch": "^2.7.0", "node-windows": "^1.0.0-beta.8", "openai": "^4.20.0", "puppeteer": "^24.14.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "react-icons": "^5.5.0", "react-window": "^1.8.11", "sharp": "^0.33.0", "sqlite3": "^5.1.6", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.6", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.22.4", "zustand": "^4.4.1"}, "devDependencies": {"@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/cors": "^2.8.17", "@types/express": "^4.17.20", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.20", "@types/multer": "^1.4.8", "@types/node": "^20.8.0", "@types/react": "^18.2.31", "@types/react-dom": "^18.2.14", "@types/react-window": "^1.8.8", "@types/sqlite3": "^3.1.9", "@types/uuid": "^9.0.7", "autoprefixer": "^10.4.16", "babel-jest": "^29.7.0", "eslint": "^8.51.0", "eslint-config-next": "14.0.0", "eventsource": "^4.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "nodemon": "^3.0.1", "postcss": "^8.4.31", "typescript": "5.8.3", "supertest": "^7.1.4"}, "engines": {"node": ">=18.0.0"}}