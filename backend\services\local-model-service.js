const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const axios = require('axios');

class LocalModelService {
  constructor() {
    this.models = new Map();
    this.ollamaEndpoint = 'http://localhost:11434';
    this.pythonPath = 'python';
    this.modelConfigs = this.getModelConfigs();
    this.isInitialized = false;
  }

  getModelConfigs() {
    return {
      'llava-1.5-7b': {
        id: 'llava-1.5-7b',
        name: 'LLaVA 1.5 7B',
        type: 'ollama',
        modelName: 'llava:7b',
        vramUsage: 8,
        contextSize: 4096,
        precision: 'fp16',
        requirements: ['ollama'],
        downloadSize: '4.7GB',
        description: 'Efficient multimodal model for RTX 4090',
        accuracy: 0.82,
        avgProcessingTime: 15000
      },
      'llava-1.5-13b': {
        id: 'llava-1.5-13b',
        name: 'LLaVA 1.5 13B',
        type: 'ollama',
        modelName: 'llava:13b',
        vramUsage: 14,
        contextSize: 4096,
        precision: 'fp16',
        requirements: ['ollama'],
        downloadSize: '7.3GB',
        description: 'Higher accuracy multimodal model for RTX 4090',
        accuracy: 0.86,
        avgProcessingTime: 25000
      },
      'qwen-vl-chat': {
        id: 'qwen-vl-chat',
        name: 'Qwen-VL Chat',
        type: 'ollama',
        modelName: 'qwen-vl:chat',
        vramUsage: 12,
        contextSize: 8192,
        precision: 'fp16',
        requirements: ['ollama'],
        downloadSize: '6.1GB',
        description: 'Multilingual vision model with strong OCR capabilities',
        accuracy: 0.85,
        avgProcessingTime: 20000
      },
      'cogvlm-chat': {
        id: 'cogvlm-chat',
        name: 'CogVLM Chat',
        type: 'python',
        modelName: 'THUDM/cogvlm-chat-hf',
        vramUsage: 18,
        contextSize: 2048,
        precision: 'fp16',
        requirements: ['python', 'transformers', 'torch'],
        downloadSize: '9.4GB',
        description: 'Advanced vision-language model via Hugging Face',
        accuracy: 0.88,
        avgProcessingTime: 30000
      },
      'paddleocr': {
        id: 'paddleocr',
        name: 'PaddleOCR',
        type: 'python',
        modelName: 'paddleocr',
        vramUsage: 2,
        contextSize: 0,
        precision: 'fp32',
        requirements: ['python', 'paddlepaddle-gpu', 'paddleocr'],
        downloadSize: '500MB',
        description: 'Specialized OCR toolkit optimized for NVIDIA GPUs',
        accuracy: 0.91,
        avgProcessingTime: 3000
      }
    };
  }

  async initialize() {
    if (this.isInitialized) return;

    try {
      // Check system requirements
      await this.checkSystemRequirements();
      
      // Check Ollama availability
      await this.checkOllamaAvailability();
      
      // Check Python environment
      await this.checkPythonEnvironment();
      
      // Scan for installed models
      await this.scanInstalledModels();
      
      this.isInitialized = true;
      console.log('Local model service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize local model service:', error);
      throw error;
    }
  }

  async checkSystemRequirements() {
    // Check NVIDIA GPU availability
    try {
      const { stdout } = await this.execCommand('nvidia-smi --query-gpu=name,memory.total --format=csv,noheader');
      const gpuInfo = stdout.trim().split('\n')[0];
      console.log('GPU detected:', gpuInfo);
      
      // Check if RTX 4090 or similar high-end GPU
      if (gpuInfo.includes('RTX 4090') || gpuInfo.includes('RTX 40')) {
        console.log('High-end GPU detected - optimal for local models');
      }
    } catch (error) {
      console.warn('NVIDIA GPU not detected or nvidia-smi not available');
    }
  }

  async checkOllamaAvailability() {
    try {
      const response = await axios.get(`${this.ollamaEndpoint}/api/version`, {
        timeout: 5000
      });
      console.log('Ollama available:', response.data.version);
      return true;
    } catch (error) {
      console.log('Ollama not available - install from https://ollama.ai');
      return false;
    }
  }

  async checkPythonEnvironment() {
    try {
      const { stdout } = await this.execCommand('python --version');
      console.log('Python version:', stdout.trim());
      
      // Check for required packages
      const requiredPackages = ['torch', 'transformers', 'paddleocr'];
      const installedPackages = [];
      
      for (const pkg of requiredPackages) {
        try {
          await this.execCommand(`python -c "import ${pkg}; print('${pkg} installed')"`);
          installedPackages.push(pkg);
        } catch (error) {
          console.log(`${pkg} not installed`);
        }
      }
      
      return installedPackages;
    } catch (error) {
      console.warn('Python not available');
      return [];
    }
  }

  async scanInstalledModels() {
    // Check Ollama models
    try {
      const response = await axios.get(`${this.ollamaEndpoint}/api/tags`);
      const installedModels = response.data.models || [];
      
      for (const model of installedModels) {
        const configKey = this.findModelConfigByName(model.name);
        if (configKey) {
          this.models.set(configKey, {
            ...this.modelConfigs[configKey],
            isInstalled: true,
            size: model.size,
            modified: model.modified_at
          });
        }
      }
    } catch (error) {
      console.log('Could not scan Ollama models');
    }
  }

  findModelConfigByName(ollamaName) {
    for (const [key, config] of Object.entries(this.modelConfigs)) {
      if (config.type === 'ollama' && config.modelName === ollamaName) {
        return key;
      }
    }
    return null;
  }

  async installModel(modelId) {
    const config = this.modelConfigs[modelId];
    if (!config) {
      throw new Error(`Model ${modelId} not found`);
    }

    if (config.type === 'ollama') {
      return await this.installOllamaModel(config);
    } else if (config.type === 'python') {
      return await this.installPythonModel(config);
    }
  }

  async installOllamaModel(config) {
    try {
      console.log(`Installing Ollama model: ${config.modelName}`);
      
      // Start pull command
      const pullProcess = spawn('ollama', ['pull', config.modelName], {
        stdio: 'pipe'
      });

      let output = '';
      let error = '';

      pullProcess.stdout.on('data', (data) => {
        output += data.toString();
        // Parse download progress if needed
        const progressMatch = data.toString().match(/(\d+)%/);
        if (progressMatch) {
          console.log(`Download progress: ${progressMatch[1]}%`);
        }
      });

      pullProcess.stderr.on('data', (data) => {
        error += data.toString();
      });

      return new Promise((resolve, reject) => {
        pullProcess.on('close', (code) => {
          if (code === 0) {
            console.log(`Model ${config.modelName} installed successfully`);
            this.models.set(config.id, {
              ...config,
              isInstalled: true,
              installedAt: new Date().toISOString()
            });
            resolve(true);
          } else {
            reject(new Error(`Failed to install model: ${error}`));
          }
        });
      });
    } catch (error) {
      throw new Error(`Failed to install Ollama model: ${error.message}`);
    }
  }

  async installPythonModel(config) {
    // Create Python script for model installation
    const installScript = this.generatePythonInstallScript(config);
    const scriptPath = path.join(__dirname, 'temp_install.py');
    
    try {
      await fs.writeFile(scriptPath, installScript);
      
      const { stdout, stderr } = await this.execCommand(`python ${scriptPath}`, {
        timeout: 300000 // 5 minutes
      });
      
      console.log('Python model installation output:', stdout);
      
      this.models.set(config.id, {
        ...config,
        isInstalled: true,
        installedAt: new Date().toISOString()
      });
      
      return true;
    } catch (error) {
      throw new Error(`Failed to install Python model: ${error.message}`);
    } finally {
      // Cleanup temp script
      try {
        await fs.unlink(scriptPath);
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  }

  generatePythonInstallScript(config) {
    if (config.id === 'cogvlm-chat') {
      return `
import os
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM

print("Installing CogVLM model...")
model_name = "${config.modelName}"
cache_dir = "./models/cogvlm"

try:
    tokenizer = AutoTokenizer.from_pretrained(model_name, cache_dir=cache_dir)
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        cache_dir=cache_dir,
        torch_dtype=torch.float16,
        device_map="auto"
    )
    print("CogVLM model installed successfully")
except Exception as e:
    print(f"Error installing CogVLM: {e}")
    exit(1)
`;
    } else if (config.id === 'paddleocr') {
      return `
import os
import paddleocr

print("Installing PaddleOCR...")
try:
    # Initialize PaddleOCR (this will download models)
    ocr = paddleocr.PaddleOCR(use_gpu=True, lang='en')
    print("PaddleOCR installed successfully")
except Exception as e:
    print(f"Error installing PaddleOCR: {e}")
    exit(1)
`;
    }
    
    return 'print("No installation script available for this model")';
  }

  async processImage(imageData, modelId, options = {}) {
    const model = this.models.get(modelId);
    if (!model) {
      throw new Error(`Model ${modelId} not found`);
    }

    if (!model.isInstalled) {
      throw new Error(`Model ${modelId} not installed`);
    }

    const startTime = Date.now();

    try {
      let result;
      if (model.type === 'ollama') {
        result = await this.processWithOllama(imageData, model, options);
      } else if (model.type === 'python') {
        result = await this.processWithPython(imageData, model, options);
      }

      return {
        success: true,
        result,
        processingTime: Date.now() - startTime,
        modelUsed: modelId
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        processingTime: Date.now() - startTime,
        modelUsed: modelId
      };
    }
  }

  async processWithOllama(imageData, model, options) {
    // Convert image to base64
    const base64Image = imageData.toString('base64');
    
    const prompt = this.buildOCRPrompt(options.extractionType || 'driver_license');
    
    const response = await axios.post(`${this.ollamaEndpoint}/api/generate`, {
      model: model.modelName,
      prompt: prompt,
      images: [base64Image],
      format: 'json',
      stream: false,
      options: {
        temperature: 0.1,
        top_p: 0.9,
        num_predict: 1500
      }
    });

    return this.parseOCRResponse(response.data.response, model.id);
  }

  async processWithPython(imageData, model, options) {
    // Create temporary image file
    const tempImagePath = path.join(__dirname, 'temp_image.jpg');
    await fs.writeFile(tempImagePath, imageData);

    const processingScript = this.generatePythonProcessingScript(model, tempImagePath, options);
    const scriptPath = path.join(__dirname, 'temp_process.py');

    try {
      await fs.writeFile(scriptPath, processingScript);
      
      const { stdout, stderr } = await this.execCommand(`python ${scriptPath}`, {
        timeout: 60000 // 1 minute
      });

      if (stderr) {
        console.error('Python processing error:', stderr);
      }

      return this.parseOCRResponse(stdout, model.id);
    } finally {
      // Cleanup
      try {
        await fs.unlink(tempImagePath);
        await fs.unlink(scriptPath);
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  }

  generatePythonProcessingScript(model, imagePath, options) {
    if (model.id === 'paddleocr') {
      return `
import paddleocr
import json
import cv2
import numpy as np

try:
    # Initialize PaddleOCR
    ocr = paddleocr.PaddleOCR(use_gpu=True, lang='en')
    
    # Read image
    image = cv2.imread('${imagePath}')
    
    # Perform OCR
    result = ocr.ocr(image, cls=True)
    
    # Extract text
    all_text = []
    for line in result:
        for word_info in line:
            all_text.append(word_info[1][0])
    
    raw_text = ' '.join(all_text)
    
    # Basic driver's license parsing
    ocr_result = {
        "firstName": "",
        "lastName": "",
        "dateOfBirth": "",
        "address": "",
        "licenseNumber": "",
        "expirationDate": "",
        "issueDate": "",
        "state": "",
        "confidence": 0.8,
        "rawText": raw_text
    }
    
    # Simple pattern matching for common fields
    import re
    
    # Look for license number patterns
    license_patterns = [
        r'[A-Z]\\d{7,12}',  # Common license format
        r'\\d{8,12}',       # Numeric license
        r'[A-Z]{1,2}\\d{6,10}[A-Z]?'  # Mixed format
    ]
    
    for pattern in license_patterns:
        match = re.search(pattern, raw_text)
        if match:
            ocr_result["licenseNumber"] = match.group()
            break
    
    # Look for date patterns
    date_patterns = [
        r'\\d{2}/\\d{2}/\\d{4}',  # MM/DD/YYYY
        r'\\d{2}-\\d{2}-\\d{4}',  # MM-DD-YYYY
        r'\\d{4}-\\d{2}-\\d{2}'   # YYYY-MM-DD
    ]
    
    dates_found = []
    for pattern in date_patterns:
        matches = re.findall(pattern, raw_text)
        dates_found.extend(matches)
    
    if len(dates_found) >= 2:
        ocr_result["dateOfBirth"] = dates_found[0]
        ocr_result["expirationDate"] = dates_found[1]
    
    print(json.dumps(ocr_result))
    
except Exception as e:
    error_result = {
        "firstName": "",
        "lastName": "",
        "dateOfBirth": "",
        "address": "",
        "licenseNumber": "",
        "expirationDate": "",
        "issueDate": "",
        "state": "",
        "confidence": 0.0,
        "rawText": f"Error: {str(e)}"
    }
    print(json.dumps(error_result))
`;
    }
    
    return 'print("{\\"error\\": \\"No processing script available\\"}");';
  }

  buildOCRPrompt(extractionType = 'driver_license') {
    return `
Analyze this driver's license image and extract the following information. Return ONLY a JSON object with this exact structure:

{
  "firstName": "extracted first name",
  "lastName": "extracted last name",
  "dateOfBirth": "YYYY-MM-DD format",
  "address": "full address",
  "licenseNumber": "license number",
  "expirationDate": "YYYY-MM-DD format",
  "issueDate": "YYYY-MM-DD format",
  "state": "two-letter state code",
  "confidence": 0.8,
  "rawText": "all visible text"
}

Important: Return only the JSON object, no additional text or explanations.
`;
  }

  parseOCRResponse(responseText, modelId) {
    try {
      const parsed = JSON.parse(responseText);
      
      // Add metadata
      parsed.processingTime = Date.now();
      parsed.modelUsed = modelId;
      
      return parsed;
    } catch (error) {
      console.error('Failed to parse OCR response:', error);
      return {
        firstName: '',
        lastName: '',
        dateOfBirth: '',
        address: '',
        licenseNumber: '',
        expirationDate: '',
        issueDate: '',
        state: '',
        confidence: 0.0,
        rawText: responseText,
        processingTime: Date.now(),
        modelUsed: modelId
      };
    }
  }

  async execCommand(command, options = {}) {
    const { spawn } = require('child_process');
    const timeout = options.timeout || 30000;
    
    return new Promise((resolve, reject) => {
      const [cmd, ...args] = command.split(' ');
      const child = spawn(cmd, args, { stdio: 'pipe' });
      
      let stdout = '';
      let stderr = '';
      
      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });
      
      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });
      
      const timer = setTimeout(() => {
        child.kill('SIGTERM');
        reject(new Error(`Command timed out after ${timeout}ms`));
      }, timeout);
      
      child.on('close', (code) => {
        clearTimeout(timer);
        if (code === 0) {
          resolve({ stdout, stderr });
        } else {
          reject(new Error(`Command failed with code ${code}: ${stderr}`));
        }
      });
    });
  }

  getInstalledModels() {
    return Array.from(this.models.values()).filter(model => model.isInstalled);
  }

  getAvailableModels() {
    return Object.values(this.modelConfigs);
  }

  async uninstallModel(modelId) {
    const model = this.models.get(modelId);
    if (!model) {
      throw new Error(`Model ${modelId} not found`);
    }

    if (model.type === 'ollama') {
      await this.execCommand(`ollama rm ${model.modelName}`);
    }
    
    this.models.delete(modelId);
    return true;
  }

  getSystemInfo() {
    return {
      ollamaAvailable: this.ollamaAvailable,
      pythonAvailable: this.pythonAvailable,
      installedModels: this.getInstalledModels().length,
      availableModels: this.getAvailableModels().length,
      totalVRAMUsed: this.calculateVRAMUsage()
    };
  }

  calculateVRAMUsage() {
    return this.getInstalledModels().reduce((total, model) => {
      return total + model.vramUsage;
    }, 0);
  }
}

module.exports = LocalModelService;