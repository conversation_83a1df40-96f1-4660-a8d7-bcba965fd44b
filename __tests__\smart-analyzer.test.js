/**
 * Smart Filter Analyzer Tests
 * Tests for the Smart Filter Analyzer functionality
 */

const { buildAnalysisManifest, generateFilterChips } = require('../backend/utils/build-manifest');
const { detectLicenseSide } = require('../backend/utils/detect-license-side');

describe('Smart Filter Analyzer', () => {
  describe('buildAnalysisManifest', () => {
    test('should return empty manifest for null stats', () => {
      const result = buildAnalysisManifest(null);
      
      expect(result).toEqual({
        sideCounts: {},
        filenameClusters: [],
        sizeBuckets: [],
        resBuckets: [],
        cachedPct: 0,
        totalImages: 0,
        imageIds: [],
        imageSides: {}
      });
    });

    test('should convert Maps to arrays correctly', () => {
      const mockStats = {
        sideCounts: { front: 5, back: 3, selfie: 2, unknown: 1 },
        filenameClusters: new Map([
          ['gen-1', 10],
          ['dl', 8],
          ['license', 5]
        ]),
        sizeBuckets: new Map([
          ['< 100KB', 2],
          ['100KB - 300KB', 5],
          ['500KB - 1MB', 3]
        ]),
        resBuckets: new Map([
          ['< 1MP', 1],
          ['2-5MP', 7],
          ['> 12MP', 2]
        ]),
        imageIds: ['img1', 'img2', 'img3', 'img4', 'img5'],
        imageSides: new Map([
          ['img1', 'front'],
          ['img2', 'back']
        ])
      };

      const result = buildAnalysisManifest(mockStats);

      expect(result.sideCounts).toEqual({
        front: 5,
        back: 3,
        selfie: 2,
        unknown: 1
      });

      expect(result.filenameClusters).toEqual([
        { token: 'gen-1', count: 10 },
        { token: 'dl', count: 8 },
        { token: 'license', count: 5 }
      ]);

      expect(result.sizeBuckets).toHaveLength(3);
      expect(result.sizeBuckets[0]).toEqual({ label: '< 100KB', count: 2 });

      expect(result.resBuckets).toHaveLength(3);
      expect(result.totalImages).toBe(5);
      expect(result.imageIds).toEqual(['img1', 'img2', 'img3', 'img4', 'img5']);
      expect(result.imageSides).toEqual({ img1: 'front', img2: 'back' });
    });

    test('should sort filename clusters by count descending', () => {
      const mockStats = {
        sideCounts: {},
        filenameClusters: new Map([
          ['small', 3],
          ['large', 10],
          ['medium', 7]
        ]),
        sizeBuckets: new Map(),
        resBuckets: new Map(),
        imageIds: []
      };

      const result = buildAnalysisManifest(mockStats);

      expect(result.filenameClusters).toEqual([
        { token: 'large', count: 10 },
        { token: 'medium', count: 7 },
        { token: 'small', count: 3 }
      ]);
    });

    test('should sort size buckets by logical order', () => {
      const mockStats = {
        sideCounts: {},
        filenameClusters: new Map(),
        sizeBuckets: new Map([
          ['> 5MB', 2],
          ['< 100KB', 5],
          ['1MB - 2MB', 3]
        ]),
        resBuckets: new Map(),
        imageIds: []
      };

      const result = buildAnalysisManifest(mockStats);

      expect(result.sizeBuckets[0].label).toBe('< 100KB');
      expect(result.sizeBuckets[1].label).toBe('1MB - 2MB');
      expect(result.sizeBuckets[2].label).toBe('> 5MB');
    });

    test('should include metadata', () => {
      const mockStats = {
        sideCounts: {},
        filenameClusters: new Map(),
        sizeBuckets: new Map(),
        resBuckets: new Map(),
        imageIds: []
      };

      const result = buildAnalysisManifest(mockStats);

      expect(result.generatedAt).toBeDefined();
      expect(result.version).toBe('1.1.0');
      expect(typeof result.cachedPct).toBe('number');
    });
  });

  describe('generateFilterChips', () => {
    test('should generate chips from manifest', () => {
      const mockManifest = {
        sideCounts: { front: 10, back: 5 },
        filenameClusters: [
          { token: 'gen-1', count: 8 }
        ],
        sizeBuckets: [
          { label: '< 100KB', count: 3 }
        ],
        resBuckets: [
          { label: '2-5MP', count: 6 }
        ]
      };

      const chips = generateFilterChips(mockManifest);

      expect(chips).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            label: '10 fronts',
            type: 'side',
            predicate: { kind: 'side', value: 'front' }
          }),
          expect.objectContaining({
            label: '5 backs',
            type: 'side',
            predicate: { kind: 'side', value: 'back' }
          }),
          expect.objectContaining({
            label: 'token:gen-1 (8)',
            type: 'filename',
            predicate: { kind: 'filename-contains', text: 'gen-1' }
          })
        ])
      );
    });

    test('should filter out filename clusters with count < 5', () => {
      const mockManifest = {
        sideCounts: {},
        filenameClusters: [
          { token: 'small', count: 3 },  // Should be filtered out
          { token: 'large', count: 10 }  // Should be included
        ],
        sizeBuckets: [],
        resBuckets: []
      };

      const chips = generateFilterChips(mockManifest);
      const filenameChips = chips.filter(chip => chip.type === 'filename');

      expect(filenameChips).toHaveLength(1);
      expect(filenameChips[0].label).toBe('token:large (10)');
    });

    test('should sort chips by count descending', () => {
      const mockManifest = {
        sideCounts: { front: 5, back: 10 },  // back > front
        filenameClusters: [
          { token: 'small', count: 8 },
          { token: 'large', count: 15 }
        ],
        sizeBuckets: [],
        resBuckets: []
      };

      const chips = generateFilterChips(mockManifest);

      // Should be sorted by count descending
      expect(chips[0].count).toBeGreaterThanOrEqual(chips[1].count);
    });
  });

  describe('detectLicenseSide', () => {
    // Mock OCR service for testing
    const mockOCRService = {
      processImage: jest.fn()
    };

    beforeEach(() => {
      mockOCRService.processImage.mockReset();
    });

    test('should detect front side correctly', async () => {
      mockOCRService.processImage.mockResolvedValue({
        success: true,
        result: {
          rawText: 'this is a driver license front with photo'
        }
      });

      const result = await detectLicenseSide(Buffer.from('test'), mockOCRService);
      expect(result).toBe('front');
    });

    test('should detect back side correctly', async () => {
      mockOCRService.processImage.mockResolvedValue({
        success: true,
        result: {
          rawText: 'this is the back of a driver license with barcode'
        }
      });

      const result = await detectLicenseSide(Buffer.from('test'), mockOCRService);
      expect(result).toBe('back');
    });

    test('should detect selfie correctly', async () => {
      mockOCRService.processImage.mockResolvedValue({
        success: true,
        result: {
          rawText: 'person taking a selfie holding their license'
        }
      });

      const result = await detectLicenseSide(Buffer.from('test'), mockOCRService);
      expect(result).toBe('selfie');
    });

    test('should return unknown for unclear images', async () => {
      mockOCRService.processImage.mockResolvedValue({
        success: true,
        result: {
          rawText: 'some random text that does not mention license'
        }
      });

      const result = await detectLicenseSide(Buffer.from('test'), mockOCRService);
      expect(result).toBe('unknown');
    });

    test('should handle OCR service errors', async () => {
      mockOCRService.processImage.mockResolvedValue({
        success: false,
        error: 'OCR failed'
      });

      const result = await detectLicenseSide(Buffer.from('test'), mockOCRService);
      expect(result).toBe('unknown');
    });

    test('should handle OCR service exceptions', async () => {
      mockOCRService.processImage.mockRejectedValue(new Error('Network error'));

      const result = await detectLicenseSide(Buffer.from('test'), mockOCRService);
      expect(result).toBe('unknown');
    });

    test('should require both buffer and OCR service', async () => {
      await expect(detectLicenseSide(null, mockOCRService)).rejects.toThrow();
      await expect(detectLicenseSide(Buffer.from('test'), null)).rejects.toThrow();
    });
  });
});