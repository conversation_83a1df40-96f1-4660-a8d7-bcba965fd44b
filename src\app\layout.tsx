import type { Metadata } from 'next'
import { Roboto } from 'next/font/google'
import './globals.css'
import { ThemeProvider } from '@/components/theme-provider'
import { BrowserCompatibility } from './browser-compatibility'

const roboto = Roboto({ 
  weight: ['300', '400', '500', '700'],
  subsets: ['latin'],
  display: 'swap',
})

export const metadata: Metadata = {
  title: 'DL Organizer - Driver\'s License OCR Processing',
  description: 'Professional driver\'s license image organization and OCR processing with AI-powered data extraction',
  keywords: ['driver license', 'OCR', 'image processing', 'document management', 'AI'],
  authors: [{ name: '<PERSON>' }],
  icons: {
    icon: '/favicon.svg',
    shortcut: '/favicon.svg',
    apple: '/favicon.svg',
  },
  openGraph: {
    title: 'DL Organizer',
    description: 'Professional driver\'s license OCR processing',
    type: 'website',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={roboto.className}>
        <BrowserCompatibility />
        <ThemeProvider>
          <div className="min-h-screen bg-background text-foreground">
            {children}
          </div>
        </ThemeProvider>
      </body>
    </html>
  )
}