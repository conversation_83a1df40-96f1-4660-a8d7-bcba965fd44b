# DL Organizer v2

> 🚀 **AI-Powered Driver's License OCR Processing System**

A comprehensive Next.js + Express.js hybrid application for processing driver's license images with multi-provider OCR capabilities and integrated ReadySearch functionality.

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![Next.js](https://img.shields.io/badge/Next.js-14+-blue.svg)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.8+-blue.svg)](https://www.typescriptlang.org/)

## 🌟 Key Features

### Core Functionality
- **Multi-Provider OCR**: OpenAI GPT-4o, OpenRouter models with intelligent fallback
- **Dual Mode Processing**: US and Australian driver's license support
- **Batch Processing**: Queue-based bulk OCR with progress tracking
- **Smart File Organization**: Automated folder management and naming strategies
- **Real-time Validation**: Model availability checking with caching

### ReadySearch Integration
- **Embedded ReadySearch v3.0**: Full Australian DL database search capabilities
- **Enhanced CLI Tools**: Production-ready command-line interface
- **Browser Automation**: Automated data extraction and validation
- **Direct API Access**: RESTful API for programmatic access

### Advanced Features
- **Image Processing**: Sharp-based thumbnails, rotation, and transformations
- **Cost Tracking**: API usage monitoring with spending limits
- **Windows Optimization**: Native filesystem APIs and drive detection
- **Production Ready**: Health monitoring, logging, and backup systems

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- Windows 10/11 (optimized for Windows)
- Git

### Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/dl-organizer-v2.git
cd dl-organizer-v2

# Install dependencies
npm install

# Start development environment
npm run dev
```

### Smart Development Server

```bash
# Auto-resolve port conflicts and start both servers
npm run dev:smart

# Individual servers
npm run dev:frontend    # Next.js on port 3030
npm run dev:backend     # Express on port 3050
```

## 📋 Available Commands

### Development
```bash
npm run dev                  # Start both frontend and backend
npm run dev:smart           # Smart port management with conflict resolution
npm run build               # Build for production
npm run start               # Start production servers
```

### Testing
```bash
npm run test                # Jest unit tests
npm run test:e2e           # Playwright E2E tests
npm run test:integration   # Integration tests
npm run lint               # ESLint
npm run typecheck          # TypeScript checking
```

### Production & Maintenance
```bash
npm run setup              # Initial production setup
npm run health             # System health check
npm run backup             # Create database backup
npm run maintenance        # Run maintenance tasks
npm run service:install    # Install as Windows service
```

### Utilities
```bash
npm run ports:check        # Check port availability
npm run ports:resolve      # Resolve port conflicts
```

## 🏗️ Architecture

### Technology Stack
- **Frontend**: Next.js 14.2+ with TypeScript 5.8+
- **Backend**: Express.js with comprehensive middleware
- **Database**: SQLite with synchronous API
- **Styling**: Tailwind CSS + Radix UI
- **Image Processing**: Sharp
- **Testing**: Jest + Playwright
- **AI Integration**: Multi-provider OCR system

### Project Structure
```
dl-organizer-v2/
├── src/                    # Next.js frontend
│   ├── components/         # React components
│   ├── app/               # App Router pages and API routes
│   └── types/             # TypeScript definitions
├── backend/               # Express.js backend
│   ├── routes/           # API endpoints
│   ├── services/         # Business logic
│   └── utils/            # Utilities
├── ReadySearch/          # Integrated ReadySearch v3.0
├── tests/               # E2E and integration tests
└── data/                # SQLite database and logs
```

## 🔧 Configuration

### Environment Variables
```bash
# API Keys
OPENAI_API_KEY=your_openai_key
OPENROUTER_API_KEY=your_openrouter_key

# Database
DATABASE_PATH=./data/dl-organizer.db

# Ports (auto-detected if not specified)
FRONTEND_PORT=3030
BACKEND_PORT=3050
```

### OCR Providers
Configure multiple OCR providers with automatic failover:
- OpenAI GPT-4o (primary)
- OpenRouter models (fallback)
- Local models (future support)

## 📊 ReadySearch Integration

DL Organizer v2 includes ReadySearch v3.0 directly integrated:

### Features
- Australian driver's license database search
- Enhanced CLI with production capabilities
- Browser automation for data validation
- REST API for programmatic access

### Usage
```bash
# Navigate to ReadySearch
cd ReadySearch

# Run CLI tool
python readysearch_cli.py --help

# Start API server
python api.py

# Launch GUI
python readysearch_gui.py
```

## 🧪 Testing

### E2E Testing
```bash
# Run all E2E tests
npm run test:e2e

# Run specific test
npm run test:e2e -- --grep "OCR processing"

# Visual testing
npm run test:e2e -- --headed
```

### Test Coverage
- Unit tests for business logic
- Integration tests for API endpoints
- E2E tests for complete workflows
- Visual regression testing

## 🔒 Security

- Input validation and sanitization
- SQL injection prevention
- File system access controls
- API rate limiting
- Secure credential management

## 📈 Production Deployment

### Windows Service Installation
```bash
npm run service:install
```

### Health Monitoring
- Endpoint: `/api/health`
- Database connectivity checks
- Disk space monitoring
- API provider status validation

### Backup Strategy
- Automated SQLite backups
- 30-day retention policy
- Configurable backup schedules

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: See [CLAUDE.md](CLAUDE.md) for detailed development guidance
- **Issues**: Report bugs and feature requests via GitHub Issues
- **Wiki**: Additional documentation and guides

## 🔄 Version History

### v2.0.0 (Current)
- Complete architecture overhaul
- ReadySearch v3.0 integration
- Enhanced Windows optimization
- Production-ready deployment
- Comprehensive testing suite

---

**Built with ❤️ for efficient driver's license processing**