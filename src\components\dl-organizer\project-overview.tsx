"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ThemeToggle } from "@/components/theme-provider";
import {
  Folder,
  FolderOpen,
  Users,
  FileText,
  Image as ImageIcon,
  CheckCircle,
  Clock,
  AlertCircle,
  BarChart3,
  Search,
  Plus,
  ArrowLeft,
  RefreshCw,
  Settings,
  Edit,
  Trash2,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Project } from "@/types";
import FolderPicker from "@/components/ui/folder-picker";

interface ProjectOverview {
  totalFolders: number;
  completedFolders: number;
  pendingFolders: number;
  currentFolders: number;
  totalImages: number;
  totalTextFiles: number;
  foldersByStatus: {
    completed: Array<{ name: string; path: string }>;
    pending: Array<{ name: string; path: string }>;
    current: Array<{ name: string; path: string }>;
    unknown: Array<{ name: string; path: string }>;
  };
}

interface ProjectOverviewProps {
  projects: Project[];
  onProjectCreate: (name: string, rootPath: string) => Promise<void>;
  onProjectSelect: (project: Project) => void;
  onProjectEdit: (project: Project, name: string, rootPath: string) => void;
  onProjectDelete: (projectId: string) => void;
  onBackToProjects: () => void;
}

export default function ProjectOverview({
  projects,
  onProjectCreate,
  onProjectSelect,
  onProjectEdit,
  onProjectDelete,
  onBackToProjects,
}: ProjectOverviewProps) {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [projectName, setProjectName] = useState("");
  const [rootPath, setRootPath] = useState("");
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [overview, setOverview] = useState<ProjectOverview | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [confirmDelete, setConfirmDelete] = useState<string | null>(null);
  const [isCreating, setIsCreating] = useState(false);

  const handleCreateProject = async () => {
    if (projectName.trim() && rootPath.trim()) {
      setIsCreating(true);
      setError(null);
      try {
        await onProjectCreate(projectName.trim(), rootPath.trim());
        setProjectName("");
        setRootPath("");
        setShowCreateForm(false);
      } catch (error) {
        setError(
          error instanceof Error ? error.message : "Failed to create project"
        );
      } finally {
        setIsCreating(false);
      }
    }
  };

  const handleEditProject = (project: Project) => {
    setEditingProject(project);
    setProjectName(project.name);
    setRootPath(project.rootPath);
    setShowEditForm(true);
  };

  const handleSaveEdit = () => {
    if (editingProject && projectName.trim() && rootPath.trim()) {
      onProjectEdit(editingProject, projectName.trim(), rootPath.trim());
      setEditingProject(null);
      setProjectName("");
      setRootPath("");
      setShowEditForm(false);
    }
  };

  const handleDeleteProject = (projectId: string) => {
    onProjectDelete(projectId);
    setConfirmDelete(null);
  };

  const cancelEdit = () => {
    setEditingProject(null);
    setProjectName("");
    setRootPath("");
    setShowEditForm(false);
  };

  const loadProjectOverview = async (project: Project) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(
        "http://localhost:3003/api/filesystem/overview",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ rootPath: project.rootPath }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to load project overview");
      }

      const data = await response.json();
      setOverview(data.overview);
      setSelectedProject(project);
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to load overview"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenProject = (project: Project) => {
    onProjectSelect(project);
  };

  const filteredProjects = projects.filter(
    (project) =>
      project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.rootPath.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (selectedProject && overview) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSelectedProject(null);
                setOverview(null);
              }}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Projects
            </Button>
            <div>
              <h1 className="text-2xl font-bold">{selectedProject.name}</h1>
              <p className="text-sm text-muted-foreground">
                {selectedProject.rootPath}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => loadProjectOverview(selectedProject)}
              disabled={isLoading}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button
              onClick={() => handleOpenProject(selectedProject)}
              variant="info"
            >
              <FolderOpen className="h-4 w-4 mr-2" />
              Open Project
            </Button>
            <ThemeToggle />
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Folder className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Folders</p>
                  <p className="text-2xl font-bold">
                    {overview.totalFolders.toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Completed</p>
                  <p className="text-2xl font-bold">
                    {overview.completedFolders.toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <ImageIcon className="h-5 w-5 text-purple-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Images</p>
                  <p className="text-2xl font-bold">
                    {overview.totalImages.toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-orange-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Text Files</p>
                  <p className="text-2xl font-bold">
                    {overview.totalTextFiles.toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Progress Bar */}
        <Card>
          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Project Progress</span>
                <span className="text-sm text-muted-foreground">
                  {Math.round(
                    (overview.completedFolders / overview.totalFolders) * 100
                  )}
                  % Complete
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-green-500 h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${
                      (overview.completedFolders / overview.totalFolders) * 100
                    }%`,
                  }}
                />
              </div>
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{overview.completedFolders} completed</span>
                <span>{overview.pendingFolders} pending</span>
                <span>{overview.currentFolders} in progress</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Folder Status Breakdown */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                Completed Folders
                <Badge variant="outline">
                  {overview.foldersByStatus.completed.length}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {overview.foldersByStatus.completed
                  .slice(0, 10)
                  .map((folder, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-2 p-2 rounded hover:bg-muted/50"
                    >
                      <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                      <span className="text-sm font-mono truncate">
                        {folder.name}
                      </span>
                    </div>
                  ))}
                {overview.foldersByStatus.completed.length > 10 && (
                  <p className="text-xs text-muted-foreground text-center py-2">
                    +{overview.foldersByStatus.completed.length - 10} more...
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-yellow-500" />
                Pending Folders
                <Badge variant="outline">
                  {overview.foldersByStatus.pending.length}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {overview.foldersByStatus.pending
                  .slice(0, 10)
                  .map((folder, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-2 p-2 rounded hover:bg-muted/50"
                    >
                      <Clock className="h-4 w-4 text-yellow-500 flex-shrink-0" />
                      <span className="text-sm font-mono truncate">
                        {folder.name}
                      </span>
                    </div>
                  ))}
                {overview.foldersByStatus.pending.length > 10 && (
                  <p className="text-xs text-muted-foreground text-center py-2">
                    +{overview.foldersByStatus.pending.length - 10} more...
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">DL Organizer Projects</h1>
          <p className="text-muted-foreground">
            Manage your driver&apos;s license OCR processing projects
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={() => setShowCreateForm(true)} variant="info">
            <Plus className="h-4 w-4 mr-2" />
            New Project
          </Button>
          <ThemeToggle />
        </div>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search projects..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Create Project Form */}
      {showCreateForm && (
        <Card>
          <CardHeader>
            <CardTitle>Create New Project</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            <div>
              <label className="text-sm font-medium">Project Name</label>
              <Input
                value={projectName}
                onChange={(e) => setProjectName(e.target.value)}
                placeholder="e.g., 100x US DL w Selfie"
              />
            </div>
            <div>
              <label className="text-sm font-medium">Root Folder Path</label>
              <FolderPicker
                value={rootPath}
                onChange={setRootPath}
                placeholder="C:\Users\<USER>\Downloads\Telegram Desktop\100x us dl w selfie"
              />
            </div>
            <div className="flex gap-2">
              <Button
                onClick={handleCreateProject}
                disabled={!projectName.trim() || !rootPath.trim() || isCreating}
              >
                {isCreating ? "Creating..." : "Create Project"}
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowCreateForm(false)}
                disabled={isCreating}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Edit Project Form */}
      {showEditForm && editingProject && (
        <Card>
          <CardHeader>
            <CardTitle>Edit Project</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium">Project Name</label>
              <Input
                value={projectName}
                onChange={(e) => setProjectName(e.target.value)}
                placeholder="e.g., 100x US DL w Selfie"
              />
            </div>
            <div>
              <label className="text-sm font-medium">Root Folder Path</label>
              <FolderPicker
                value={rootPath}
                onChange={setRootPath}
                placeholder="C:\Users\<USER>\Downloads\Telegram Desktop\100x us dl w selfie"
              />
            </div>
            <div className="flex gap-2">
              <Button
                onClick={handleSaveEdit}
                disabled={!projectName.trim() || !rootPath.trim()}
              >
                Save Changes
              </Button>
              <Button variant="outline" onClick={cancelEdit}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Delete Confirmation */}
      {confirmDelete && (
        <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950">
          <CardHeader>
            <CardTitle className="text-red-700 dark:text-red-300">
              Delete Project
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-red-600 dark:text-red-400">
              Are you sure you want to delete this project? This action cannot
              be undone.
            </p>
            <div className="flex gap-2">
              <Button
                variant="destructive"
                onClick={() => handleDeleteProject(confirmDelete)}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Project
              </Button>
              <Button variant="outline" onClick={() => setConfirmDelete(null)}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Projects Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredProjects.map((project) => (
          <Card
            key={project.id}
            className="cursor-pointer hover:shadow-lg transition-all"
          >
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex items-start justify-between">
                  <div className="space-y-1 flex-1 min-w-0">
                    <h3 className="font-semibold text-lg truncate">
                      {project.name}
                    </h3>
                    <p
                      className="text-sm text-muted-foreground break-all line-clamp-2"
                      title={project.rootPath}
                    >
                      {project.rootPath}
                    </p>
                  </div>
                  <Folder className="h-6 w-6 text-blue-500 flex-shrink-0" />
                </div>

                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <span>
                    Created {new Date(project.createdAt).toLocaleDateString()}
                  </span>
                  <span>•</span>
                  <span>
                    Updated {new Date(project.updatedAt).toLocaleDateString()}
                  </span>
                </div>

                <div className="flex flex-wrap gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => loadProjectOverview(project)}
                    disabled={isLoading}
                  >
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Overview
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => handleOpenProject(project)}
                    variant="info"
                  >
                    <FolderOpen className="h-4 w-4 mr-2" />
                    Open
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditProject(project);
                    }}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    onClick={(e) => {
                      e.stopPropagation();
                      setConfirmDelete(project.id);
                    }}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredProjects.length === 0 && searchTerm && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No projects match your search</p>
        </div>
      )}

      {projects.length === 0 && !showCreateForm && (
        <div className="text-center py-12">
          <Folder className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Projects Yet</h3>
          <p className="text-muted-foreground mb-4">
            Create your first project to start organizing driver&apos;s license
            images
          </p>
          <Button onClick={() => setShowCreateForm(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create First Project
          </Button>
        </div>
      )}
    </div>
  );
}
