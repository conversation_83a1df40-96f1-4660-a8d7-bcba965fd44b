const fs = require('fs');
const path = require('path');

class Logger {
  constructor(options = {}) {
    this.level = options.level || 'info';
    this.logDir = options.logDir || path.join(__dirname, '../../data/logs');
    this.maxFileSize = options.maxFileSize || 10 * 1024 * 1024; // 10MB
    this.maxFiles = options.maxFiles || 5;
    
    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3
    };
    
    this.currentLevel = this.levels[this.level] || 2;
    this.ensureLogDirectory();
  }
  
  ensureLogDirectory() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }
  
  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const metaStr = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
    return `[${timestamp}] ${level.toUpperCase()}: ${message}${metaStr}\n`;
  }
  
  writeToFile(level, formattedMessage) {
    const filename = level === 'error' ? 'error.log' : 'app.log';
    const filepath = path.join(this.logDir, filename);
    
    try {
      // Check file size and rotate if necessary
      if (fs.existsSync(filepath)) {
        const stats = fs.statSync(filepath);
        if (stats.size > this.maxFileSize) {
          this.rotateLogFile(filepath);
        }
      }
      
      fs.appendFileSync(filepath, formattedMessage);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }
  
  rotateLogFile(filepath) {
    const dir = path.dirname(filepath);
    const ext = path.extname(filepath);
    const basename = path.basename(filepath, ext);
    
    // Rotate existing files
    for (let i = this.maxFiles - 1; i > 0; i--) {
      const oldFile = path.join(dir, `${basename}.${i}${ext}`);
      const newFile = path.join(dir, `${basename}.${i + 1}${ext}`);
      
      if (fs.existsSync(oldFile)) {
        if (i === this.maxFiles - 1) {
          fs.unlinkSync(oldFile); // Delete oldest
        } else {
          fs.renameSync(oldFile, newFile);
        }
      }
    }
    
    // Move current file to .1
    const rotatedFile = path.join(dir, `${basename}.1${ext}`);
    fs.renameSync(filepath, rotatedFile);
  }
  
  log(level, message, meta = {}) {
    if (this.levels[level] <= this.currentLevel) {
      const formatted = this.formatMessage(level, message, meta);
      
      // Always log to console in development
      if (process.env.NODE_ENV !== 'production') {
        const colors = {
          error: '\x1b[31m', // Red
          warn: '\x1b[33m',  // Yellow
          info: '\x1b[36m',  // Cyan
          debug: '\x1b[90m'  // Gray
        };
        console.log(`${colors[level] || ''}${formatted.trim()}\x1b[0m`);
      }
      
      // Write to file
      this.writeToFile(level, formatted);
    }
  }
  
  error(message, meta = {}) {
    this.log('error', message, meta);
  }
  
  warn(message, meta = {}) {
    this.log('warn', message, meta);
  }
  
  info(message, meta = {}) {
    this.log('info', message, meta);
  }
  
  debug(message, meta = {}) {
    this.log('debug', message, meta);
  }
  
  // Express middleware for request logging
  middleware() {
    return (req, res, next) => {
      const start = Date.now();
      
      res.on('finish', () => {
        const duration = Date.now() - start;
        const status = res.statusCode;
        const method = req.method;
        const url = req.originalUrl || req.url;
        const userAgent = req.get('User-Agent') || '';
        
        const level = status >= 400 ? 'error' : 'info';
        this.log(level, `${method} ${url} ${status}`, {
          duration: `${duration}ms`,
          userAgent,
          ip: req.ip
        });
      });
      
      next();
    };
  }
}

// Create singleton instance
const logger = new Logger({
  level: process.env.LOG_LEVEL || 'info',
  logDir: process.env.LOG_DIR || path.join(__dirname, '../../data/logs')
});

module.exports = logger;