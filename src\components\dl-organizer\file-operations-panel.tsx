'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Save, 
  FileText, 
  Edit3, 
  CheckCircle, 
  AlertTriangle, 
  Download,
  Info,
  Zap
} from 'lucide-react'

interface FileOperationsPanelProps {
  imageId: string | null
  imageName?: string
  onOperationComplete?: (operation: string, result: any) => void
  className?: string
}

interface SaveOptions {
  hasOCRResults: boolean
  hasReadySearch: boolean
  readySearchStatus: string
  readySearchMatches: number
  hasTXTFile: boolean
  suggestedFilename?: {
    newPath: string
    basename: string
    readySearchStatus: string
    hasReadySearchData: boolean
    hasMatches: boolean
  }
}

export function FileOperationsPanel({ 
  imageId, 
  imageName = 'image', 
  onOperationComplete,
  className 
}: FileOperationsPanelProps) {
  const [saveOptions, setSaveOptions] = useState<SaveOptions | null>(null)
  const [loading, setLoading] = useState(false)
  const [savingTXT, setSavingTXT] = useState(false)
  const [renaming, setRenaming] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  
  // Rename form state
  const [newBaseName, setNewBaseName] = useState('')
  const [includeReadySearchIndicator, setIncludeReadySearchIndicator] = useState(true)

  const loadSaveOptions = React.useCallback(async () => {
    if (!imageId) return

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`http://localhost:3003/api/file-operations/save-options/${imageId}`)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to load save options')
      }

      const data = await response.json()
      if (data.success) {
        setSaveOptions(data)
      } else {
        throw new Error(data.error || 'Failed to load save options')
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load save options')
    } finally {
      setLoading(false)
    }
  }, [imageId])

  // Load save options when imageId changes
  useEffect(() => {
    if (imageId) {
      loadSaveOptions()
      // Extract base name from image for rename field
      const baseName = imageName.replace(/\.[^/.]+$/, '').replace(/-(rs-m|rs-nm|no-rs)$/i, '')
      setNewBaseName(baseName)
    } else {
      setSaveOptions(null)
    }
  }, [imageId, imageName, loadSaveOptions])

  const handleSaveTXT = async () => {
    if (!imageId) return

    setSavingTXT(true)
    setError(null)
    setSuccess(null)

    try {
      const response = await fetch('http://localhost:3003/api/file-operations/save-txt', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ imageId })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to save TXT file')
      }

      const result = await response.json()
      if (result.success) {
        setSuccess(`TXT file saved successfully${result.hasReadySearch ? ' with ReadySearch results' : ''}`)
        onOperationComplete?.('save-txt', result)
        // Reload save options to update state
        loadSaveOptions()
      } else {
        throw new Error(result.error || 'Failed to save TXT file')
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save TXT file')
    } finally {
      setSavingTXT(false)
    }
  }

  const handleRename = async () => {
    if (!imageId || !newBaseName.trim()) return

    setRenaming(true)
    setError(null)
    setSuccess(null)

    try {
      const response = await fetch('http://localhost:3003/api/file-operations/rename-with-rs-indicator', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          imageId,
          newBaseName: newBaseName.trim(),
          includeReadySearchIndicator
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to rename image')
      }

      const result = await response.json()
      if (result.success) {
        const rsIndicator = result.readySearchStatus !== 'no-data' && includeReadySearchIndicator 
          ? ` (${result.readySearchStatus})` 
          : ''
        setSuccess(`Image renamed to "${result.newBaseName}"${rsIndicator}`)
        onOperationComplete?.('rename', result)
        // Clear rename form
        setNewBaseName('')
      } else {
        throw new Error(result.error || 'Failed to rename image')
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to rename image')
    } finally {
      setRenaming(false)
    }
  }

  const getReadySearchBadge = (status: string, matchCount: number) => {
    switch (status) {
      case 'rs-m':
        return <Badge variant="success">
          {matchCount} matches found
        </Badge>
      case 'rs-nm':
        return <Badge variant="warning">
          No matches found
        </Badge>
      case 'no-rs':
        return <Badge variant="outline">No ReadySearch analysis</Badge>
      default:
        return <Badge variant="outline">Unknown status</Badge>
    }
  }

  if (!imageId) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Save className="h-5 w-5" />
            File Operations
          </CardTitle>
          <CardDescription>
            Save OCR results and rename files with ReadySearch indicators
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            Select an image to view file operations
          </div>
        </CardContent>
      </Card>
    )
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Save className="h-5 w-5" />
            File Operations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            Loading file options...
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Save className="h-5 w-5" />
          File Operations
        </CardTitle>
        <CardDescription>
          Save OCR results and rename files with ReadySearch indicators
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        
        {/* Error/Success Messages */}
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        {saveOptions && (
          <>
            {/* Current Status */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium">Current Status</h4>
              <div className="grid grid-cols-1 gap-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">OCR Results</span>
                  {saveOptions.hasOCRResults ? (
                    <Badge variant="default">Available</Badge>
                  ) : (
                    <Badge variant="secondary">Not available</Badge>
                  )}
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">ReadySearch Status</span>
                  {getReadySearchBadge(saveOptions.readySearchStatus, saveOptions.readySearchMatches)}
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">TXT File</span>
                  {saveOptions.hasTXTFile ? (
                    <Badge variant="outline">Exists</Badge>
                  ) : (
                    <Badge variant="secondary">Not saved</Badge>
                  )}
                </div>
              </div>
            </div>

            <Separator />

            {/* TXT File Saving */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <h4 className="text-sm font-medium">Save as TXT File</h4>
              </div>

              {!saveOptions.hasOCRResults ? (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Run OCR analysis first to save results as TXT file.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="space-y-3">
                  <div className="text-sm text-muted-foreground">
                    Save OCR results in a standardized TXT format
                    {saveOptions.hasReadySearch && ' with integrated ReadySearch analysis'}
                  </div>

                  {saveOptions.hasReadySearch && (
                    <Alert>
                      <Zap className="h-4 w-4" />
                      <AlertDescription>
                        This TXT file will include ReadySearch analysis with {saveOptions.readySearchMatches} matches found.
                      </AlertDescription>
                    </Alert>
                  )}

                  <Button
                    onClick={handleSaveTXT}
                    disabled={savingTXT}
                    className="w-full"
                  >
                    {savingTXT ? (
                      <>
                        <Download className="h-4 w-4 mr-2 animate-spin" />
                        Saving TXT File...
                      </>
                    ) : (
                      <>
                        <FileText className="h-4 w-4 mr-2" />
                        {saveOptions.hasTXTFile ? 'Update TXT File' : 'Save TXT File'}
                      </>
                    )}
                  </Button>
                </div>
              )}
            </div>

            <Separator />

            {/* Rename with ReadySearch Indicators */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Edit3 className="h-4 w-4" />
                <h4 className="text-sm font-medium">Rename with ReadySearch Indicators</h4>
              </div>

              <div className="space-y-3">
                <div>
                  <Label htmlFor="newBaseName">New Base Name</Label>
                  <Input
                    id="newBaseName"
                    value={newBaseName}
                    onChange={(e) => setNewBaseName(e.target.value)}
                    placeholder="Enter new filename (without extension)"
                    className="mt-1"
                  />
                </div>

                {saveOptions.hasOCRResults && saveOptions.readySearchStatus !== 'no-rs' && (
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeReadySearchIndicator"
                      checked={includeReadySearchIndicator}
                      onCheckedChange={(checked) => setIncludeReadySearchIndicator(!!checked)}
                    />
                    <Label htmlFor="includeReadySearchIndicator" className="text-sm">
                      Include ReadySearch indicator ({saveOptions.readySearchStatus})
                    </Label>
                  </div>
                )}

                {saveOptions.readySearchStatus !== 'no-rs' && (
                  <div className="text-xs text-muted-foreground bg-muted/50 p-3 rounded">
                    <div><strong>ReadySearch Indicators:</strong></div>
                    <div>• rs-m = ReadySearch matches found</div>
                    <div>• rs-nm = ReadySearch processed, no matches</div>
                    <div>Example: &quot;john_smith-rs-m.jpg&quot;</div>
                  </div>
                )}

                {newBaseName && (
                  <div className="text-sm text-muted-foreground">
                    Preview: <code>
                      {newBaseName}
                      {saveOptions.readySearchStatus !== 'no-rs' && includeReadySearchIndicator && `-${saveOptions.readySearchStatus}`}
                      .jpg
                    </code>
                  </div>
                )}

                <Button
                  onClick={handleRename}
                  disabled={renaming || !newBaseName.trim()}
                  variant="outline"
                  className="w-full"
                >
                  {renaming ? (
                    <>
                      <Edit3 className="h-4 w-4 mr-2 animate-spin" />
                      Renaming...
                    </>
                  ) : (
                    <>
                      <Edit3 className="h-4 w-4 mr-2" />
                      Rename Image
                    </>
                  )}
                </Button>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}

export default FileOperationsPanel