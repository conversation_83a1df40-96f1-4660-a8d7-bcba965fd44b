#!/usr/bin/env python3
"""
ReadySearch Advanced GUI – v2.3 (Ultimate)
A single‑file, production‑ready Tkinter application with a compact, modern
interface.  All prior layout bugs are fixed and the code incorporates every
item from the review checklist:
  • opens fully usable at 1366 × 768 without clipped widgets
  • sidebar min‑width enforced & wheel‑scrollable
  • progress bar always visible
  • Roboto→Segoe UI font‑fallback
  • paned‑window sash default width computed from sidebar request width
  • style options wrapped in try/except for Tk < 8.7 compatibility
  • mouse‑wheel support on scrollable sidebar
  • single entry‑point guarded by __main__
"""

from __future__ import annotations

import asyncio
import csv
import json
import sys
import threading
import webbrowser
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, List, Dict, Optional

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, font

# ────────────────────────  Local imports  ───────────────────────────
sys.path.append(str(Path(__file__).parent))
try:
    from config import Config
    from readysearch_automation.input_loader import SearchRecord
    from readysearch_cli import ReadySearchCLI
except Exception as err:  # graceful failure if dev forgot local libs
    tk.Tk().withdraw()
    messagebox.showerror(
        "Dependency error",
        "Required module missing: %s\nEnsure config.py, readysearch_cli.py, "
        "and readysearch_automation/ are next to this file." % err,
    )
    raise SystemExit(1)

# ────────────────────────  Data model  ──────────────────────────────

@dataclass
class GUISearchResult:
    name: str
    status: str
    search_duration: float
    matches_found: int
    exact_matches: int
    partial_matches: int
    match_category: str
    match_reasoning: str
    detailed_results: List[Dict[str, Any]]
    timestamp: str
    birth_year: Optional[int] = None
    error: Optional[str] = None
    total_results_found: Optional[int] = None

# ────────────────────────  Styling  ─────────────────────────────────

class ModernStyle:
    """Encapsulates the colour palette, fonts and ttk style definitions."""

    PALETTE = {
        "primary": "#1E40AF",
        "primary_light": "#3B82F6",
        "primary_dark": "#1E3A8A",
        "secondary": "#059669",
        "secondary_light": "#10B981",
        "success": "#16A34A",
        "success_light": "#22C55E",
        "warning": "#DC2626",
        "warning_light": "#EF4444",
        "danger": "#B91C1C",
        "danger_light": "#DC2626",
        "bg": "#F9FAFB",
        "surface": "#FFFFFF",
        "surface_alt": "#F3F4F6",
        "hover": "#E5E7EB",
        "border": "#D1D5DB",
        "border_focus": "#3B82F6",
        "text": "#111827",
        "text_muted": "#6B7280",
        "header_bg": "#1F2937",
    }

    FONTS: Dict[str, tuple] = {}

    @classmethod
    def _init_fonts(cls) -> None:
        fams = set(font.families())
        base = "Roboto" if "Roboto" in fams else "Segoe UI"
        cls.FONTS = {
            "title": (base, 14, "bold"),
            "subtitle": (base, 11),
            "heading": (base, 11, "bold"),
            "body": (base, 9),
            "button": (base, 9, "bold"),
            "small": (base, 8),
            "mono": ("Consolas", 9),
        }

    # Style helper that safely applies options not present in old Tk
    @staticmethod
    def _safe_config(style: ttk.Style, name: str, *, map: dict | None = None, **cfg):
        try:
            style.configure(name, **cfg)
        except tk.TclError:
            # strip unsupported keys and try again
            bad = {k for k in cfg if "bordercolor" in k or "focuscolor" in k}
            for k in bad:
                cfg.pop(k)
            style.configure(name, **cfg)
        if map:
            try:
                style.map(name, **map)
            except tk.TclError:
                pass  # ignore unsupported map opts

    @classmethod
    def init(cls, root: tk.Tk) -> None:
        root.configure(bg=cls.PALETTE["bg"])
        cls._init_fonts()
        style = ttk.Style()
        style.theme_use("clam")

        # Buttons
        btns = {
            "Primary": "primary",
            "Success": "success",
            "Warning": "warning",
            "Danger": "danger",
            "Secondary": "secondary",
        }
        for k, col in btns.items():
            cls._safe_config(
                style,
                f"{k}.TButton",
                font=cls.FONTS["button"],
                padding=(8, 4),
                relief="flat",
                background=cls.PALETTE[col],
                foreground="white",
                borderwidth=1,
                map={
                    "background": [
                        ("pressed", cls.PALETTE.get(f"{col}_dark", cls.PALETTE[col])),
                        ("active", cls.PALETTE.get(f"{col}_light", cls.PALETTE[col])),
                    ]
                },
            )

        # Frames & labels
        style.configure("Card.TFrame", background=cls.PALETTE["surface"], borderwidth=1, relief="solid")
        style.configure("Header.TFrame", background=cls.PALETTE["header_bg"])
        style.configure("Heading.TLabel", background=cls.PALETTE["surface"], foreground=cls.PALETTE["text"], font=cls.FONTS["heading"])
        style.configure("Body.TLabel", background=cls.PALETTE["surface"], foreground=cls.PALETTE["text"], font=cls.FONTS["body"])
        style.configure("Muted.TLabel", background=cls.PALETTE["surface"], foreground=cls.PALETTE["text_muted"], font=cls.FONTS["small"])

        # Entry & checkbutton
        cls._safe_config(
            style,
            "Modern.TEntry",
            font=cls.FONTS["body"],
            fieldbackground=cls.PALETTE["surface"],
            borderwidth=1,
            padding=(8, 6),
            relief="solid",
        )
        style.configure("Modern.TCheckbutton", background=cls.PALETTE["surface"], foreground=cls.PALETTE["text"], font=cls.FONTS["body"])

        # LabelFrame
        style.configure("Modern.TLabelframe", background=cls.PALETTE["surface"], borderwidth=1, relief="solid")
        style.configure("Modern.TLabelframe.Label", background=cls.PALETTE["surface"], foreground=cls.PALETTE["text"], font=cls.FONTS["heading"])

        # Notebook + Treeview
        style.configure("Modern.TNotebook", background=cls.PALETTE["surface"], borderwidth=0)
        style.configure("Modern.TNotebook.Tab", padding=(10, 4), font=cls.FONTS["body"], background=cls.PALETTE["surface_alt"], foreground=cls.PALETTE["text_muted"])
        style.map("Modern.TNotebook.Tab", background=[("selected", cls.PALETTE["primary"]), ("active", cls.PALETTE["hover"])], foreground=[("selected", "white")])
        style.configure("Modern.Treeview", background=cls.PALETTE["surface"], fieldbackground=cls.PALETTE["surface"], foreground=cls.PALETTE["text"], rowheight=28, font=cls.FONTS["body"], borderwidth=1, relief="solid")
        style.configure("Modern.Treeview.Heading", background=cls.PALETTE["surface_alt"], foreground=cls.PALETTE["text"], font=cls.FONTS["heading"], borderwidth=1)

        # Text defaults
        root.option_add("*Text.font", cls.FONTS["mono"])
        root.option_add("*Text.background", cls.PALETTE["surface"])
        root.option_add("*Text.foreground", cls.PALETTE["text"])
        root.option_add("*Text.insertBackground", cls.PALETTE["text"])
        root.option_add("*Text.selectBackground", cls.PALETTE["primary_light"])

# ────────────────────────  GUI  ────────────────────────────────────

class ReadySearchGUI:
    def __init__(self) -> None:
        self.root = tk.Tk()
        self.root.withdraw()  # hide until style + geometry set to avoid flicker
        self.cli = ReadySearchCLI(enable_rich=False)
        self.results: List[GUISearchResult] = []
        self.config = Config.get_config()

        self._setup_window()
        ModernStyle.init(self.root)
        self._build_ui()
        self.root.deiconify()

    # ─────────────────────  window & layout  ──────────────────────
    def _setup_window(self) -> None:
        sw, sh = self.root.winfo_screenwidth(), self.root.winfo_screenheight()
        w, h = max(int(sw * 0.6), 1100), max(int(sh * 0.6), 700)
        x, y = (sw - w) // 2, (sh - h) // 2
        self.root.geometry(f"{w}x{h}+{x}+{y}")
        self.root.minsize(1100, 700)
        self.root.title("ReadySearch Advanced GUI")
        try:
            self.root.iconbitmap("icon.ico")
        except tk.TclError:
            pass

    def _build_ui(self) -> None:
        self._header()
        self._panes()
        self._progress()
        self._status()

    # ─────────────────────  header  ───────────────────────────────
    def _header(self):
        frame = ttk.Frame(self.root, style="Header.TFrame", height=55)
        frame.pack(fill=tk.X)
        frame.pack_propagate(False)
        ttk.Label(frame, text="🔍 ReadySearch GUI", style="Heading.TLabel", foreground="white", background=ModernStyle.PALETTE["header_bg"], font=ModernStyle.FONTS["title"]).pack(pady=(6, 0))
        ttk.Label(frame, text="Professional Name Search & Export", style="Body.TLabel", foreground="white", background=ModernStyle.PALETTE["header_bg"], font=ModernStyle.FONTS["subtitle"]).pack()

    # ─────────────────────  main panes  ───────────────────────────
    def _panes(self):
        top = ttk.Frame(self.root, style="Card.TFrame")
        top.pack(fill=tk.BOTH, expand=True, padx=10, pady=(10, 0))

        self.paned = tk.PanedWindow(top, orient=tk.HORIZONTAL, bg=ModernStyle.PALETTE["bg"], sashwidth=8, borderwidth=0)
        self.paned.pack(fill=tk.BOTH, expand=True)

        self._sidebar()
        self._results()

        # place sash after Idle loop so widths are known
        self.root.after(100, lambda: self.paned.sash_place(0, self.sidebar.winfo_reqwidth(), 0))

    # helper to bind wheel to canvas scroll
    @staticmethod
    def _bind_mousewheel(widget: tk.Widget, target_canvas: tk.Canvas):
        def _on(e):
            target_canvas.yview_scroll(int(-1 * (e.delta / 120)), "units")
        widget.bind("<Enter>", lambda _: target_canvas.bind_all("<MouseWheel>", _on))
        widget.bind("<Leave>", lambda _: target_canvas.unbind_all("<MouseWheel>"))

    def _sidebar(self):
        self.sidebar = ttk.Frame(self.paned, style="Card.TFrame", width=380)
        self.paned.add(self.sidebar, minsize=360)

        # title
        ttk.Label(self.sidebar, text="Search Configuration", style="Heading.TLabel").pack(anchor="w", padx=15, pady=(15, 5))

        # scrollable area
        canvas = tk.Canvas(self.sidebar, bg=ModernStyle.PALETTE["surface"], highlightthickness=0)
        vs = ttk.Scrollbar(self.sidebar, orient="vertical", command=canvas.yview)
        scroll = ttk.Frame(canvas, style="Card.TFrame", padding=15)
        canvas.create_window((0, 0), window=scroll, anchor="nw")
        canvas.configure(yscrollcommand=vs.set)
        vs.pack(side="right", fill="y")
        canvas.pack(side="left", fill="both", expand=True)
        scroll.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        self._bind_mousewheel(scroll, canvas)

        # quick‑add widgets ------------------------------------------------
        qf = ttk.LabelFrame(scroll, text="Quick add", style="Modern.TLabelframe")
        qf.pack(anchor="w", pady=10, ipadx=5, ipady=5)
        qf.configure(width=320)  # Match batch search width
        ttk.Label(qf, text="Name:", style="Body.TLabel").pack(anchor="w", padx=5)
        self.e_name = ttk.Entry(qf, width=30, style="Modern.TEntry")
        self.e_name.pack(anchor="w", pady=3, padx=5)
        ttk.Label(qf, text="Birth year (optional):", style="Body.TLabel").pack(anchor="w", padx=5)
        self.e_year = ttk.Entry(qf, width=6, style="Modern.TEntry")
        self.e_year.pack(anchor="w", pady=3, padx=5)
        bf = ttk.Frame(qf)
        bf.pack(anchor="w", pady=5, padx=5)
        ttk.Button(bf, text="Add", style="Secondary.TButton", command=self._add_name, width=8).pack(side="left", padx=(0, 5))
        ttk.Button(bf, text="Load test", style="Warning.TButton", command=self._load_test, width=10).pack(side="left")

        # options ----------------------------------------------------------
        opt = ttk.LabelFrame(scroll, text="Search options", style="Modern.TLabelframe")
        opt.pack(anchor="w", pady=10, ipadx=5, ipady=5)
        opt.configure(width=320)  # Match batch search width
        self.exact_var = tk.BooleanVar()
        ttk.Checkbutton(opt, text="Require EXACT first‑name match", variable=self.exact_var, style="Modern.TCheckbutton").pack(anchor="w", padx=5, pady=5)

        # batch ------------------------------------------------------------
        bat = ttk.LabelFrame(scroll, text="Batch search", style="Modern.TLabelframe")
        bat.pack(anchor="w", fill=tk.Y, expand=True, ipadx=5, ipady=5)
        bat.configure(width=320)  # Match other panels width
        ttk.Label(bat, text="One per line or ';' separated", style="Muted.TLabel").pack(anchor="w", padx=5)
        tf = ttk.Frame(bat)
        tf.pack(anchor="w", pady=5, padx=5)
        sb = ttk.Scrollbar(tf, orient="vertical")
        self.t_batch = tk.Text(tf, height=6, width=40, yscrollcommand=sb.set, font=ModernStyle.FONTS["body"], relief="solid", borderwidth=1)
        sb.config(command=self.t_batch.yview)
        sb.pack(side="right", fill="y")
        self.t_batch.pack(side="left")
        self.t_batch.insert("end", "Andro Cutuk,1975\nAnthony Bek,1993\nGhafoor Jaggi Nadery,1978")
        bf2 = ttk.Frame(bat)
        bf2.pack(anchor="w", pady=5, padx=5)
        ttk.Button(bf2, text="Start", style="Primary.TButton", command=self._batch_search, width=8).pack(side="left", padx=(0, 5))
        ttk.Button(bf2, text="Clear", style="Secondary.TButton", command=self._clear_batch, width=8).pack(side="left", padx=(0, 5))
        ttk.Button(bf2, text="Load file", style="Secondary.TButton", command=self._load_file, width=10).pack(side="left")

    # ─────────────────────  results pane  ──────────────────────────
    def _results(self):
        self.res_frame = ttk.Frame(self.paned, style="Card.TFrame")
        self.paned.add(self.res_frame, minsize=450)

        ctl = ttk.Frame(self.res_frame, style="Card.TFrame")
        ctl.pack(fill=tk.X, padx=15, pady=10)
        ttk.Label(ctl, text="Export:", style="Heading.TLabel").pack(side="left")
        for ext in ("json", "csv", "txt"):
            ttk.Button(ctl, text=ext.upper(), style="Success.TButton", command=lambda e=ext: self.export_results(e)).pack(side="left", padx=2)
        ttk.Button(ctl, text="Clear", style="Danger.TButton", command=self._clear_results).pack(side="right")

        nb = ttk.Notebook(self.res_frame, style="Modern.TNotebook")
        nb.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 15))

        # summary tab
        sumf = ttk.Frame(nb, style="Card.TFrame")
        nb.add(sumf, text="Summary")
        tv_container = ttk.Frame(sumf, style="Card.TFrame")
        tv_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        vs = ttk.Scrollbar(tv_container, orient="vertical")
        hs = ttk.Scrollbar(tv_container, orient="horizontal")
        cols = ("Name", "Match_Summary", "Duration", "Date_of_Birth", "Status")
        self.tv = ttk.Treeview(tv_container, columns=cols, show="headings", style="Modern.Treeview", yscrollcommand=vs.set, xscrollcommand=hs.set)
        vs.config(command=self.tv.yview)
        hs.config(command=self.tv.xview)
        vs.pack(side="right", fill="y")
        hs.pack(side="bottom", fill="x")
        self.tv.pack(fill=tk.BOTH, expand=True)
        heads = {
            "Name": "👤 Name",
            "Match_Summary": "🎯 Match Summary",
            "Duration": "⏱ Duration",
            "Date_of_Birth": "📅 Date of Birth",
            "Status": "📊 Status",
        }
        for c in cols:
            self.tv.heading(c, text=heads[c])
            if c == "Name":
                self.tv.column(c, width=200, minwidth=150)
            elif c == "Match_Summary":
                self.tv.column(c, width=160, minwidth=130)
            elif c == "Duration":
                self.tv.column(c, width=100, minwidth=80)
            elif c == "Date_of_Birth":
                self.tv.column(c, width=120, minwidth=100)
            elif c == "Status":
                self.tv.column(c, width=100, minwidth=80)
            else:
                self.tv.column(c, width=120, minwidth=80)

        # detailed tab
        detf = ttk.Frame(nb, style="Card.TFrame")
        nb.add(detf, text="Detailed")
        vs2 = ttk.Scrollbar(detf, orient="vertical")
        self.t_detail = tk.Text(detf, wrap="word", yscrollcommand=vs2.set, font=ModernStyle.FONTS["mono"], relief="solid", borderwidth=1)
        vs2.config(command=self.t_detail.yview)
        vs2.pack(side="right", fill="y")
        self.t_detail.pack(fill=tk.BOTH, expand=True)

    # ─────────────────────  progress & status  ─────────────────────
    def _progress(self):
        self.f_prog = ttk.Frame(self.root, style="Card.TFrame")
        self.f_prog.pack(fill=tk.X, padx=10, pady=5)
        ttk.Label(self.f_prog, text="Search progress", style="Heading.TLabel").pack(anchor="w")
        self.var_prog = tk.DoubleVar()
        ttk.Progressbar(self.f_prog, variable=self.var_prog, maximum=100).pack(fill=tk.X, pady=4)
        self.var_status = tk.StringVar(value="Ready")
        # Status is now only shown in the bottom combined display

    def _status(self):
        bar = ttk.Frame(self.root, style="Card.TFrame", height=30)
        bar.pack(fill=tk.X, padx=10, pady=(0, 10))
        bar.pack_propagate(False)
        self.var_results = tk.StringVar(value="Results: 0")
        # Combine status and results into a single coherent message
        self.var_combined_status = tk.StringVar(value="Ready - Results: 0")
        ttk.Label(bar, textvariable=self.var_combined_status, style="Body.TLabel").pack(side="left", padx=10)

    # ─────────────────────  helpers & callbacks  ───────────────────
    def _extract_date_of_birth(self, result: GUISearchResult) -> str:
        """Extract date of birth from detailed results or return 'Unknown'"""
        if not result.detailed_results:
            return "Unknown"
        
        for match in result.detailed_results:
            dob = match.get('date_of_birth', match.get('birth_date', ''))
            if dob and dob != 'Unknown' and dob.strip():
                return dob.strip()
        
        return "Unknown"
    
    def _extract_location(self, result: GUISearchResult) -> str:
        """Extract location information from detailed results"""
        if not result.detailed_results:
            return "Unknown"
        
        # Look for the new 'location' field that we added to the CLI
        for match in result.detailed_results:
            location = match.get('location', '').strip()
            if location and location.upper() not in ['UNKNOWN', 'N/A', '']:
                return location
        
        return "Unknown"
    
    def _format_match_summary(self, result: GUISearchResult) -> str:
        """Format match summary as '2 Exact, 0 Partial of 5 Total'"""
        exact = result.exact_matches
        partial = result.partial_matches
        total = result.matches_found
        
        if total == 0:
            return "No matches"
        elif total == 1:
            if exact == 1:
                return "1 Exact match"
            elif partial == 1:
                return "1 Partial match"
        else:
            return f"{exact} Exact, {partial} Partial of {total}"
    
    def _format_status_icon(self, result: GUISearchResult) -> str:
        """Format status with appropriate icon"""
        if result.error or result.status == 'Error':
            return "⚠️ Error"
        elif result.matches_found > 0:
            return "✅ Found"
        else:
            return "❌ None"

    def _update_status_display(self):
        """Update the combined status display at the bottom"""
        status = self.var_status.get()
        results_count = len(self.results) if hasattr(self, 'results') else 0
        self.var_combined_status.set(f"{status} - Results: {results_count}")
        # Also update the separate results var for compatibility
        self.var_results.set(f"Results: {results_count}")

    def _add_name(self):
        name = self.e_name.get().strip()
        year = self.e_year.get().strip()
        if not name:
            messagebox.showwarning("Input", "Enter a name")
            return
        entry = f"{name},{year}" if year else name
        txt = self.t_batch.get("1.0", "end").strip()
        self.t_batch.insert("end", ("\n" if txt else "") + entry)
        self.e_name.delete(0, "end")
        self.e_year.delete(0, "end")

    def _load_test(self):
        self.t_batch.delete("1.0", "end")
        self.t_batch.insert("end", "Andro Cutuk,1975\nAnthony Bek,1993\nGhafoor Jaggi Nadery,1978")

    def _clear_batch(self):
        self.t_batch.delete("1.0", "end")

    def _load_file(self):
        fp = filedialog.askopenfilename(filetypes=[("Text", "*.txt"), ("CSV", "*.csv"), ("JSON", "*.json"), ("All", "*.*")])
        if not fp:
            return
        try:
            content = Path(fp).read_text(encoding="utf-8")
            self.t_batch.delete("1.0", "end")
            self.t_batch.insert("end", content)
        except Exception as e:
            messagebox.showerror("Load error", str(e))

    # ---------------- search logic ----------------
    def _batch_search(self):
        raw = self.t_batch.get("1.0", "end").strip()
        if not raw:
            messagebox.showerror("Error", "Batch input empty")
            return
        records = []
        for line in raw.replace(";", "\n").split("\n"):
            line = line.strip()
            if not line:
                continue
            if "," in line:
                n, y = line.split(",", 1)
                try:
                    records.append(SearchRecord(name=n.strip(), birth_year=int(y.strip())))
                except ValueError:
                    records.append(SearchRecord(name=line))
            else:
                records.append(SearchRecord(name=line))
        for r in records:
            r.exact_matching = self.exact_var.get()
        self._run_search(records)

    def _run_search(self, recs: List[SearchRecord]):
        def worker():
            total = len(recs)
            res: List[GUISearchResult] = []
            for idx, rec in enumerate(recs, 1):
                self.root.after(0, lambda i=idx, n=rec.name: self._update_prog(i, total, f"Searching {n}…"))
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    r = loop.run_until_complete(self.cli.search_person(rec))
                    loop.close()
                except Exception as e:
                    r = type("obj", (), {
                        "name": rec.name,
                        "status": "Error",
                        "search_duration": 0.0,
                        "matches_found": 0,
                        "exact_matches": 0,
                        "partial_matches": 0,
                        "match_category": "ERROR",
                        "match_reasoning": str(e),
                        "detailed_results": [],
                        "error": str(e),
                    })()
                res.append(GUISearchResult(
                    name=r.name,
                    status=r.status,
                    search_duration=r.search_duration,
                    matches_found=r.matches_found,
                    exact_matches=r.exact_matches,
                    partial_matches=r.partial_matches,
                    match_category=r.match_category,
                    match_reasoning=r.match_reasoning,
                    detailed_results=r.detailed_results,
                    timestamp=datetime.now().isoformat(),
                    birth_year=rec.birth_year,
                    error=getattr(r, "error", None),
                    total_results_found=r.matches_found,
                ))
            self.root.after(0, lambda: self._add_results(res))
        threading.Thread(target=worker, daemon=True).start()

    def _update_prog(self, i: int, total: int, msg: str):
        self.var_prog.set(i / total * 100)
        self.var_status.set(msg)
        self._update_status_display()
        self.root.update_idletasks()

    def _add_results(self, new: List[GUISearchResult]):
        self.results.extend(new)
        for r in new:
            match_summary = self._format_match_summary(r)
            date_of_birth = self._extract_date_of_birth(r)
            status_icon = self._format_status_icon(r)
            
            self.tv.insert("", "end", values=(
                r.name, 
                match_summary, 
                f"{r.search_duration:.2f}s", 
                date_of_birth, 
                status_icon
            ))
        self._refresh_detail()
        self.var_status.set("Finished")
        self._update_status_display()
        self.var_prog.set(0)

    def _refresh_detail(self):
        self.t_detail.delete("1.0", "end")
        if not self.results:
            self.t_detail.insert("end", "No search results available.\n")
            return
        
        # Summary statistics
        total_searches = len(self.results)
        matches = [r for r in self.results if r.matches_found > 0]
        no_matches = [r for r in self.results if r.matches_found == 0 and r.status != 'Error']
        errors = [r for r in self.results if r.status == 'Error']
        avg_duration = sum(r.search_duration for r in self.results) / total_searches if total_searches > 0 else 0
        
        self.t_detail.insert("end", "READYSEARCH GUI - SESSION SUMMARY\n")
        self.t_detail.insert("end", "=" * 50 + "\n\n")
        
        self.t_detail.insert("end", f"Total Searches: {total_searches}\n")
        self.t_detail.insert("end", f"Found Matches: {len(matches)}\n")
        self.t_detail.insert("end", f"No Matches: {len(no_matches)}\n")
        self.t_detail.insert("end", f"Errors: {len(errors)}\n")
        self.t_detail.insert("end", f"Success Rate: {((len(matches) + len(no_matches))/total_searches*100):.1f}%\n")
        self.t_detail.insert("end", f"Average Duration: {avg_duration:.2f}s\n\n")
        
        # Detailed results
        self.t_detail.insert("end", "DETAILED RESULTS:\n")
        self.t_detail.insert("end", "-" * 30 + "\n\n")
        
        for i, result in enumerate(self.results, 1):
            status_icon = "✅" if result.matches_found > 0 else "⭕" if result.status != 'Error' else "❌"
            
            self.t_detail.insert("end", f"{i}. {status_icon} {result.name}\n")
            self.t_detail.insert("end", f"   Status: {result.status}\n")
            self.t_detail.insert("end", f"   Duration: {result.search_duration:.2f}s\n")
            self.t_detail.insert("end", f"   Matches: {result.matches_found}\n")
            self.t_detail.insert("end", f"   Category: {result.match_category}\n")
            
            if result.birth_year:
                self.t_detail.insert("end", f"   Birth Year: {result.birth_year}\n")
            
            if result.detailed_results:
                self.t_detail.insert("end", "   Detailed Matches:\n")
                for j, match in enumerate(result.detailed_results, 1):
                    matched_name = match.get('matched_name', 'Unknown')
                    match_type = match.get('match_type', 'Unknown')
                    birth_date = match.get('date_of_birth', match.get('birth_date', 'Unknown'))
                    location = match.get('location', '').strip()
                    
                    self.t_detail.insert("end", f"     {j}. {matched_name} ({match_type})\n")
                    if birth_date != 'Unknown':
                        self.t_detail.insert("end", f"        Date of Birth: {birth_date}\n")
                    if location:
                        self.t_detail.insert("end", f"        Location: {location}\n")
                    self.t_detail.insert("end", "\n")
            
            if result.error:
                self.t_detail.insert("end", f"   Error: {result.error}\n")
            
            self.t_detail.insert("end", "\n")

    # --- START OF CHANGES: Added missing _clear_results method to fix AttributeError ---
    def _clear_results(self):
        if not self.results:
            messagebox.showinfo("No Data", "No results to clear.")
            return
        
        if messagebox.askyesno("Clear Results", "Are you sure you want to clear all results?"):
            self.results.clear()
            
            for item in self.tv.get_children():
                self.tv.delete(item)
            
            self.t_detail.delete("1.0", tk.END)
            self.t_detail.insert("end", "No search results available.\n")
            
            # Update status to show cleared results
            self.var_status.set("Ready")
            self._update_status_display()
            messagebox.showinfo("Cleared", "All results have been cleared successfully.")
    # --- END OF CHANGES ---

    def export_results(self, format_type: str):
        """Export results in specified format with enhanced error handling"""
        if not self.results:
            messagebox.showwarning("No Data", "No search results to export.")
            return
        
        # Get filename from user with better default path
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"readysearch_results_{timestamp}"
        
        # Set initial directory to Export subdirectory in program root
        try:
            program_root = Path(__file__).parent
            export_dir = program_root / "Export"
            export_dir.mkdir(exist_ok=True)  # Create if it doesn't exist
            initial_dir = str(export_dir)
        except Exception:
            # Fallback to Desktop/Documents if Export creation fails
            try:
                initial_dir = str(Path.home() / "Desktop")
                if not Path(initial_dir).exists():
                    initial_dir = str(Path.home() / "Documents")
            except:
                initial_dir = str(Path.cwd())
        
        try:
            if format_type == 'json':
                filename = filedialog.asksaveasfilename(
                    parent=self.root,
                    title="Export Results as JSON",
                    defaultextension=".json",
                    filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                    initialfile=f"{default_filename}.json",
                    initialdir=initial_dir
                )
            elif format_type == 'csv':
                filename = filedialog.asksaveasfilename(
                    parent=self.root,
                    title="Export Results as CSV",
                    defaultextension=".csv",
                    filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                    initialfile=f"{default_filename}.csv",
                    initialdir=initial_dir
                )
            elif format_type == 'txt':
                filename = filedialog.asksaveasfilename(
                    parent=self.root,
                    title="Export Results as TXT",
                    defaultextension=".txt",
                    filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                    initialfile=f"{default_filename}.txt",
                    initialdir=initial_dir
                )
            else:
                messagebox.showerror("Error", f"Unsupported format: {format_type}")
                return
            
            if not filename:
                return
            
            # Update status
            self.var_status.set(f"📤 Exporting {format_type.upper()} file...")
            self._update_status_display()
            self.root.update()
            
            if format_type == 'json':
                self._export_json(filename)
            elif format_type == 'csv':
                self._export_csv(filename)
            elif format_type == 'txt':
                self._export_txt(filename)
            
            # Verify file was created and show enhanced completion message
            if Path(filename).exists():
                file_size = Path(filename).stat().st_size
                self.var_status.set(f"✅ Export complete - {file_size:,} bytes written")
                self._update_status_display()
                
                messagebox.showinfo("Export Complete", 
                    f"Results exported successfully!\n\n"
                    f"File: {Path(filename).name}\n"
                    f"Location: {Path(filename).parent}\n"
                    f"Size: {file_size:,} bytes\n"
                    f"Records: {len(self.results)}")
                
                # Ask if user wants to open the file
                if messagebox.askyesno("Open File", "Would you like to open the exported file?"):
                    try:
                        import os
                        os.startfile(filename)  # Windows-specific
                    except:
                        import webbrowser
                        webbrowser.open(f"file://{filename}")
            else:
                messagebox.showerror("Export Error", "File was not created successfully.")
                
        except PermissionError as e:
            messagebox.showerror("Permission Error", 
                f"Cannot write to the selected location.\n"
                f"Please choose a different folder or run as administrator.\n\n"
                f"Error: {str(e)}")
            self.var_status.set("❌ Export failed - Permission denied")
            self._update_status_display()
        except Exception as e:
            messagebox.showerror("Export Error", 
                f"Failed to export results:\n\n"
                f"Error: {str(e)}\n"
                f"File: {filename if 'filename' in locals() else 'Unknown'}")
            self.var_status.set("❌ Export failed")
            self._update_status_display()

    def _export_json(self, filename: str):
        """Export comprehensive results as JSON with detailed match information"""
        total_searches = len(self.results)
        total_matches = sum(r.matches_found for r in self.results)
        exact_matches = sum(r.exact_matches for r in self.results)
        partial_matches = sum(r.partial_matches for r in self.results)
        successful_searches = len([r for r in self.results if r.status != 'Error'])
        
        data = {
            'export_info': {
                'timestamp': datetime.now().isoformat(),
                'tool_version': 'ReadySearch Advanced GUI v2.3 Ultimate',
                'export_type': 'Comprehensive Search Results with Location Data',
                'total_searches': total_searches,
                'successful_searches': successful_searches,
                'total_matches_found': total_matches,
                'exact_matches_total': exact_matches,
                'partial_matches_total': partial_matches,
                'success_rate': f"{(successful_searches/total_searches*100):.1f}%" if total_searches > 0 else "0%"
            },
            'comprehensive_results': []
        }
        
        for r in self.results:
            result_data = {
                'search_info': {
                    'name': r.name,
                    'birth_year': r.birth_year,
                    'search_timestamp': r.timestamp,
                    'search_duration_seconds': r.search_duration
                },
                'match_summary': {
                    'status': r.status,
                    'total_results_found': r.matches_found,
                    'exact_matches': r.exact_matches,
                    'partial_matches': r.partial_matches,
                    'match_category': r.match_category,
                    'match_reasoning': r.match_reasoning,
                    'has_location_data': any('location' in str(match).lower() or 'address' in str(match).lower() 
                                           for match in r.detailed_results) if r.detailed_results else False
                },
                'detailed_matches': [],
                'error_info': r.error if r.error else None
            }
            
            if r.detailed_results:
                for i, match in enumerate(r.detailed_results, 1):
                    detailed_match = {
                        'match_number': i,
                        'matched_name': match.get('matched_name', 'Unknown'),
                        'match_type': match.get('match_type', 'Unknown'),
                        'confidence': match.get('confidence', 0.0),
                        'date_of_birth': match.get('date_of_birth', 'Unknown'),
                        'additional_details': {}
                    }
                    
                    location_fields = ['address', 'location', 'city', 'state', 'postcode', 'suburb', 'street']
                    for field in location_fields:
                        if field in match:
                            detailed_match['additional_details'][field] = match[field]
                    
                    for key, value in match.items():
                        if key not in ['matched_name', 'match_type', 'confidence', 'date_of_birth'] and key not in location_fields:
                            detailed_match['additional_details'][key] = value
                    
                    result_data['detailed_matches'].append(detailed_match)
            
            data['comprehensive_results'].append(result_data)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

    def _export_csv(self, filename: str):
        """Export comprehensive results as CSV with detailed match information and location data"""
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            writer.writerow([
                'Search_Name', 'Birth_Year', 'Status', 'Search_Duration_Seconds', 
                'Total_Results_Found', 'Exact_Matches', 'Partial_Matches', 
                'Match_Category', 'Match_Reasoning', 'Search_Timestamp',
                'Match_Number', 'Matched_Name', 'Match_Type', 'Match_Confidence',
                'Date_of_Birth', 'Location_Address', 'Location_City', 'Location_State',
                'Location_Postcode', 'Additional_Details', 'Error_Info'
            ])
            
            for result in self.results:
                if result.detailed_results:
                    for i, match in enumerate(result.detailed_results, 1):
                        location_address = match.get('address', match.get('location', ''))
                        location_city = match.get('city', match.get('suburb', ''))
                        location_state = match.get('state', '')
                        location_postcode = match.get('postcode', '')
                        
                        additional_details = {}
                        excluded_fields = {'matched_name', 'match_type', 'confidence', 'date_of_birth', 
                                         'address', 'location', 'city', 'suburb', 'state', 'postcode'}
                        
                        for key, value in match.items():
                            if key not in excluded_fields:
                                additional_details[key] = value
                        
                        additional_details_str = '; '.join([f"{k}: {v}" for k, v in additional_details.items()]) if additional_details else ''
                        
                        writer.writerow([
                            result.name,
                            result.birth_year or '',
                            result.status,
                            result.search_duration,
                            result.matches_found,
                            result.exact_matches,
                            result.partial_matches,
                            result.match_category,
                            result.match_reasoning,
                            result.timestamp,
                            i,
                            match.get('matched_name', ''),
                            match.get('match_type', ''),
                            match.get('confidence', ''),
                            match.get('date_of_birth', ''),
                            location_address,
                            location_city,
                            location_state,
                            location_postcode,
                            additional_details_str,
                            result.error or ''
                        ])
                else:
                    writer.writerow([
                        result.name,
                        result.birth_year or '',
                        result.status,
                        result.search_duration,
                        result.matches_found,
                        result.exact_matches,
                        result.partial_matches,
                        result.match_category,
                        result.match_reasoning,
                        result.timestamp,
                        '',  # Match_Number
                        '',  # Matched_Name
                        '',  # Match_Type
                        '',  # Match_Confidence
                        '',  # Date_of_Birth
                        '',  # Location_Address
                        '',  # Location_City
                        '',  # Location_State
                        '',  # Location_Postcode
                        '',  # Additional_Details
                        result.error or ''
                    ])

    def _export_txt(self, filename: str):
        """Export comprehensive results as formatted text with detailed match information and location data"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("READYSEARCH ADVANCED GUI v2.3 - COMPREHENSIVE SEARCH RESULTS REPORT\n")
            f.write("=" * 80 + "\n\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Tool Version: ReadySearch Advanced GUI v2.3 Ultimate\n")
            f.write(f"Report Type: Comprehensive Results with Location Data\n\n")
            
            total_searches = len(self.results)
            total_matches = sum(r.matches_found for r in self.results)
            exact_matches = sum(r.exact_matches for r in self.results)
            partial_matches = sum(r.partial_matches for r in self.results)
            successful_searches = len([r for r in self.results if r.status != 'Error'])
            
            f.write("📊 SUMMARY STATISTICS\n")
            f.write("-" * 30 + "\n")
            f.write(f"Total Searches Performed: {total_searches}\n")
            f.write(f"Successful Searches: {successful_searches}\n")
            f.write(f"Total Matches Found: {total_matches}\n")
            f.write(f"Exact Matches: {exact_matches}\n")
            f.write(f"Partial Matches: {partial_matches}\n")
            f.write(f"Success Rate: {(successful_searches/total_searches*100):.1f}%\n" if total_searches > 0 else "Success Rate: 0%\n")
            f.write("\n")
            
            f.write("🔍 DETAILED SEARCH RESULTS\n")
            f.write("=" * 50 + "\n\n")
            
            for i, result in enumerate(self.results, 1):
                f.write(f"{i}. 👤 {result.name}\n")
                f.write("=" * 60 + "\n")
                
                f.write("📋 SEARCH INFORMATION:\n")
                f.write(f"   Name Searched: {result.name}\n")
                if result.birth_year:
                    f.write(f"   Birth Year: {result.birth_year}\n")
                f.write(f"   Search Duration: {result.search_duration:.2f} seconds\n")
                f.write(f"   Search Timestamp: {result.timestamp}\n")
                f.write("\n")
                
                f.write("📊 MATCH SUMMARY:\n")
                f.write(f"   Status: {result.status}\n")
                f.write(f"   Total Results Found: {result.matches_found}\n")
                f.write(f"   Exact Matches: {result.exact_matches}\n")
                f.write(f"   Partial Matches: {result.partial_matches}\n")
                f.write(f"   Match Category: {result.match_category}\n")
                f.write(f"   Match Reasoning: {result.match_reasoning}\n")
                f.write("\n")
                
                if result.detailed_results:
                    f.write("🗂️ DETAILED MATCH INFORMATION:\n")
                    for j, match in enumerate(result.detailed_results, 1):
                        f.write(f"   Match #{j}:\n")
                        f.write(f"      Name: {match.get('matched_name', 'Unknown')}\n")
                        f.write(f"      Match Type: {match.get('match_type', 'Unknown')}\n")
                        f.write(f"      Confidence: {match.get('confidence', 'Unknown')}\n")
                        f.write(f"      Date of Birth: {match.get('date_of_birth', 'Unknown')}\n")
                        
                        location_data = []
                        location_fields = ['address', 'location', 'city', 'suburb', 'state', 'postcode']
                        for field in location_fields:
                            if field in match and match[field]:
                                location_data.append(f"{field.title()}: {match[field]}")
                        
                        if location_data:
                            f.write("      📍 Location Information:\n")
                            for location_item in location_data:
                                f.write(f"         {location_item}\n")
                        
                        additional_details = {}
                        excluded_fields = {'matched_name', 'match_type', 'confidence', 'date_of_birth'} | set(location_fields)
                        for key, value in match.items():
                            if key not in excluded_fields:
                                additional_details[key] = value
                        
                        if additional_details:
                            f.write("      ℹ️ Additional Details:\n")
                            for key, value in additional_details.items():
                                f.write(f"         {key.replace('_', ' ').title()}: {value}\n")
                        
                        f.write("\n")
                else:
                    f.write("   No detailed match information available.\n\n")
                
                if result.error:
                    f.write(f"❌ ERROR INFORMATION:\n")
                    f.write(f"   Error: {result.error}\n\n")
                
                f.write("-" * 60 + "\n\n")
            
            f.write("📄 END OF REPORT\n")
            f.write("=" * 80 + "\n")
            f.write(f"Report generated by ReadySearch Advanced GUI v2.3 Ultimate\n")
            f.write(f"Timestamp: {datetime.now().isoformat()}\n")

    # ─────────────────────  public api  ────────────────────────────
    def run(self):
        self.root.mainloop()


if __name__ == "__main__":
    ReadySearchGUI().run()