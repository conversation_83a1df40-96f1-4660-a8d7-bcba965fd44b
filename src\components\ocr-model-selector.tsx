"use client"

import { useState, useEffect, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { RefreshCw, Bot, AlertCircle, CheckCircle, ExternalLink } from 'lucide-react'
import { cn } from '@/lib/utils'

interface VisionModel {
  id: string
  name: string
  provider: string
  cost: string
  description: string
  tier: 'free' | 'paid'
  contextWindow: number
  maxOutputTokens: number
  isRecommended?: boolean
}

interface OCRModelSelectorProps {
  selectedModel: string
  onModelChange: (modelId: string) => void
  onConfigChange?: (config: any) => void
  className?: string
}

export default function OCRModelSelector({
  selectedModel,
  onModelChange,
  onConfigChange,
  className
}: OCRModelSelectorProps) {
  const [openRouterConfig, setOpenRouterConfig] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [connectionStatus, setConnectionStatus] = useState<'unknown' | 'connected' | 'failed'>('unknown')
  const [isTestingConnection, setIsTestingConnection] = useState(false)

  // Popular vision models available on OpenRouter
  const visionModels: VisionModel[] = [
    // Free models
    {
      id: 'google/gemini-flash-1.5',
      name: 'Gemini Flash 1.5',
      provider: 'Google',
      cost: 'Free',
      description: 'Fast and efficient vision model for document analysis',
      tier: 'free',
      contextWindow: 1000000,
      maxOutputTokens: 8192,
      isRecommended: true
    },
    {
      id: 'qwen/qwen-2-vl-7b-instruct',
      name: 'Qwen2-VL 7B',
      provider: 'Qwen',
      cost: 'Free',
      description: 'Open-source vision-language model',
      tier: 'free',
      contextWindow: 32768,
      maxOutputTokens: 8192
    },
    {
      id: 'meta-llama/llama-3.2-11b-vision-instruct',
      name: 'Llama 3.2 11B Vision',
      provider: 'Meta',
      cost: 'Free',
      description: 'Meta\'s vision-capable language model',
      tier: 'free',
      contextWindow: 131072,
      maxOutputTokens: 2048
    },
    {
      id: 'microsoft/phi-3.5-vision-instruct',
      name: 'Phi-3.5 Vision',
      provider: 'Microsoft',
      cost: 'Free',
      description: 'Microsoft\'s efficient vision model',
      tier: 'free',
      contextWindow: 131072,
      maxOutputTokens: 4096
    },
    // Paid models
    {
      id: 'openai/gpt-4o',
      name: 'GPT-4o',
      provider: 'OpenAI',
      cost: '$15/1M tokens',
      description: 'Most capable multimodal model for complex document analysis',
      tier: 'paid',
      contextWindow: 128000,
      maxOutputTokens: 4096,
      isRecommended: true
    },
    {
      id: 'openai/gpt-4o-mini',
      name: 'GPT-4o Mini',
      provider: 'OpenAI',
      cost: '$0.15/1M tokens',
      description: 'Affordable multimodal model with good performance',
      tier: 'paid',
      contextWindow: 128000,
      maxOutputTokens: 16384
    },
    {
      id: 'anthropic/claude-3.5-sonnet',
      name: 'Claude 3.5 Sonnet',
      provider: 'Anthropic',
      cost: '$3/1M tokens',
      description: 'Excellent vision and reasoning capabilities',
      tier: 'paid',
      contextWindow: 200000,
      maxOutputTokens: 4096
    },
    {
      id: 'google/gemini-pro-1.5',
      name: 'Gemini Pro 1.5',
      provider: 'Google',
      cost: '$7/1M tokens',
      description: 'Large context window for complex documents',
      tier: 'paid',
      contextWindow: 2000000,
      maxOutputTokens: 8192
    },
    {
      id: 'anthropic/claude-3-5-haiku',
      name: 'Claude 3.5 Haiku',
      provider: 'Anthropic',
      cost: '$0.25/1M tokens',
      description: 'Fast and efficient vision model for OCR tasks',
      tier: 'paid',
      contextWindow: 200000,
      maxOutputTokens: 4096
    }
  ]

  const testConnection = useCallback(async (config: any) => {
    console.log('Testing connection with config:', config);
    setIsTestingConnection(true)
    setConnectionStatus('unknown')
    
    try {
      const response = await fetch('/api/settings/openrouter/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          apiKey: config.apiKey,
          model: config.selectedModel || selectedModel,
          baseUrl: config.baseUrl
        })
      })

      const result = await response.json();
      console.log('Test connection response:', result);

      if (response.ok && result.success) {
        setConnectionStatus('connected')
      } else {
        console.error('Connection test failed:', result);
        setConnectionStatus('failed')
      }
    } catch (error) {
      console.error('Connection test error:', error);
      setConnectionStatus('failed')
    } finally {
      setIsTestingConnection(false)
    }
  }, [selectedModel])

  const loadOpenRouterConfig = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/settings/openrouter')
      if (response.ok) {
        const config = await response.json()
        setOpenRouterConfig(config)
        
        // Test connection if API key is configured
        if (config.apiKey) {
          console.log('Auto-testing connection on load');
          await testConnection(config)
        } else {
          console.log('No API key found, skipping connection test');
        }
      } else {
        setError('Failed to load OpenRouter configuration')
      }
    } catch (error) {
      setError('Failed to connect to backend')
      console.error('Error loading OpenRouter config:', error)
    } finally {
      setIsLoading(false)
    }
  }, [testConnection])

  useEffect(() => {
    loadOpenRouterConfig()
  }, [loadOpenRouterConfig])

  const getModelsByTier = (tier: 'free' | 'paid') => {
    return visionModels.filter(model => model.tier === tier)
  }

  const getSelectedModel = () => {
    return visionModels.find(model => model.id === selectedModel)
  }

  const formatContextWindow = (tokens: number) => {
    if (tokens >= 1000000) {
      return `${(tokens / 1000000).toFixed(1)}M`
    } else if (tokens >= 1000) {
      return `${(tokens / 1000).toFixed(0)}K`
    }
    return tokens.toString()
  }

  const handleModelSelect = async (modelId: string) => {
    onModelChange(modelId)
    onConfigChange?.({ selectedModel: modelId })
    
    // Test connection with new model
    if (openRouterConfig?.apiKey) {
      await testConnection({ ...openRouterConfig, selectedModel: modelId })
    }
  }

  if (isLoading) {
    return (
      <div className={cn("flex items-center justify-center p-8", className)}>
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
          <p className="text-muted-foreground">Loading model configuration...</p>
        </div>
      </div>
    )
  }

  if (!openRouterConfig?.apiKey) {
    return (
      <div className={cn("space-y-4", className)}>
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            OpenRouter API key is required to use AI models. Please configure it in the settings.
          </AlertDescription>
        </Alert>
        
        <div className="text-center p-8">
          <Bot className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="font-medium mb-2">OpenRouter Configuration Required</h3>
          <p className="text-sm text-muted-foreground mb-4">
            You need to configure your OpenRouter API key to use AI vision models for OCR analysis.
          </p>
          <div className="flex gap-2 justify-center">
            <Button variant="outline" onClick={loadOpenRouterConfig}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button asChild>
              <a href="https://openrouter.ai/keys" target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-4 w-4 mr-2" />
                Get API Key
              </a>
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("space-y-6", className)}>
      {error && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Connection Status */}
      <Card className="bg-gray-50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-gray-800 dark:text-gray-200">
            <Bot className="h-5 w-5" />
            OpenRouter Connection
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={cn(
                "h-3 w-3 rounded-full transition-all duration-300",
                connectionStatus === 'connected' 
                  ? 'bg-green-500 shadow-lg shadow-green-500/60 ring-2 ring-green-500/30' 
                  : connectionStatus === 'failed' 
                  ? 'bg-red-500 shadow-lg shadow-red-500/60 ring-2 ring-red-500/30' 
                  : 'bg-gray-400 dark:bg-gray-500'
              )} />
              <div className="flex flex-col">
                <span className={cn(
                  "text-sm font-medium",
                  connectionStatus === 'connected' 
                    ? 'text-green-600 dark:text-green-400' 
                    : connectionStatus === 'failed' 
                    ? 'text-red-600 dark:text-red-400' 
                    : 'text-gray-600 dark:text-gray-400'
                )}>
                  {connectionStatus === 'connected' ? 'Connected' : 
                   connectionStatus === 'failed' ? 'Connection Failed' : 'Not Tested'}
                </span>
                {connectionStatus === 'connected' && (
                  <span className="text-xs text-green-500 dark:text-green-400">API key is valid</span>
                )}
                {connectionStatus === 'failed' && (
                  <span className="text-xs text-red-500 dark:text-red-400">Check API key and model</span>
                )}
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                console.log('Test Connection button clicked with config:', openRouterConfig);
                testConnection(openRouterConfig);
              }}
              disabled={isTestingConnection || !openRouterConfig?.apiKey}
              className="hover:bg-muted/60 transition-colors"
            >
              {isTestingConnection ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <CheckCircle className="h-4 w-4 mr-2" />
              )}
              {isTestingConnection ? 'Testing...' : 'Test Connection'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Model Selection */}
      <div className="space-y-4">
        <h3 className="font-medium text-gray-800 dark:text-gray-200">Select AI Vision Model</h3>
        
        <RadioGroup value={selectedModel} onValueChange={handleModelSelect}>
          {/* Free Models */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300">Free Models</h4>
              <Badge variant="success" className="text-xs">
                No Cost
              </Badge>
            </div>
            
            {getModelsByTier('free').map((model) => (
              <div key={model.id} className="flex items-start space-x-3">
                <RadioGroupItem value={model.id} id={model.id} className="mt-1" />
                <div className="flex-1">
                  <Label htmlFor={model.id} className="cursor-pointer">
                    <Card className={cn(
                      "transition-all hover:shadow-md",
                      selectedModel === model.id && "ring-2 ring-primary"
                    )}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{model.name}</span>
                            <Badge variant="secondary" className="text-xs">
                              {model.provider}
                            </Badge>
                            {model.isRecommended && (
                              <Badge variant="info" className="text-xs">
                                Recommended
                              </Badge>
                            )}
                          </div>
                          <Badge variant="success-outline" className="text-xs">
                            FREE
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{model.description}</p>
                        <div className="flex gap-4 text-xs text-muted-foreground">
                          <span>Context: {formatContextWindow(model.contextWindow)}</span>
                          <span>Max Output: {formatContextWindow(model.maxOutputTokens)}</span>
                        </div>
                      </CardContent>
                    </Card>
                  </Label>
                </div>
              </div>
            ))}
          </div>

          {/* Paid Models */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300">Paid Models</h4>
              <Badge variant="warning" className="text-xs">
                Pay per Use
              </Badge>
            </div>
            
            {getModelsByTier('paid').map((model) => (
              <div key={model.id} className="flex items-start space-x-3">
                <RadioGroupItem value={model.id} id={model.id} className="mt-1" />
                <div className="flex-1">
                  <Label htmlFor={model.id} className="cursor-pointer">
                    <Card className={cn(
                      "transition-all hover:shadow-md",
                      selectedModel === model.id && "ring-2 ring-primary"
                    )}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{model.name}</span>
                            <Badge variant="secondary" className="text-xs">
                              {model.provider}
                            </Badge>
                            {model.isRecommended && (
                              <Badge variant="info" className="text-xs">
                                Recommended
                              </Badge>
                            )}
                          </div>
                          <Badge variant="warning-outline" className="text-xs">
                            {model.cost}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{model.description}</p>
                        <div className="flex gap-4 text-xs text-muted-foreground">
                          <span>Context: {formatContextWindow(model.contextWindow)}</span>
                          <span>Max Output: {formatContextWindow(model.maxOutputTokens)}</span>
                        </div>
                      </CardContent>
                    </Card>
                  </Label>
                </div>
              </div>
            ))}
          </div>
        </RadioGroup>
      </div>

      {/* Selected Model Summary */}
      {selectedModel && (
        <Card className="bg-muted/50">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Selected Model</CardTitle>
          </CardHeader>
          <CardContent>
            {(() => {
              const model = getSelectedModel()
              if (!model) return null
              
              return (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{model.name}</span>
                    <Badge variant="secondary">{model.provider}</Badge>
                    <Badge variant={model.tier === 'free' ? 'default' : 'outline'}>
                      {model.tier === 'free' ? 'FREE' : model.cost}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">{model.description}</p>
                  <div className="flex gap-4 text-xs text-muted-foreground">
                    <span>Context: {formatContextWindow(model.contextWindow)} tokens</span>
                    <span>Max Output: {formatContextWindow(model.maxOutputTokens)} tokens</span>
                  </div>
                </div>
              )
            })()}
          </CardContent>
        </Card>
      )}
    </div>
  )
}